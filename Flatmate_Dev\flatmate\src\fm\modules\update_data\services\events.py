#!/usr/bin/env python3
"""
Event service for Update Data module.

Provides centralized event publishing for file processing and related activities.
"""

from enum import Enum, auto
from typing import Any, Dict, List, Optional

from fm.core.services.event_bus import global_event_bus


class UpdateDataEvents(Enum):
    """Enumeration of specific events for Update Data module."""

    # File Processing Events
    FILE_PROCESSING_STARTED = auto()
    FILE_PROCESSING_COMPLETED = auto()
    FILE_PROCESSING_STATS = auto()
    UNRECOGNIZED_FILES_DETECTED = auto()

    # Additional events from root events.py
    FILE_BACKUP_COMPLETE = auto()
    MASTER_SAVE_COMPLETE = auto()
    PROCESSING_ERROR = auto()
    FORMAT_DETECTED = auto()
    FORMAT_UNKNOWN = auto()


class UpdateDataEventService:
    """Service for publishing events related to Update Data module.

    This service provides a centralized way to publish events throughout the Update Data module.
    It uses the global event bus for communication between components.
    """

    # Helper methods for creating event data
    @staticmethod
    def create_unrecognized_data(files, target_dir):
        """Create data for unrecognized file event."""
        return {"files": [str(f) for f in files], "target_dir": str(target_dir)}

    @staticmethod
    def create_backup_data(files, backup_dir):
        """Create data for backup complete event."""
        return {"files": [str(f) for f in files], "backup_dir": str(backup_dir)}

    @staticmethod
    def create_master_data(file_path, show_in_explorer=False):
        """Create data for master save event."""
        return {"file_path": str(file_path), "show_in_explorer": show_in_explorer}

    @staticmethod
    def create_error_data(error_type, message, details=None):
        """Create data for error event."""
        return {"error_type": error_type, "message": message, "details": details or {}}

    @classmethod
    def publish_processing_started(cls, job_sheet: Dict[str, Any]):
        """
        Publish an event indicating file processing has started.

        Args:
            job_sheet: Dictionary containing job details
        """
        global_event_bus.publish(
            UpdateDataEvents.FILE_PROCESSING_STARTED.name, job_sheet
        )

    @classmethod
    def publish_processing_stats(cls, stats: Dict[str, Any]):
        """
        Publish processing statistics.

        Args:
            stats: Dictionary of processing statistics
        """
        global_event_bus.publish(UpdateDataEvents.FILE_PROCESSING_STATS.name, stats)

    @classmethod
    def publish_processing_completed(cls, result: Dict[str, Any]):
        """
        Publish an event when file processing is completed.

        Args:
            result: Dictionary containing processing results
        """
        global_event_bus.publish(
            UpdateDataEvents.FILE_PROCESSING_COMPLETED.name, result
        )

    @classmethod
    def publish_unrecognized_files(cls, unrecognized_files: List[Dict[str, Any]]):
        """
        Publish an event for unrecognized files.

        Args:
            unrecognized_files: List of unrecognized file details
        """
        global_event_bus.publish(
            UpdateDataEvents.UNRECOGNIZED_FILES_DETECTED.name, unrecognized_files
        )

    @classmethod
    def publish_error(
        cls, error_type: str, message: str, details: Optional[Dict[str, Any]] = None
    ):
        """Publish error notifications to subscribers.

        Args:
            error_type: Type of error that occurred
            message: Error message to publish
            details: Additional error details
        """
        global_event_bus.publish(
            UpdateDataEvents.PROCESSING_ERROR.name,
            cls.create_error_data(error_type, message, details),
        )

    @classmethod
    def publish_backup_complete(cls, files, backup_dir):
        """Publish an event when file backup is completed.

        Args:
            files: List of backed up files
            backup_dir: Directory where files were backed up
        """
        global_event_bus.publish(
            UpdateDataEvents.FILE_BACKUP_COMPLETE.name,
            cls.create_backup_data(files, backup_dir),
        )

    @classmethod
    def publish_master_save_complete(cls, file_path, show_in_explorer=False):
        """Publish an event when master file is saved.

        Args:
            file_path: Path to the saved master file
            show_in_explorer: Whether to show the file in explorer
        """
        global_event_bus.publish(
            UpdateDataEvents.MASTER_SAVE_COMPLETE.name,
            cls.create_master_data(file_path, show_in_explorer),
        )

    @classmethod
    def publish_format_detected(cls, format_name: str, file_path: str):
        """Publish an event when a file format is detected.

        Args:
            format_name: Name of the detected format
            file_path: Path to the file
        """
        global_event_bus.publish(
            UpdateDataEvents.FORMAT_DETECTED.name,
            {"format": format_name, "file": str(file_path)},
        )

    @classmethod
    def publish_format_unknown(cls, file_path: str):
        """Publish an event when a file format is unknown.

        Args:
            file_path: Path to the file
        """
        global_event_bus.publish(
            UpdateDataEvents.FORMAT_UNKNOWN.name, {"file": str(file_path)}
        )
