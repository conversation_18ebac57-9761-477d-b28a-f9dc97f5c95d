#!/usr/bin/env python3
"""
Module Generator Tool for FlatMate

This script generates the folder structure and boilerplate code for a new module
in the FlatMate application.

Usage:
    python -m tools.module_generator.create_module new_module_name "New Module Display Name"
"""

import os
import sys
import argparse
from pathlib import Path


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Generate a new FlatMate module")
    parser.add_argument("module_name", help="Snake case name of the module (e.g., 'new_module')")
    parser.add_argument("display_name", help="Human-readable display name (e.g., 'New Module')")
    return parser.parse_args()


def create_directory_structure(module_name, base_path):
    """Create the directory structure for the new module."""
    module_path = base_path / module_name
    
    # Create main directories
    directories = [
        "",  # Module root
        "_view",
        "_view/left_panel",
        "_view/center_panel",
        "_view/components",
        "core",
        "config",
        "services",
    ]
    
    for directory in directories:
        dir_path = module_path / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        # Create __init__.py in each directory
        (dir_path / "__init__.py").touch()
    
    return module_path


def main():
    """Main entry point for the module generator."""
    args = parse_args()
    module_name = args.module_name
    display_name = args.display_name
    
    # Find the project root and modules directory
    current_dir = Path(__file__).parent
    project_root = current_dir.parent.parent
    modules_dir = project_root / "src" / "fm" / "modules"
    
    print(f"Creating new module: {module_name} ({display_name})")
    print(f"Project root: {project_root}")
    print(f"Modules directory: {modules_dir}")
    
    # Create the directory structure
    module_path = create_directory_structure(module_name, modules_dir)
    print(f"Created directory structure at: {module_path}")
    
    # TODO: Generate boilerplate code from templates
    print("TODO: Generate boilerplate code from templates")
    
    # TODO: Update module registry
    print("TODO: Update module registry in ModuleCoordinator")
    
    # TODO: Update MODULE_MAP in modules_and_features.py
    print("TODO: Update MODULE_MAP in modules_and_features.py")
    
    print("\nModule structure created successfully!")
    print("Note: This is a placeholder implementation. Full code generation will be implemented in the future.")


if __name__ == "__main__":
    main()