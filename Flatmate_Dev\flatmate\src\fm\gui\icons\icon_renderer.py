"""
Icon rendering utilities for the application.
Provides consistent SVG rendering with color control across all components.
"""

from pathlib import Path
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QIcon, QColor, QPixmap, QPainter
from PySide6.QtSvg import QSvgRenderer


class IconRenderer:
    """
    Utility class for rendering SVG icons with consistent coloring.
    Provides a clean interface for icon loading and coloring across the application.
    """
    
    @staticmethod
    def create_colored_icon(icon_path, size=QSize(32, 32), color=QColor(255, 255, 255)):
        """
        Create a colored icon from an SVG file with explicit color control.
        
        Args:
            icon_path: Path to the SVG icon file (str or Path)
            size: Size of the icon (QSize)
            color: Color to apply to the icon (QColor)
            
        Returns:
            QIcon: Colored icon
        """
        try:
            # Convert path to string if it's a Path object
            path_str = str(icon_path)
            
            # Create SVG renderer
            renderer = QSvgRenderer(path_str)
            if not renderer.isValid():
                return QIcon(path_str)  # Fallback to standard loading
                
            # Create transparent pixmap
            pixmap = QPixmap(size)
            pixmap.fill(Qt.GlobalColor.transparent)
            
            # Render SVG onto pixmap
            painter = QPainter(pixmap)
            renderer.render(painter)
            painter.end()
            
            # Create a colored version using QPainter composition
            colored_pixmap = QPixmap(pixmap.size())
            colored_pixmap.fill(Qt.GlobalColor.transparent)
            
            painter = QPainter(colored_pixmap)
            # Draw the original SVG
            painter.drawPixmap(0, 0, pixmap)
            # Apply color overlay
            painter.setCompositionMode(QPainter.CompositionMode.CompositionMode_SourceIn)
            painter.fillRect(colored_pixmap.rect(), color)
            painter.end()
            
            return QIcon(colored_pixmap)
        except Exception as e:
            print(f"Error creating colored icon: {e}")
            return QIcon(path_str)  # Fallback to standard loading
    
    @staticmethod
    def load_icon(icon_path, size=QSize(32, 32), use_theme_color=True):
        """
        Load an icon with appropriate coloring based on the application theme.
        
        Args:
            icon_path: Path to the SVG icon file
            size: Size of the icon
            use_theme_color: Whether to apply theme coloring
            
        Returns:
            QIcon: Loaded and themed icon
        """
        if not icon_path:
            return None
            
        # Default to white for now - in future this could be retrieved from theme
        icon_color = QColor(255, 255, 255)  # "calm white" for dark mode
        
        if use_theme_color:
            return IconRenderer.create_colored_icon(icon_path, size, icon_color)
        else:
            # Just load the icon without color manipulation
            return QIcon(str(icon_path))
