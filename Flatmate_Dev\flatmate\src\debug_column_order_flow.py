#!/usr/bin/env python3
"""
Debug script to trace the column ordering flow.
"""

def debug_column_order_flow():
    """Debug the complete column ordering flow."""
    print("=== Column Order Flow Debug ===\n")
    
    try:
        from fm.core.data_services.standards.columns import Columns
        
        print("1. Testing get_ordered_display_columns('categorize'):")
        ordered_columns = Columns.get_ordered_display_columns('categorize')
        print(f"   Count: {len(ordered_columns)}")
        for i, col in enumerate(ordered_columns):
            print(f"   {i+1}. {col.display_name:15} (db: {col.db_name:12}, order: {col.order:2d})")
        print()
        
        print("2. Converting to display names (as done in transaction_view_panel):")
        visible_columns = [col.display_name for col in ordered_columns]
        print(f"   Display names: {visible_columns}")
        print()
        
        print("3. Testing get_default_visible_columns('categorize'):")
        default_cols = Columns.get_default_visible_columns('categorize')
        print(f"   Default columns: {default_cols}")
        print()
        
        print("4. Check if there's a mismatch:")
        ordered_db_names = [col.db_name for col in ordered_columns]
        print(f"   Ordered db_names: {ordered_db_names}")
        print(f"   Default db_names: {default_cols}")
        print(f"   Match: {ordered_db_names[:len(default_cols)] == default_cols}")
        print()
        
        print("5. Test ColumnOrderService directly:")
        from fm.core.data_services.standards.column_order_service import ColumnOrderService
        service = ColumnOrderService()
        service_order = service.get_column_order('categorize')
        print(f"   Service order: {service_order}")
        print()
        
        print("6. Check if alphabetical sorting is happening somewhere:")
        alphabetical_display = sorted(visible_columns)
        print(f"   Alphabetical order: {alphabetical_display}")
        print(f"   Current order:      {visible_columns}")
        print(f"   Is alphabetical: {visible_columns == alphabetical_display}")
        print()
        
        # Test what happens when we simulate the table view process
        print("7. Simulate table view column processing:")
        
        # This is what the table view might be doing
        print("   a) Original order:", visible_columns)
        
        # Check if there's any sorting in the table view configuration
        print("   b) If table view sorts by keys:", sorted(visible_columns))
        
        # Check DataFrame column order impact
        import pandas as pd
        sample_data = {col.db_name: [1, 2, 3] for col in ordered_columns}
        df = pd.DataFrame(sample_data)
        print(f"   c) DataFrame columns: {list(df.columns)}")
        
        # Apply display names
        display_mapping = {col.db_name: col.display_name for col in ordered_columns}
        df_renamed = df.rename(columns=display_mapping)
        print(f"   d) After rename: {list(df_renamed.columns)}")
        
        print("\n=== Debug Complete ===")
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_column_order_flow()
