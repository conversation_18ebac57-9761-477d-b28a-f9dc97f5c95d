"""Base class for bank statement type maps."""

import os.path
import re
from abc import ABC

from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import List, Optional, Tuple, Generator, Type, ClassVar

import pandas as pd

from fm.core.data_services.standards.columns import Columns
from fm.core.data_services.standards.column_definition import Column
from fm.core.data_services.date_format_service import DateFormatService
from fm.core.services.logger import log


class AccountNumberSource(Enum):
    """Specifies the source from which an account number was extracted."""
    FROM_METADATA = "metadata"
    FROM_DATA = "data column"
    FROM_FILENAME = "filename"

@dataclass
class StatementType:
    """Configuration for statement format identification."""
    bank_name: str  # e.g., "Kiwibank"
    variant: str    # e.g., "basic"
    file_type: str  # e.g., "csv"
    
    def validate(self):
        if not self.bank_name or not isinstance(self.bank_name, str):
            raise ValueError("bank_name must be a non-empty string")
        if not self.variant or not isinstance(self.variant, str):
            raise ValueError("variant must be a non-empty string")
        if not self.file_type or not isinstance(self.file_type, str):
            raise ValueError("file_type must be a non-empty string")


@dataclass
class ColumnAttributes:
    """Configuration for column mapping and formatting."""
    # Required fields
    has_col_names: bool
    colnames_in_header: bool
    n_source_cols: int
    date_format: str

    data_start_row: int = 0

    col_names_row: int = 0
    has_account_column: bool = False
    
    source_col_names: List[str] = field(default_factory=list)
    target_col_names: List[Column] = field(default_factory=list)
    concat_cols_for_details: Optional[List[Column]] = None
    file_encoding: str = 'utf-8'
    
    def validate(self):
        """Validate column attributes."""

        if not self.date_format or not isinstance(self.date_format, str):
            raise ValueError("date_format must be a non-empty string")
        if not isinstance(self.n_source_cols, int) or self.n_source_cols <= 0:
            raise ValueError("n_source_cols must be a positive integer")
        if not isinstance(self.data_start_row, int) or self.data_start_row < 0:
            raise ValueError("data_start_row must be a non-negative integer")
        if not isinstance(self.col_names_row, int) or self.col_names_row < 0:
            raise ValueError("col_names_row must be a non-negative integer")
        if not isinstance(self.colnames_in_header, bool):
            raise ValueError("colnames_in_header must be a boolean")
        if not isinstance(self.has_col_names, bool):
            raise ValueError("has_col_names must be a boolean")
        if self.has_col_names and not self.source_col_names:
            raise ValueError("source_col_names must be provided when has_col_names is True")
        if self.source_col_names and len(self.source_col_names) != self.n_source_cols:
            raise ValueError(f"source_col_names length ({len(self.source_col_names)}) must match n_source_cols ({self.n_source_cols})")
        if self.target_col_names and len(self.target_col_names) != self.n_source_cols:
            raise ValueError(f"target_col_names length ({len(self.target_col_names)}) must match n_source_cols ({self.n_source_cols})")
        if not isinstance(self.file_encoding, str) or not self.file_encoding:
            raise ValueError("file_encoding must be a non-empty string")
            


@dataclass
class AccountNumberAttributes:
    """Configuration for account number extraction."""
    pattern: str = None  # Required when any location flag is True
    in_data: bool = False
    location: Tuple[int, int] = field(default_factory=lambda: (0, 0))
    in_file_name: bool = False
    in_metadata: bool = False
    required: bool = False  # Whether account number is required for this handler
    
    def validate(self):
        # Check for at least one location flag set
        location_flags = [self.in_data, self.in_file_name, self.in_metadata]
        if not any(location_flags):
            raise ValueError("At least one account location (in_data, in_file_name, in_metadata) must be True")
            
        # If any location flag is set, pattern is required
        if any(location_flags):
            if not self.pattern:
                raise ValueError("pattern is required when any account location is specified")
                
            # Validate location coordinates
            if not isinstance(self.location, tuple) or len(self.location) != 2:
                raise ValueError("location must be a tuple of two integers")
                
            row, col = self.location
            if not isinstance(row, int) or not isinstance(col, int) or row < 0 or col < 0:
                raise ValueError("location coordinates must be non-negative integers")


@dataclass
class SourceMetadataAttributes:
    """Configuration for source metadata handling."""
    has_metadata_rows: bool = False
    metadata_start: Tuple[int, int] = field(default_factory=lambda: (0, 0))
    metadata_end: Tuple[int, int] = field(default_factory=lambda: (0, 0))
    
    def validate(self):
        if not isinstance(self.has_metadata_rows, bool):
            raise ValueError("has_metadata_rows must be a boolean")
            
        # Validate coordinates
        for name, coord in [("metadata_start", self.metadata_start), 
                          ("metadata_end", self.metadata_end)]:
            if not isinstance(coord, tuple) or len(coord) != 2:
                raise ValueError(f"{name} must be a tuple of two integers")
            row, col = coord
            if not isinstance(row, int) or not isinstance(col, int) or row < 0 or col < 0:
                raise ValueError(f"{name} coordinates must be non-negative integers")
        
        if self.has_metadata_rows and self.metadata_start > self.metadata_end:
            raise ValueError("metadata_start must not be after metadata_end")

@dataclass
class StatementAttributes:
    """Unified configuration for statement handling.
    
    Groups all statement-related attributes into a single class for better organization
    and enforces validation rules across all attributes.
    """
    statement_type_attrs: StatementType = None
    columns_attrs: ColumnAttributes = None
    account_attrs: AccountNumberAttributes = None
    metadata_attrs: SourceMetadataAttributes = None
    
    
    def validate(self):
        """Validate all statement attributes and their relationships."""
        # Validate all components
        self.statement_type_attrs.validate()
        self.columns_attrs.validate()
        self.account_attrs.validate()
        self.metadata_attrs.validate()        
    


class StatementHandler(ABC):
    """Base class for all statement handlers.
    
    Subclasses must define:
    - statement_type: StatementType
    - columns: ColumnAttributes
    - account: AccountNumberAttributes
    - metadata: SourceMetadataAttributes
    
    These will be automatically validated on initialization.
    """
    
    def __init__(self):
        """Initializes the handler and validates its configuration."""
        self.attributes = StatementAttributes(
            statement_type_attrs=self.statement_type,
            columns_attrs=self.columns,
            account_attrs=self.account,
            metadata_attrs=self.metadata,
        )
        self.attributes.validate()
        log.debug(f"Initialized {self.__class__.__name__}", module=self.__class__.__name__)

    # Type hints for statement attributes
    StatementType: ClassVar[Type[StatementType]] = StatementType
    
    # Instance attributes
    attributes: StatementAttributes

    # --- Public API ---

    @classmethod
    def can_handle_file(cls, filepath: str) -> bool:
        """Check if this handler can process a file using an early-exit scoring system.

        This check is designed for efficiency, returning True as soon as the
        confidence score reaches the minimum threshold of 15.

        Scoring:
        - Account pattern found (any source): 10
        - Column names match: 10
        - Number of columns match: 5

        Args:
            filepath: Path to the file to check.

        Returns:
            bool: True if this handler can process the file.
        """
        handler = cls()
        df_preview = handler._read_csv(filepath, nrows=20)
        if df_preview is None or df_preview.empty:
            return False

        # Initialize score and debug info
        score = 0
        debug_info = []
        min_score = 15

        # 1. Check account number (most important check)
        account_number, source = handler._extract_account_number(
            df_preview, filepath=filepath, return_source=True
        )
        if account_number:
            # Higher scores for more reliable sources
            if source == AccountNumberSource.FROM_METADATA:
                account_score = 15  # Most reliable - exact match in metadata
            elif source == AccountNumberSource.FROM_DATA:
                account_score = 15   # Also very reliable - exact match in data
            elif source == AccountNumberSource.FROM_FILENAME:
                account_score = 5    # Less reliable - could be coincidental
            
            score += account_score
            debug_info.append(f"Account={account_score} (source: {source.value})")
            if score >= min_score:
                return True
        else:
            debug_info.append("Account=0")

        # 2. Check column headers if present
        column_score = 0
        if handler.columns.has_col_names:
            try:
                header_row = df_preview.iloc[handler.columns.col_names_row].astype(str)
                expected_headers = set(handler.columns.source_col_names)
                actual_headers = set(header_row.str.strip().tolist())
                if expected_headers.issubset(actual_headers):
                    column_score += 10
                    score += 10
                    if score >= min_score:
                        return True
            except Exception as e:
                log.debug(f"Error checking column headers: {e}")
        
        # 3. Check column count
        n_cols_match = len(df_preview.columns) == handler.columns.n_source_cols
        if n_cols_match:
            column_score += 5
            score += 5
            if score >= min_score:
                return True

        # Log final decision
        debug_info.append(f"Columns={column_score} (n_cols_match={n_cols_match})")
        debug_info.append(f"Total={score}, MinReq={min_score}")
        
        log.debug(
            f"{cls.__name__} scoring for '{Path(filepath).name}': {', '.join(debug_info)}",
            module=cls.__name__
        )

        return score >= min_score

    def process_file(self, filepath: str) -> Optional[pd.DataFrame]:
        """Reads and processes a single statement file.

        This is the main entry point for a handler. It reads the file and applies
        all formatting and standardization steps.

        Args:
            filepath: The path to the statement file.

        Returns:
            A formatted DataFrame or None if the file cannot be processed.
        """
        self._current_filepath = filepath
        log.info(f"Processing '{Path(filepath).name}' with {self.__class__.__name__}")

        df = self._read_file(filepath)
        if df is None or df.empty:
            log.warning(f"Could not read or file is empty: {filepath}")
            return None

        formatted_df = self._format_df(df)
        self._validate_data(formatted_df)

        return formatted_df

    # --- Core Internal Helpers ---

    def _read_csv(self, filepath: str, nrows: Optional[int] = None) -> Optional[pd.DataFrame]:
        """Centralized method to read CSV files as headerless."""
        col_attrs = self.columns
        try:
            df = pd.read_csv(
                filepath,
                header=None,  # Always read without headers
                nrows=nrows,
                #usecols=range(col_attrs.n_source_cols),
                on_bad_lines='skip',
                engine='python',
                encoding=col_attrs.file_encoding,
                sep=','
            )
            return df
        except (FileNotFoundError, pd.errors.EmptyDataError):
            return None  # Return None for non-existent or empty files
        except Exception as e:
            log.error(f"Unexpected error reading {filepath}: {e}")
            return None

    def _read_file(self, filepath: str) -> Optional[pd.DataFrame]:
        """Reads the full statement file into a DataFrame using the centralized _read_csv method.

        NOTE: This method acts as a dispatcher. It can be extended in the future
        to handle different file types (e.g., PDF, XLSX) by checking the file
        extension and calling the appropriate reader (e.g., _read_pdf).
        """
        return self._read_csv(filepath, nrows=None)

    def _extract_account_number(self, df: pd.DataFrame, filepath: str | None = None, return_source: bool = False) -> str | tuple[str, AccountNumberSource | None]:
        """Extract account number using handler configuration.
        
        Returns:
            Tuple of (account_number, source) if return_source is True, else just account_number.
            Returns empty string (and None for source) if no match found.
        """
        if not self.account.pattern:
            return ("", None) if return_source else ""

        # 1. Check metadata if configured
        if self.account.in_metadata:
            try:
                row, col = self.account.location
                value = str(df.iloc[row, col]).strip()
                if match := re.search(self.account.pattern, value):
                    account = match.group(1) if match.groups() else match.group(0)
                    return (account, AccountNumberSource.FROM_METADATA) if return_source else account
            except Exception:
                pass

        # 2. Check data column if configured and has account column
        if self.account.in_data and getattr(self.columns, 'has_account_column', False):
            try:
                col = self.account.location[1]
                for value in df[col].dropna():
                    value = str(value).strip()
                    if match := re.search(self.account.pattern, value):
                        account = match.group(1) if match.groups() else match.group(0)
                        return (account, AccountNumberSource.FROM_DATA) if return_source else account
            except Exception:
                pass

        # 3. Check filename if configured
        if self.account.in_file_name and filepath:
            try:
                if match := re.search(self.account.pattern, Path(filepath).name):
                    return (match.group(0), AccountNumberSource.FROM_FILENAME) if return_source else match.group(0)
            except Exception:
                pass

        return ("", None) if return_source else ""

    def _standardise_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Assigns the standardised target column names to the DataFrame."""
        target_names = [str(col) for col in self.columns.target_col_names]

        df.columns = target_names
        return df

    def _standardise_dates(self, df: pd.DataFrame) -> pd.DataFrame:
        """standardise dates in the DataFrame to ISO format using DateFormatService."""
        date_col_name = str(Columns.DATE)

        if date_col_name not in df.columns:
            return df

        original_dates = df[date_col_name].copy()

        # Use the DateFormatService to standardise the date column.
        # Passing the format_hint makes parsing more reliable and uses the
        # handler-specific date format configuration.
        df[date_col_name] = df[date_col_name].apply(
            DateFormatService.standardize_date,
            format_hint=self.columns.date_format
        )

        # Find rows where parsing failed (new value is NaT, old value was not)
        failed_mask = pd.isna(df[date_col_name]) & original_dates.notna()
        if failed_mask.any():
            failed_count = failed_mask.sum()
            failed_examples = original_dates[failed_mask].unique().tolist()
            log.warning(
                f"{failed_count}/{len(df)} dates in '{self._current_filepath}' could not be parsed.\n"
                f"Examples of failing values: {failed_examples[:5]}",
                module=self.__class__.__name__
            )

        return df

    def _create_details_column(self, df: pd.DataFrame, columns: list[Column]) -> None:
        """Create a details column by concatenating specified standard columns."""
        # Convert enums to their string values for DataFrame operations
        col_names = [str(col) for col in columns]
        existing_cols = [col for col in col_names if col in df.columns]

        if not existing_cols:
            log.warning("No valid columns provided for details creation")
            return

        df[str(Columns.DETAILS)] = df[existing_cols].apply(
            lambda x: ' '.join(str(val) for val in x if pd.notna(val) and str(val).strip()),
            axis=1
        )
        log.info(f"Created details column from: {', '.join(existing_cols)}")

    def _reorder_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Reorder columns based on StandardColumns enum order, if they exist"""
        standard_order = [str(c) for c in Columns.get('statement_handler')]
        ordered_cols = [col for col in standard_order if col in df.columns]
        remaining_cols = [col for col in df.columns if col not in ordered_cols]
        return df.reindex(columns=ordered_cols + remaining_cols)

    def _custom_format(self, df: pd.DataFrame) -> pd.DataFrame:
        """Hook for handlers to perform bank-specific formatting."""
        return df

    # --- Internal Orchestration ---

    def _format_df(self, df: pd.DataFrame) -> pd.DataFrame:
        """standardises the format of the input DataFrame based on handler configuration."""
        account_number = self._extract_account_number(df)

        # --- 1. Slice DataFrame to exclude metadata/header rows ---
        # With headerless reading, we now always slice from data_start_row.
        if self.columns.data_start_row > 0:
            df = df.iloc[self.columns.data_start_row:].reset_index(drop=True)

        # --- 2. standardise column headers ---
        df = self._standardise_columns(df)

        # --- Clean the DataFrame ---
        df.dropna(how='all', inplace=True) # Drop fully empty rows

        # No need for additional header detection - we already have data_start_row and colnames_in_header
        df[str(Columns.SOURCE_FILENAME)] = os.path.basename(self._current_filepath)

        if not self.columns.has_account_column:
            df[str(Columns.ACCOUNT)] = account_number if account_number else ''

        df = self._standardise_dates(df)
        df = self._custom_format(df)

        if str(Columns.EMPTY_COLUMN) in df.columns:
            df = df.drop(columns=[str(Columns.EMPTY_COLUMN)])

        # If the handler has explicitly configured columns for concatenation,
        # run the creation logic using that specific list.
        if self.columns.concat_cols_for_details:
            self._create_details_column(df, self.columns.concat_cols_for_details)
        
        df = self._set_data_types(df)
        df = self._reorder_columns(df)

        return df

    def _set_data_types(self, df: pd.DataFrame) -> pd.DataFrame:
        amount_col = str(Columns.AMOUNT)
        balance_col = str(Columns.BALANCE)

        if amount_col in df.columns:
            # Ensure the column is of string type before trying string operations
            if df[amount_col].dtype == 'object':
                # Remove currency symbols, commas, and whitespace
                df[amount_col] = df[amount_col].str.replace(r'[$,]', '', regex=True).str.strip()

            # Convert to numeric, coercing errors to NaN (which can be caught in validation)
            df[amount_col] = pd.to_numeric(df[amount_col], errors='coerce')

        if balance_col in df.columns:
            if df[balance_col].dtype == 'object':
                df[balance_col] = df[balance_col].str.replace(r'[$,]', '', regex=True).str.strip()
            df[balance_col] = pd.to_numeric(df[balance_col], errors='coerce')

        return df

    def _validate_data(self, df: pd.DataFrame):
        """
        Validates the DataFrame for required columns and null values.

        A valid transaction row must contain non-null values for DATE, DETAILS, AMOUNT,
        and ACCOUNT. Additionally, it must have a valid entry in EITHER the BALANCE
        or the UNIQUE_ID column.

        Raises:
            ValueError: If validation fails.
        """
        # Core columns that must always be present and populated.
        core_required_cols = [
            Columns.DATE,
            Columns.DETAILS,
            Columns.AMOUNT,
            Columns.ACCOUNT,
        ]

        for col_def in core_required_cols:
            col_name = str(col_def)
            if col_name not in df.columns:
                raise ValueError(
                    f"Validation failed for {os.path.basename(self._current_filepath)}: "
                    f"Required column '{col_name}' is missing."
                )
            
            if df[col_name].isnull().any():
                null_rows = df[df[col_name].isnull()].index.tolist()
                raise ValueError(
                    f"Validation failed for {os.path.basename(self._current_filepath)}: "
                    f"Required column '{col_name}' contains null values at rows: {null_rows}"
                )

        # Each row must have either a balance or a unique_id.
        balance_col = str(Columns.BALANCE)
        unique_id_col = str(Columns.UNIQUE_ID)

        has_balance = balance_col in df.columns
        has_unique_id = unique_id_col in df.columns

        if not has_balance and not has_unique_id:
            raise ValueError(
                f"Validation failed for {os.path.basename(self._current_filepath)}: "
                f"DataFrame must contain at least one of '{balance_col}' or '{unique_id_col}'."
            )

        # Check row-level validity
        invalid_rows_mask = pd.Series([False] * len(df), index=df.index)
        if has_balance and has_unique_id:
            invalid_rows_mask = df[balance_col].isnull() & df[unique_id_col].isnull()
        elif has_balance:
            invalid_rows_mask = df[balance_col].isnull()
        elif has_unique_id:
            invalid_rows_mask = df[unique_id_col].isnull()

        if invalid_rows_mask.any():
            invalid_indices = invalid_rows_mask[invalid_rows_mask].index.tolist()
            raise ValueError(
                f"Validation failed for {os.path.basename(self._current_filepath)}: "
                f"Rows {invalid_indices} are missing both '{balance_col}' and '{unique_id_col}'."
            )

    # --- Public API ---

    def process_file(self, filepath: str) -> Optional[pd.DataFrame]:
        """Reads, formats, and validates a statement file."""
        self._current_filepath = filepath
        try:
            df = self._read_file(filepath)
            if df is None or df.empty:
                log.warning(f"File is empty or could not be read: {filepath}", module=self.__class__.__name__)
                return None
                
            formatted_df = self._format_df(df)
            self._validate_data(formatted_df)
            return formatted_df
        except Exception as e:
            context = f"processing file {os.path.basename(filepath)}"
            log.error(
                f"Error in {self.__class__.__name__} while {context}: {e}",
                exc_info=True
            )
            raise

    @staticmethod
    def _read_csv_preview(filepath: str, encoding: str, header_row: int, nrows_to_read: int) -> Optional[pd.DataFrame]:
        """Reads a preview of a CSV file for validation purposes."""
        try:
            return pd.read_csv(
                filepath,
                encoding=encoding,
                header=header_row,
                nrows=nrows_to_read,
                skip_blank_lines=True,
            )
        except (FileNotFoundError, pd.errors.EmptyDataError, IndexError):
            return None
        except Exception as e:
            log.debug(f"Failed to read preview of {filepath}: {e}")
            return None

    @classmethod
    def can_handle_file(cls, filepath: str) -> bool:
        """Check if this handler can process a file using an early-exit scoring system.

        This check is designed for efficiency, returning True as soon as the
        confidence score reaches the minimum threshold of 15.

        Scoring:
        - Account pattern found (any source): 10
        - Column names match: 10
        - Number of columns match: 5

        Args:
            filepath: Path to the file to check.

        Returns:
            bool: True if this handler can process the file.
        """
        handler = cls()
        score = 0
        min_score = 15
        log_msg = f"Handler {cls.__name__} score for '{Path(filepath).name}':"

        try:
            # Read a preview for analysis
            rows_to_read = handler.columns.data_start_row + 5
            df_preview = handler._read_csv(filepath, nrows=rows_to_read)

            if df_preview is None or df_preview.empty:
                log.debug(f"[{cls.__name__}] Could not read preview or file is empty: {filepath}")
                return False

            # 1. Check for account number (high confidence)
            account_number, source = handler._extract_account_number(
                df_preview, filepath=filepath, return_source=True
            )
            if account_number:
                score += 10  # Account match is a strong signal

            # 2. Check column name match
            columns_match = False
            if handler.columns.has_col_names and handler.columns.source_col_names:
                try:
                    header_row = df_preview.iloc[handler.columns.col_names_row].astype(str)
                    expected_headers = set(handler.columns.source_col_names)
                    actual_headers = set(header_row.str.strip().tolist())
                    if expected_headers.issubset(actual_headers):
                        score += 10
                        columns_match = True
                except IndexError:
                    log.debug(f"[{cls.__name__}] Not enough rows for header at row {handler.columns.col_names_row}")
                except Exception as e:
                    log.warning(f"[{cls.__name__}] Error checking columns for {filepath}: {e}")
            
            if score >= min_score:
                log.debug(f"{log_msg} {score} (Account + Columns)")
                return True

            # 3. Check number of columns match
            n_cols_match = len(df_preview.columns) == handler.columns.n_source_cols
            if n_cols_match:
                score += 5

            # Final decision
            log.debug(
                f"{log_msg} {score} (Account: {account_number is not None}, "
                f"Cols: {columns_match}, NumCols: {n_cols_match}) -> {'Pass' if score >= min_score else 'Fail'}"
            )
            
            if score >= min_score:
                return True
            else:
                return False

        except Exception as e:
            log.error(f"Error in {cls.__name__}.can_handle_file for {filepath}: {e}", exc_info=True)
            return False