"""
Shared pane components for the Flatmate application.

This package provides reusable pane components that can be used
within panel containers across different modules.

Panes are sub-components that live within panels and provide
focused functionality with activation/deactivation behavior.
"""

from .dev_settings_pane import DevSettingsPaneCore, DevSettingsPaneWithButton

__all__ = [
    'DevSettingsPaneCore',
    'DevSettingsPaneWithButton'
]
