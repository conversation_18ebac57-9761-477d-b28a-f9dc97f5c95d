"""Presenter for the Categorize module."""

import os
from typing import List, Optional
import pandas as pd
from PySide6.QtWidgets import QFileDialog

from fm.core.services.logger import log
from fm.core.services.cache_service import cache_service
from fm.database_service.z_archive.data_service import DataService
from .._view.cat_view import CatView
from ..core.processor import categorize_files


class CategorizePresenter():
    """Presenter for the Categorize module."""
    
    def __init__(self, parent=None):
        """Initialize the Categorize presenter."""
        super().__init__(parent)
        self.view = CatView()
        self.data_service = DataService()
        
        # Connect signals
        self.view.select_files_clicked.connect(self._on_select_files)
        self.view.transaction_selected.connect(self._on_transaction_selected)
        self.view.tags_updated.connect(self._on_tags_updated)
        
        # Track current transactions
        self._current_transactions_df = None
        self._modified = False
    
    def initialize(self):
        """Initialize the presenter."""
        super().initialize()
        
        # Check for cached transactions
        if cache_service.has('categorize_transactions'):
            self._current_transactions_df = cache_service.get('categorize_transactions')
            self.view.set_transactions(self._current_transactions_df)
    
    def cleanup(self):
        """Clean up resources."""
        # Cache current transactions if modified
        if self._modified and self._current_transactions_df is not None:
            cache_service.put('categorize_transactions', self._current_transactions_df)
        
        super().cleanup()
    
    def _on_select_files(self):
        """Handle select files button click."""
        # Show file dialog
        file_paths, _ = QFileDialog.getOpenFileNames(
            self.view,
            "Select Bank Statement Files",
            "",
            "CSV Files (*.csv);;Excel Files (*.xlsx *.xls);;All Files (*.*)"
        )
        
        if not file_paths:
            return
        
        # Process files
        try:
            transactions_df = categorize_files(file_paths)
            if transactions_df is not None and not transactions_df.empty:
                self._current_transactions_df = transactions_df
                self.view.set_transactions(transactions_df)
                self._modified = False
        except Exception as e:
            self.show_error(f"Error processing files: {str(e)}")
    
    def _on_transaction_selected(self, transaction_id):
        """Handle transaction selection."""
        # This could be used to show details in a separate panel
        pass
    
    def _on_tags_updated(self, transaction_id, tags):
        """Handle tags update."""
        # Update in-memory DataFrame
        if self._current_transactions_df is not None:
            idx = self._current_transactions_df.index[
                self._current_transactions_df['id'] == transaction_id
            ].tolist()
            
            if idx:
                self._current_transactions_df.at[idx[0], 'tags'] = tags
                self._modified = True
                
                # Update database
                self.data_service.update_transaction_tags(transaction_id, tags)
    
    def load_transactions_from_db(self, 
                                start_date=None, 
                                end_date=None, 
                                account_number=None):
        """Load transactions from the database with optional filtering."""
        try:
            transactions_df = self.data_service.get_transactions_as_dataframe(
                start_date=start_date,
                end_date=end_date,
                account_number=account_number
            )
            
            if transactions_df is not None and not transactions_df.empty:
                self._current_transactions_df = transactions_df
                self.view.set_transactions(transactions_df)
                self._modified = False
            else:
                self.show_info("No transactions found matching the criteria.")
        except Exception as e:
            self.show_error(f"Error loading transactions: {str(e)}")