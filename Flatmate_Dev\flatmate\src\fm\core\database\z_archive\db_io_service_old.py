"""
SUPERSEDED: Database I/O Service - High-level API for database operations.

This module has been superseded by the enhanced version in data_services/db_io_service.py
which includes column management integration and improved functionality.

ARCHIVED: This file is kept for reference only.
"""

from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import pandas as pd

from ..sql_repository.sqlite_repository import SQLiteTransactionRepository
from ..sql_repository.transaction_repository import ImportResult, Transaction


class DBIOService:
    """
    SUPERSEDED: High-level service for database I/O operations.
    
    This class has been superseded by the enhanced version in data_services/
    which includes column management integration.
    """
    
    def __init__(self, repo: Optional[SQLiteTransactionRepository] = None):
        """
        Initialize the DB I/O Service.
        
        Args:
            repo: Optional repository instance for dependency injection.
                 If None, a new SQLiteTransactionRepository will be created.
        """
        self.repo = repo or SQLiteTransactionRepository()
    
    # --- Importing --------------------------------------------------
    
    def import_csv(self, file_path: Path, mapping: Optional[Dict[str, str]] = None) -> ImportResult:
        """
        Import transactions from a CSV file.
        
        Args:
            file_path: Path to the CSV file
            mapping: Optional column mapping (source_column -> target_column)
            
        Returns:
            ImportResult with counts of added, duplicate, and error records
        """
        try:
            # Read CSV into DataFrame
            df = pd.read_csv(file_path)
            
            # Apply column mapping if provided
            if mapping:
                df = df.rename(columns=mapping)
            
            # Import from DataFrame
            return self.import_dataframe(df, source_file=str(file_path))
        except Exception as e:
            return ImportResult(
                added_count=0,
                duplicate_count=0,
                error_count=1,
                errors=[f"Error importing CSV: {str(e)}"]
            )
    
    def import_dataframe(self, df: pd.DataFrame, source_file: Optional[str] = None) -> ImportResult:
        """
        Import transactions from a pandas DataFrame.
        
        Args:
            df: DataFrame containing transaction data
            source_file: Optional source file path for reference
            
        Returns:
            ImportResult with counts of added, duplicate, and error records
        """
        if df is None or df.empty:
            return ImportResult(
                added_count=0,
                duplicate_count=0,
                error_count=0,
                errors=["No data to import"]
            )
        
        try:
            # Delegate to repository
            return self.repo.add_transactions_from_df(df, source_file=source_file)
        except Exception as e:
            return ImportResult(
                added_count=0,
                duplicate_count=0,
                error_count=len(df),
                errors=[f"Error importing DataFrame: {str(e)}"]
            )
    
    # --- Exporting --------------------------------------------------
    
    def export_dataframe(self, *, all_cols: bool = False, **filters) -> pd.DataFrame:
        """
        Export transactions to a pandas DataFrame.
        
        Args:
            all_cols: If True, include all columns; otherwise, include only core columns
            **filters: Optional filters to apply (start_date, end_date, account_number, etc.)
            
        Returns:
            DataFrame containing transaction data
        """
        # Get transactions based on filters
        transactions = self.list_transactions(**filters)
        
        if not transactions:
            return pd.DataFrame()
        
        # Convert to DataFrame
        df = pd.DataFrame([t.__dict__ for t in transactions])
        
        # Filter columns if not all_cols
        if not all_cols and not df.empty:
            core_cols = ['date', 'description', 'amount', 'account_number', 'tags']
            all_cols_set = set(df.columns)
            cols_to_keep = [col for col in core_cols if col in all_cols_set]
            df = df[cols_to_keep]
        
        return df
    
    def export_csv(self, target: Path, *, all_cols: bool = False, **kwargs) -> None:
        """
        Export transactions to a CSV file.
        
        Args:
            target: Path to the output CSV file
            all_cols: If True, include all columns; otherwise, include only core columns
            **kwargs: Additional arguments to pass to pandas to_csv
            
        Returns:
            None
        """
        df = self.export_dataframe(all_cols=all_cols)
        
        if df.empty:
            # Create an empty file with headers
            core_cols = ['date', 'description', 'amount', 'account_number', 'tags']
            pd.DataFrame(columns=core_cols).to_csv(target, index=False, **kwargs)
        else:
            # Export with provided options
            df.to_csv(target, index=False, **kwargs)
    
    # --- Query helpers ---------------------------------------------
    
    def list_transactions(self, **filters) -> List[Transaction]:
        """
        List transactions with optional filtering.
        
        Args:
            **filters: Optional filters (start_date, end_date, account_number, etc.)
            
        Returns:
            List of Transaction objects matching the filters
        """
        return self.repo.get_transactions(filters)
    
    def get_transaction_by_id(self, transaction_id: int) -> Optional[Transaction]:
        """
        Get a transaction by its ID.
        
        Args:
            transaction_id: ID of the transaction to retrieve
            
        Returns:
            Transaction object if found, None otherwise
        """
        return self.repo.get_transaction_by_id(transaction_id)
    
    def update_transaction(self, transaction_id: int, updates: Dict[str, Any]) -> bool:
        """
        Update a transaction.
        
        Args:
            transaction_id: ID of the transaction to update
            updates: Dictionary of fields to update
            
        Returns:
            True if successful, False otherwise
        """
        return self.repo.update_transaction(transaction_id, updates)
    
    def update_transaction_tags(self, transaction_id: int, tags: str) -> bool:
        """
        Update the tags for a transaction.
        
        Args:
            transaction_id: ID of the transaction to update
            tags: New tags string (comma-separated)
            
        Returns:
            True if successful, False otherwise
        """
        return self.update_transaction(transaction_id, {'tags': tags})
    
    def get_unique_account_numbers(self) -> List[str]:
        """
        Get a list of unique account numbers in the database.
        
        Returns:
            List of unique account numbers
        """
        transactions = self.list_transactions()
        account_numbers = set()
        
        for transaction in transactions:
            if transaction.account_number:
                account_numbers.add(transaction.account_number)
        
        return sorted(list(account_numbers))
    
    # --- Statistics and reporting ---------------------------------
    
    def stats(self) -> Dict[str, Any]:
        """
        Get statistics about the transaction database.
        
        Returns:
            Dictionary with statistics:
            - total_count: Total number of transactions
            - date_range: (min_date, max_date) tuple
            - account_counts: Dictionary mapping account numbers to transaction counts
            - total_amount: Sum of all transaction amounts
        """
        transactions = self.list_transactions()
        
        if not transactions:
            return {
                'total_count': 0,
                'date_range': (None, None),
                'account_counts': {},
                'total_amount': 0.0
            }
        
        # Calculate statistics
        dates = [t.date for t in transactions if t.date]
        min_date = min(dates) if dates else None
        max_date = max(dates) if dates else None
        
        account_counts = {}
        for t in transactions:
            if t.account_number:
                account_counts[t.account_number] = account_counts.get(t.account_number, 0) + 1
        
        total_amount = sum(t.amount for t in transactions)
        
        return {
            'total_count': len(transactions),
            'date_range': (min_date, max_date),
            'account_counts': account_counts,
            'total_amount': total_amount
        }

    def delete_all_transactions(self) -> int:
        """
        Delete all transactions from the database.
        
        Returns:
            The number of transactions deleted.
        """
        return self.repo.delete_all_transactions()
