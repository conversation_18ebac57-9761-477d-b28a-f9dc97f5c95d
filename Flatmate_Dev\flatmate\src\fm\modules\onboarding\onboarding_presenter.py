# PROPOSED - NEEDS REVIEW
# This is a proposed implementation of the onboarding presenter
# Currently disabled to prevent VS Code errors

from typing import Optional, Dict, Any
from PySide6.QtCore import QObject, Signal

from .onboarding_state import OnboardingState, OnboardingStep
from .onboarding_view import OnboardingView
from ...models.profiles.flatmate import FlatmateProfile
from ...models.profiles.flat_manager import FlatManagerProfile

class OnboardingPresenter(QObject):
    """Presenter class for managing the onboarding process."""
    
    # Signals
    step_changed = Signal(OnboardingStep)
    onboarding_completed = Signal(object)  # Emits the created profile
    
    def __init__(self):
        super().__init__()
        self.state = OnboardingState()
        self.view: Optional[OnboardingView] = None
    
    def set_view(self, view: 'OnboardingView') -> None:
        """Set the view and connect signals."""
        self.view = view
        self._connect_signals()
    
    def _connect_signals(self) -> None:
        """Connect view signals to presenter methods."""
        if self.view:
            self.view.next_clicked.connect(self.handle_next)
            self.view.back_clicked.connect(self.handle_back)
            self.view.form_data_submitted.connect(self.handle_form_data)
    
    def start_onboarding(self) -> None:
        """Start the onboarding process."""
        self.state = OnboardingState()  # Reset state
        self._update_view()
    
    def handle_next(self) -> None:
        """Handle next button click."""
        if self._validate_current_step():
            next_step = self.state.next_step()
            self._update_view()
            self.step_changed.emit(next_step)
            
            # If we've reached the complete step, create the profile
            if next_step == OnboardingStep.COMPLETE:
                self._create_and_emit_profile()
    
    def handle_back(self) -> None:
        """Handle back button click."""
        prev_step = self.state.previous_step()
        self._update_view()
        self.step_changed.emit(prev_step)
    
    def handle_form_data(self, data: Dict[str, Any]) -> None:
        """Handle form data submission."""
        # Store all form data
        for key, value in data.items():
            self.state.store_data(key, value)
        
        # Handle user type selection specifically
        if self.state.current_step == OnboardingStep.USER_TYPE:
            self.state.set_user_type(data.get('user_type', ''))
    
    def _update_view(self) -> None:
        """Update the view based on current state."""
        if self.view:
            self.view.update_step(self.state.current_step)
    
    def _validate_current_step(self) -> bool:
        """Validate the current step before proceeding."""
        if not self.view:
            return False
        return self.view.validate_current_step()
    
    def _create_and_emit_profile(self) -> None:
        """Create and emit the appropriate profile type."""
        if self.state.user_type == 'flatmate':
            profile = self._create_flatmate_profile()
        else:
            profile = self._create_manager_profile()
        
        self.onboarding_completed.emit(profile)
    
    def _create_flatmate_profile(self) -> FlatmateProfile:
        """Create a flatmate profile from collected data."""
        profile = FlatmateProfile()
        
        # Update basic info
        profile.name = self.state.get_data('name')
        profile.email = self.state.get_data('email')
        profile.phone = self.state.get_data('phone')
        
        return profile
    
    def _create_manager_profile(self) -> FlatManagerProfile:
        """Create a manager profile from collected data."""
        profile = FlatManagerProfile()
        
        # Update basic info
        profile.name = self.state.get_data('name')
        profile.email = self.state.get_data('email')
        profile.phone = self.state.get_data('phone')
        
        return profile
