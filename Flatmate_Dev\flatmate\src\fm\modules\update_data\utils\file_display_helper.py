"""
Utility for extracting file display information.
"""

import os
import pprint

import pandas as pd

from .statement_handlers._handler_registry import get_handler


class FileDisplayHelper:
    @staticmethod
    def get_file_info(file_path):
        """
        Extract file information using the lightweight handler detection.

        Args:
            file_path (str): Path to the file

        Returns:
            dict: File information including size and format.
        """
        try:
            # Normalize path to handle different slash types (e.g., from Qt dialogs)
            normalized_path = os.path.normpath(file_path)

            # File size
            size = os.path.getsize(normalized_path)
            size_str = (
                f"{size / 1024:.1f} KB"
                if size < 1024 * 1024
                else f"{size / (1024 * 1024):.1f} MB"
            )

            # Use statement handlers for format detection via filepath
            handler_instance = get_handler(normalized_path)

            # Prepare format info
            format_info = {
                "bank_type": "Unknown",
                "format_type": "Unknown",
                "handler": None,
            }

            if handler_instance:
                format_info = {
                    "bank_type": handler_instance.statement_format.bank_name,
                    "format_type": handler_instance.statement_format.variant,
                    "handler": handler_instance.__class__.__name__,
                }

            # Prepare file info dictionary
            file_info = {
                "path": normalized_path,
                "size_bytes": size,
                "size_str": size_str,
                **format_info,  # Unpack bank_type and format_type
            }

            # Pretty print to terminal
            print(f"File Information for {normalized_path}:")
            pprint.pprint(file_info, width=80, compact=True)
            print("-" * 40)

            return file_info

        except Exception as e:
            print(f"Error processing {file_path}: {e}")
            return {
                "path": file_path,
                "size_bytes": 0,
                "size_str": "N/A",
                "bank_type": None,
                "format_type": None,
                "error": str(e),
            }

    @staticmethod
    def process_files(file_paths):
        """
        Process multiple files and collect their information
        
        Args:
            file_paths (list): List of file paths to process
        
        Returns:
            dict: Mapping of file paths to their processed information
        """
        return {
            file_path: FileDisplayHelper.get_file_info(file_path) 
            for file_path in file_paths
        }
