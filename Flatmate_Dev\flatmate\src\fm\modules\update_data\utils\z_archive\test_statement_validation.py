import os
import sys
import pandas as pd

# Add project root to the Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..', '..'))
sys.path.insert(0, project_root)

from fm.core.services.logger import Logger
from fm.modules.update_data.utils.file_utils import load_csv_to_df
from fm.modules.update_data.utils.statement_handlers._handler_registry import get_handler
from fm.core.data_services.standards.fm_standard_columns import StandardColumns

logger = Logger()

TEST_CASES = [
    {
        "filepath": os.path.join(project_root, "src/fm/modules/update_data/utils/test_csvs/38-9004-0646977-00_01Oct_kbank_basic.CSV"),
        "expected_account": "38-9004-0646977-00",
        "handler_name": "KiwibankBasicCSVHandler",
    },
    {
        "filepath": os.path.join(project_root, "src/fm/modules/update_data/utils/test_csvs/38-9004-0646977-00_17Oct_kbank_fullCSV.CSV"),
        "expected_account": "38-9004-0646977-00",
        "handler_name": "KiwibankFullCSVHandler",
    },
    {
        "filepath": os.path.join(project_root, "src/fm/modules/update_data/utils/test_csvs/Export20240325231257_asb.csv"),
        "expected_account": "12-3053-0478706-50",
        "handler_name": "ASBStandardCSVHandler",
    },
    {
        "filepath": os.path.join(project_root, "src/fm/modules/update_data/utils/test_csvs/02-1245-0452229-002_01Aug2023_15Dec2024_co-op_bank.Csv"),
        "expected_account": "02-1245-0452229-002",
        "handler_name": "CoopStandardCSVHandler",
    },
]

def run_validation_tests():
    """Runs a series of validation tests on statement handlers."""
    logger.info("--- Starting Statement Handler Validation ---")

    
    all_passed = True

    for i, test in enumerate(TEST_CASES):
        filepath = test["filepath"]
        expected_account = test["expected_account"]
        expected_handler = test["handler_name"]
        filename = os.path.basename(filepath)

        logger.info(f"\n--- Test {i+1}/{len(TEST_CASES)}: {filename} ---")

        if not os.path.exists(filepath):
            logger.error(f"FAIL: File not found at {filepath}")
            all_passed = False
            continue

        # 1. Load file
        raw_df = load_csv_to_df(filepath)
        if raw_df is None:
            logger.error(f"FAIL: Failed to load CSV file.")
            all_passed = False
            continue
        logger.debug(f"Successfully loaded file with shape {raw_df.shape}")

        # 2. Find handler
        handler = get_handler(raw_df, filepath)
        if handler is None:
            logger.error(f"FAIL: No suitable handler found.")
            all_passed = False
            continue
        
        handler_name = handler.__class__.__name__
        if handler_name != expected_handler:
            logger.error(f"FAIL: Incorrect handler matched. Expected {expected_handler}, got {handler_name}")
            all_passed = False
            continue
        else:
            logger.info(f"PASS: Correct handler matched: {handler_name}")

        # 3. Process file
        try:
            processed_df = handler.format_df(raw_df)
        except Exception as e:
            logger.error(f"FAIL: An exception occurred during format_df: {e}")
            all_passed = False
            continue

        # 4. Validations (Fail Fast)

        # a. Check for non-empty dataframe
        if processed_df.empty:
            logger.error(f"FAIL: Processed DataFrame is empty.")
            all_passed = False
            continue
        logger.info(f"PASS: Processed DataFrame is not empty (shape: {processed_df.shape}).")

        # b. Check account number
        account_col = StandardColumns.ACCOUNT.value
        if account_col not in processed_df.columns:
            logger.error(f"FAIL: Account number column '{account_col}' not found in output.")
            all_passed = False
            continue
        
        extracted_account = processed_df[account_col].iloc[0]
        if extracted_account != expected_account:
            logger.error(f"FAIL: Account number mismatch. Expected: '{expected_account}', Got: '{extracted_account}'")
            all_passed = False
            continue
        logger.info(f"PASS: Account number correctly extracted: '{extracted_account}'")

        # c. Check for core standard columns
        mandatory_cols = {
            StandardColumns.DATE.value,
            StandardColumns.DETAILS.value,
            StandardColumns.AMOUNT.value,
            StandardColumns.ACCOUNT.value,
            StandardColumns.SOURCE_FILENAME.value,
        }
        
        missing_mandatory = mandatory_cols - set(processed_df.columns)
        if missing_mandatory:
            logger.error(f"FAIL: Missing mandatory standard columns: {sorted(list(missing_mandatory))}")
            all_passed = False
            continue

        # d. Check for uniqueness column (Balance or Unique Id)
        balance_col = StandardColumns.BALANCE.value
        unique_id_col = StandardColumns.UNIQUE_ID.value
        if not (balance_col in processed_df.columns or unique_id_col in processed_df.columns):
            logger.error(f"FAIL: Missing a uniqueness column. Must have either '{balance_col}' or '{unique_id_col}'.")
            all_passed = False
            continue
        
        logger.info(f"PASS: All core standard columns are present.")

    logger.info("\n--- Validation Summary ---")
    if all_passed:
        logger.info("SUCCESS: All tests passed!")
    else:
        logger.error("FAILURE: Some tests failed. Please review the logs.")

if __name__ == "__main__":
    run_validation_tests()
