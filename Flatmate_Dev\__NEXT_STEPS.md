# Flatmate Development - Next Steps

## Priority 1: Core Functionality (High Impact, Low Effort)

### 1. Database Status Indicator
- Show connection status and record counts in the UI
- Add basic database health metrics
- **Effort**: 1-2 hours
- **Impact**: High (Improves user confidence)

### 2. Account Naming
- Add account name display in transaction views
- Allow users to set custom account names
- **Effort**: 2-3 hours
- **Impact**: High (Improves usability)

## Priority 2: Data Management

### 3. Column Management System
- Implement unified column handling
- Support different column types (Statement, Import, DB, User)
- **Effort**: 4-6 hours
- **Impact**: High (Foundation for other features)

### 4. Transaction Table Improvements
- Resizable columns with saved preferences
- Better row selection and highlighting
- Copy/paste functionality
- **Effort**: 4-5 hours
- **Impact**: High (Improves usability)

## Priority 3: User Experience

### 5. Basic Settings Panel
- Database configuration
- UI preferences
- Dev settings for testing
- **Effort**: 3-4 hours
- **Impact**: Medium

### 6. Export Functionality
- Export current view to CSV/Excel
- Customizable export formats
- **Effort**: 2-3 hours
- **Impact**: Medium

## Priority 4: Technical Debt

### 7. ✅ Code Organization (Completed)
- ~~Move database code to core/data_services~~
- ~~Standardize naming conventions~~
- **Effort**: 4-6 hours
- **Impact**: High (Maintainability)

### 8. Documentation
- Document column handling system
- Update README with new features
- **Effort**: 2-3 hours
- **Impact**: Medium

## Future Enhancements

### 9. Advanced Filtering
- Filter by date, category, account, etc.
- Save filter presets
- **Effort**: 4-6 hours
- **Impact**: High

### 10. ✅ Custom Table View (Completed)
- ~~Reusable table component~~
- ~~Light/dark themes~~
- **Effort**: 8-10 hours
- **Impact**: Medium

## Notes
- Effort estimates are rough and may vary
- Priorities can be adjusted based on user feedback
- Technical debt items should be addressed alongside new features