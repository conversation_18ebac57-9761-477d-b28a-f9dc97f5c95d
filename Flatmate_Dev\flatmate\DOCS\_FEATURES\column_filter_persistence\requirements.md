# Feature Requirements

## User Story
As a user, I want the column filter tool in the categorise table view to remember my filter terms (even after closing the app), and to support simple AND/exclude logic, so that I can efficiently filter data with minimal effort.
- The search columns dropdown in the toolbar must:
  - Display all available columns as selectable options.
  - Default to the "details" column on first use.
  - Persist the last selected column between sessions (if persistence enabled).


## Acceptance Criteria
- [ ] Filter terms persist across app restarts (unless disabled by config).
- [ ] Persistence is user-configurable (can enable/disable).
- [ ] Multiple space-separated terms are treated as AND (all must match).
- [ ] Terms prefixed with `-` (with spaces before/after) are excluded (rows containing them are filtered out).
- [ ] Operator tokens (AND/exclude) must be surrounded by spaces to be treated as operators; otherwise, they are part of the search term.
- [ ] Quotes or * prefix may be used to force literal search (edge case, rare).
- [ ] UI clearly distinguishes between operators and search terms.
- [ ] Filtering is case-insensitive and matches substrings.
- [ ] No speculative features (OR, advanced operators) in phase 1.

## Success Metrics
- Filter state restored after restart if enabled.
- Filtering logic is intuitive and matches user expectations.
- No accidental interpretation of `-` as a search term.
- Operators (such as `-` for exclude) are only treated as operators if surrounded by spaces. Otherwise, they are part of the search term. For rare cases where a search term starts with `-`, use quotes or a * prefix to force literal matching.
