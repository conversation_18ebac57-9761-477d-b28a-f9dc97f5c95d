"""
Configuration Loader for FlatMate Application.

Provides a centralized configuration loading and management mechanism.
"""

from typing import Any, Dict, Optional

from .path_manager import path_manager
from .settings_manager import settings_manager
from .master_file_tracker import master_file_tracker


class ConfigurationLoader:
    """
    Centralized configuration loader for the FlatMate application.
    
    Provides a unified interface for accessing configuration components.
    """

    def __init__(self):
        """
        Initialize configuration loader with core configuration components.
        """
        self.paths = path_manager
        self.settings = settings_manager
        self.master_tracker = master_file_tracker

    def get_path(self, key: str) -> Optional[str]:
        """
        Retrieve a specific application path.
        
        Args:
            key (str): Path key to retrieve.
        
        Returns:
            Optional[str]: Path value or None if not found.
        """
        return self.paths.app_paths.get(key)

    def get_setting(self, key: str, default: Any = None) -> Any:
        """
        Retrieve a specific application setting.
        
        Args:
            key (str): Setting key to retrieve.
            default (Any, optional): Default value if setting is not found.
        
        Returns:
            Any: Setting value or default.
        """
        return self.settings.get_setting(key, default)

    def get_master_file_path(self) -> Optional[str]:
        """
        Retrieve the current master file path.
        
        Returns:
            Optional[str]: Path to the current master file.
        """
        return str(self.master_tracker.get_master_path()) if self.master_tracker.get_master_path() else None

    def update_master_file_location(self, file_path: str):
        """
        Update the master file location.
        
        Args:
            file_path (str): Path to the new master file.
        """
        self.master_tracker.update_master_location(file_path)

    def create_profile(self, name: str, settings: Optional[Dict] = None):
        """
        Create a new user profile.
        
        Args:
            name (str): Name of the profile.
            settings (Dict, optional): Initial settings for the profile.
        """
        self.settings.create_profile(name, settings)

    def get_active_profile(self) -> Dict:
        """
        Retrieve the currently active profile.
        
        Returns:
            Dict: Active user profile information.
        """
        return self.settings.get_active_profile()

# Create a singleton instance
config_loader = ConfigurationLoader()
