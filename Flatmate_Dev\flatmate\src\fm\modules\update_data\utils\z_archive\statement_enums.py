"""Statement handler attribute classes and their possible values."""
from enum import Enum
from dataclasses import dataclass, field
from typing import List, Tuple, Optional

class StatementFormat:
    """Statement format attributes."""
    # Example:
    # statement_format = StatementFormat(
    #     bank_name="ASB",
    #     variant="standard",
    #     file_type="csv"
    # )
    bank_name: str  # e.g., "ASB", "Kiwibank"
    variant: str    # e.g., "basic", "standard", "full"
    file_type: str  # e.g., "csv"

class ColumnAttributes:
    """Column mapping and formatting attributes."""
    # Example:
    # column_attrs = ColumnAttributes(
    #     has_headers=True,
    #     has_col_names=True,
    #     n_source_cols=7,
    #     col_names_in_header=True,
    #     has_account_column=False,
    #     col_names_row=0,
    #     source_col_names=["Date", "Details", ...],
    #     target_col_names=[StandardColumns.DATE, ...],
    #     date_format='%d/%m/%Y'
    # )
    has_headers: bool
    has_col_names: bool
    n_source_cols: int
    date_format: str
    
    # Optional
    col_names_in_header: bool = False
    col_names_in_data_row: bool = False
    has_account_column: bool = True
    col_names_row: int = 0
    source_col_names: List[str] = field(default_factory=list)
    target_col_names: List = field(default_factory=list)

class AccountNumberAttributes:
    """Account number extraction attributes."""
    # Example:
    # account_num_attrs = AccountNumberAttributes(
    #     pattern=r'Account\s+[\d-]+',
    #     in_metadata=True,
    #     location=(1, 0)
    # )
    pattern: str = ""
    
    # Location flags (only one should be True)
    in_data: bool = False      # In data cells (specify location)
    in_header: bool = False    # In column headers
    in_file_name: bool = False # In filename
    in_metadata: bool = False  # In metadata section
    
    # Position if in_data or in_metadata
    location: Tuple[int, int] = (0, 0)  # (row, col)

class SourceMetadataAttributes:
    """Metadata section attributes."""
    # Example:
    # source_metadata_attrs = SourceMetadataAttributes(
    #     has_metadata_rows=True,
    #     metadata_start=(0, 0),
    #     metadata_end=(5, 0)
    # )
    has_metadata_rows: bool = False
    metadata_start: Tuple[int, int] = (0, 0)  # (start_row, start_col)
    metadata_end: Tuple[int, int] = (0, 0)    # (end_row, end_col)