"""
Service for managing info bar messages across the application.
"""

from PySide6.QtCore import QObject

from ...core.services.event_bus import Events, global_event_bus


class InfoBarService(QObject):
    """Service for managing info bar messages across the application."""

    # Singleton instance
    _instance = None

    def __init__(self):
        """Initialize the info bar service."""
        super().__init__()
        self.event_bus = global_event_bus

    @classmethod
    def get_instance(cls):
        """Get the singleton instance of the service.

        Returns:
            InfoBarService: The singleton instance
        """
        if cls._instance is None:
            cls._instance = InfoBarService()
        return cls._instance

    def publish_message(self, message, priority=0, is_loading=False, progress=None, total=None, is_error=False, is_warning=False):
        """Publish a message to the info bar.

        Args:
            message: The message to display
            priority: Message priority (higher = more important)
            is_loading: Whether operation is in progress
            progress: Current progress (optional)
            total: Total items (optional)
            is_error: Whether this is an error message
            is_warning: Whether this is a warning message
        """
        # Publish the message using the event bus
        if message:
            payload = {
                "text": message,
                "is_loading": is_loading,
                "is_error": is_error,
                "is_warning": is_warning
            }

            # Add progress info if provided
            if progress is not None:
                payload["progress"] = progress
            if total is not None:
                payload["total"] = total

            self.event_bus.publish(Events.INFO_MESSAGE, payload)

    def show(self):
        """Request to show the info bar."""
        self.event_bus.publish(Events.INFO_SHOW, None)

    def hide(self):
        """Request to hide the info bar."""
        self.event_bus.publish(Events.INFO_HIDE, None)

    def set_visible(self, visible: bool):
        """Set the info bar visibility.

        Args:
            visible: True to show, False to hide
        """
        if visible:
            self.show()
        else:
            self.hide()

    def publish_loading(self, message, progress=None, total=None):
        """Publish a loading message.

        Args:
            message: The loading message to display
            progress: Current progress (optional)
            total: Total items (optional)
        """
        self.publish_message(message, is_loading=True, progress=progress, total=total)

    def publish_error(self, message):
        """Publish an error message.

        Args:
            message: The error message to display
        """
        self.publish_message(message, is_error=True)

    def publish_warning(self, message):
        """Publish a warning message.

        Args:
            message: The warning message to display
        """
        self.publish_message(message, is_warning=True)

    def clear(self):
        """Clear the current message."""
        self.event_bus.publish(Events.INFO_CLEAR, None)
