# Cat Data Module - Next Development Tasks



## High Priority Tasks

### 1. Column Data Management (App-wide Concern)
**Status**: Critical - Current alphabetic sorting is useless
**Impact**: High - Affects all table views across the application

**Current Problem**:
- Columns are sorted alphabetically
- Important columns (Date, Amount, Description) buried in the list
- No logical grouping or prioritization

**Solution**:
- Implement intelligent column ordering system
- Group columns by importance/usage frequency
- Create module-specific default column sets
- Add user customization with persistence

**Files to modify**:
- `flatmate/src/fm/core/data_services/standards/columns.py`
- `flatmate/src/fm/gui/_shared_components/enhanced_table/column_selector.py`
- Module-specific column configurations

**Acceptance Criteria**:
- [ ] Date, Amount, Description appear first
- [ ] Logical grouping (Core Data, Metadata, User Data)
- [ ] Module-specific defaults (categorize vs reports)
- [ ] User customization persisted

### 2. Table View Data Loading Optimization
**Status**: Performance improvement opportunity
**Impact**: Medium - Affects startup time

**Current State**:
- 3.1s table rendering during startup
- Blocking UI during data loading
- No user feedback during load

**Solutions**:
- Add loading screen/splash during startup
- Background threading for table rendering
- Progressive loading (load visible rows first)
- Loading progress indicators

**Implementation Options**:
```python
# Option A: Loading Screen
class LoadingScreen(QSplashScreen):
    def show_progress(self, message, percentage):
        # Update loading message and progress bar

# Option B: Background Threading
class TableDataLoader(QThread):
    progress_updated = Signal(str, int)
    data_loaded = Signal(pd.DataFrame)
```

**Acceptance Criteria**:
- [ ] Loading screen shows during startup
- [ ] Progress feedback for data loading
- [ ] Non-blocking UI during table setup
- [ ] Graceful error handling

### 3. Live Filtering System
**Status**: Critical for user experience
**Impact**: High - Core functionality improvement

**Current Problem**:
- Filters require database re-query
- Slow response to filter changes
- Loading all data but not utilizing it for filtering

**Solution**:
- Implement client-side filtering on loaded DataFrame
- Real-time filter application
- No database round-trips for filtering

**Technical Approach**:
```python
class LiveFilterManager:
    def __init__(self, dataframe):
        self.original_df = dataframe
        self.filtered_df = dataframe
        
    def apply_filters(self, filters):
        # Apply filters to self.original_df
        # Update table view with filtered results
        # No database query needed
```

**Filter Types to Support**:
- Date range filtering
- Account filtering  
- Amount range filtering
- Text search in descriptions
- Category filtering
- Multiple filter combinations

**Acceptance Criteria**:
- [ ] Instant filter application (<100ms)
- [ ] No database queries for filtering
- [ ] Multiple simultaneous filters
- [ ] Filter state persistence
- [ ] Clear/reset all filters option

### 4. Info Bar Enhancement
**Status**: User experience improvement
**Impact**: Medium - Better user feedback

**Current State**:
- Basic info bar at bottom
- Limited transaction information

**Required Features**:
```
Showing 1,247 of 2,099 transactions | Filtered by: Date (Jan 2024), Account (Kiwibank)
```

**Implementation**:
```python
class TransactionInfoBar(QWidget):
    def update_display(self, visible_count, total_count, active_filters):
        # Update display text with current state
        
    def show_filter_summary(self, filters):
        # Show active filters in readable format
```

**Acceptance Criteria**:
- [ ] Shows "X of Y transactions"
- [ ] Lists active filters
- [ ] Updates in real-time with filtering
- [ ] Clickable filter tags to remove individual filters

## Medium Priority Tasks

### 5. Account Name Management
**Status**: Data quality improvement
**Impact**: Medium - Better user experience

**Current Problem**:
- Account numbers shown instead of names (e.g., "04-1234-5678901-00")
- Users don't recognize their accounts easily
- No account naming system

**Solution Phases**:

**Phase 1: Account Name Column**
- Add account_name column to database
- Prompt user to name unnamed accounts
- Show names by default, numbers as fallback

**Phase 2: Smart Account Detection**
```python
class AccountNameDetector:
    BANK_PATTERNS = {
        '04': 'Kiwibank',
        '38': 'TSB',
        '06': 'ANZ',
        '03': 'Westpac'
    }
    
    def suggest_bank_name(self, account_number):
        # Extract bank code and suggest name
```

**Phase 3: User Account Management**
- Account management interface
- Bulk account naming
- Account grouping/categorization

**Acceptance Criteria**:
- [ ] Account name column added
- [ ] User prompted for unnamed accounts
- [ ] Bank detection from account numbers
- [ ] Names shown by default in tables
- [ ] Account management interface

### 6. Categories Toolbar
**Status**: Core functionality addition
**Impact**: High - Primary module feature

**Current State**:
- No category management tools
- Manual category entry only

**Required Features**:
- Quick category buttons (Food, Transport, Bills, etc.)
- Category autocomplete
- Bulk categorization tools
- Category statistics/summary

**Implementation**:
```python
class CategoriesToolbar(QWidget):
    def __init__(self):
        self.quick_categories = ['Food', 'Transport', 'Bills', 'Entertainment']
        self.setup_quick_buttons()
        self.setup_bulk_tools()
        
    category_applied = Signal(str)  # Connect to table selection
```

**Acceptance Criteria**:
- [ ] Quick category buttons
- [ ] Apply to selected transactions
- [ ] Bulk categorization tools
- [ ] Category usage statistics
- [ ] Custom category creation

### 7. Left Panel Redesign
**Status**: UX improvement
**Impact**: Medium - Better workflow

**Current Problem**:
- Left panel designed for database loading
- Doesn't fit new eager loading model
- Filters could be more accessible

**New Design Concept**:
```
┌─ FILTERS ─────────────┐
│ 🗓️  Date Range        │
│ 🏦  Account           │  
│ 💰  Amount Range      │
│ 🔍  Search            │
│ 📁  Category          │
│                       │
│ [Clear All] [Save]    │
└───────────────────────┘
```

**Features**:
- Collapsible filter sections
- Quick filter presets (This Month, Last 30 Days)
- Filter save/load functionality
- Visual filter state indicators

**Acceptance Criteria**:
- [ ] Redesigned for filtering workflow
- [ ] Collapsible sections
- [ ] Filter presets
- [ ] Save/load filter sets
- [ ] Visual active filter indicators

## Low Priority / Future Tasks

### 8. Technical Debt & Polish

**QPainter Warnings**:
- Investigate QPainter warnings in logs
- Fix painting issues in custom widgets
- Ensure proper widget lifecycle

**Home Module Transitions**:
- Fix missing transition animations
- Ensure smooth navigation experience
- Add visual feedback for navigation

**Performance Monitoring**:
- Add performance metrics collection
- Monitor memory usage over time
- Identify optimization opportunities

### 9. Advanced Features (Future)

**Export Functionality**:
- Export filtered transactions
- Multiple format support (CSV, Excel, PDF)
- Custom export templates

**Advanced Filtering**:
- Saved filter combinations
- Complex filter logic (AND/OR)
- Filter sharing between users

**Data Visualization**:
- Spending charts and graphs
- Category breakdown visualizations
- Trend analysis tools

## Development Priorities

Given your time constraints with paying work, suggested order:

1. **Column Data Management** - Biggest UX impact
2. **Live Filtering** - Core functionality that users expect
3. **Info Bar Enhancement** - Quick win for better feedback
4. **Loading Screen** - Polish for startup experience
5. **Account Names** - Data quality improvement
6. **Categories Toolbar** - Feature completion
7. **Left Panel Redesign** - UX refinement

## Architecture Notes

The eager loading foundation you've built makes all these features much easier to implement:
- **Live filtering** is only possible because all data is loaded
- **Real-time updates** work because widgets persist
- **Performance** is maintained because no database round-trips
- **Memory usage** remains minimal due to efficient data structures

The 256KB memory footprint gives you plenty of headroom for these enhancements without performance concerns.

## Success Metrics

- **Navigation**: Remains instant (<100ms)
- **Memory**: Stays under 1MB per module
- **Filtering**: Real-time response (<100ms)
- **User Experience**: Professional desktop app feel
- **Stability**: Zero Qt crashes
- **Data Loading**: Startup under 5 seconds total
