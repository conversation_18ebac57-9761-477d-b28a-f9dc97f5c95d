# FlatMate Configuration System

The FlatMate configuration system is a hierarchical, modular configuration management system that provides type-safe access to application settings across all modules.

## Architecture Overview

### 1. Configuration Hierarchy

The system uses a three-tier configuration hierarchy with clear precedence:

1. **User Preferences** (`~/.flatmate/preferences.yaml`)
   - User-specific settings that override all other values
   - Persisted between sessions
   - Created automatically if missing

2. **Module Defaults** (`module/config/defaults.yaml`)
   - Module-specific default values
   - Versioned with the codebase
   - Loaded if no user preference exists

3. **Hardcoded Defaults** (`ModuleKeys.get_defaults()`)
   - Fallback values coded into each module
   - Used if neither preferences nor module defaults exist
   - Ensures the application always has valid settings

### 2. Key Components

#### ConfigManager (Singleton)
- Central configuration management
- Handles loading and saving of preferences
- Manages system and user paths
- Provides environment detection

#### BaseModuleConfig
- Base class for all module configs
- Handles default value initialization
- Provides type-safe key management
- Integrates with global event bus

#### Module-Specific Configs
- Inherit from BaseModuleConfig
- Define module-specific configuration keys
- Provide module-specific default values
- Handle module-specific configuration logic

### 3. Directory Structure

```
~/.flatmate/                     # User directory
└── preferences.yaml            # User preferences

fm/
├── core/
│   └── config/
│       ├── config.py           # Core config implementation
│       ├── keys.py            # Core config keys
│       └── global_defaults.yaml # Global application defaults
│
└── modules/
    └── module_name/
        └── config/
            ├── defaults.yaml   # Module defaults
            ├── local_config.py # Module config implementation
            └── keys.py        # Module-specific keys
```

## Usage Examples

### 1. Defining Module Keys

```python
class ModuleKeys:
    """Configuration keys for the module."""
    
    class Window(str, Enum):
        """Window settings"""
        WIDTH = 'module.window.width'
        HEIGHT = 'module.window.height'

    @classmethod
    def get_defaults(cls) -> dict:
        """Get default values."""
        return {
            cls.Window.WIDTH: 800,
            cls.Window.HEIGHT: 600,
        }
```

### 2. Accessing Configuration

```python
# Get a value (automatically falls back through hierarchy)
width = module_config.get_value(ModuleKeys.Window.WIDTH)

# Set a user preference
module_config.set_value(ModuleKeys.Window.WIDTH, 1024)
```

### 3. Creating Module Config

```python
class ModuleConfig(BaseModuleConfig[ModuleKeys]):
    """Configuration manager for module."""
    
    def __init__(self):
        super().__init__()
        self.events = global_event_bus
    
    def get_defaults(self) -> dict:
        """Get default values."""
        return ModuleKeys.get_defaults()
```

## Best Practices

1. **Key Naming**
   - Use dot notation for hierarchical settings
   - Prefix with module name to avoid conflicts
   - Use clear, descriptive names

2. **Default Values**
   - Always provide sensible defaults in `defaults.yaml`
   - Use hardcoded defaults as a last resort
   - Document the purpose of each default

3. **Type Safety**
   - Define all keys in enum classes
   - Use type hints consistently
   - Validate values when setting them

4. **Module Independence**
   - Keep module configs independent
   - Don't access other module's config directly
   - Use events for cross-module communication

5. **Documentation**
   - Document all configuration keys
   - Explain the purpose of default values
   - Provide usage examples

## Configuration Files

### preferences.yaml
```yaml
# User preferences override all other settings
module:
  window:
    width: 1200
    height: 800
```

### defaults.yaml
```yaml
# Module defaults
module:
  window:
    width: 1024
    height: 768
```

## Error Handling

The system includes robust error handling:

1. **Missing Files**
   - Creates default preferences if missing
   - Falls back to module defaults
   - Uses hardcoded values as last resort

2. **Invalid Values**
   - Logs validation errors
   - Falls back to defaults
   - Maintains type safety

3. **File Access**
   - Handles permission issues
   - Provides meaningful error messages
   - Maintains system stability

## Event Integration

The configuration system integrates with the global event bus:

1. **Change Notifications**
   - Emits events when values change
   - Allows modules to react to config changes
   - Maintains loose coupling

2. **Runtime Updates**
   - Supports dynamic configuration updates
   - Notifies affected components
   - Persists changes automatically

## Security Considerations

1. **File Permissions**
   - User preferences are stored in user space
   - Module defaults are read-only
   - System paths are protected

2. **Validation**
   - Input validation on all values
   - Type checking through enums
   - Safe YAML loading

3. **Separation**
   - Sensitive data in separate files
   - Environment-specific settings
   - Clear access patterns
