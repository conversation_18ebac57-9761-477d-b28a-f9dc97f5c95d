This is a prototype for a select options group - base class - that can be used anywere, it's not tested.
it and widgets like it should go in the core fm/gui folder, in a folder called common_widgets or similar...
This raises a question about the src/fm folder structure..
we currently have src/fm/gui
which contains the main window,
and src/fm/core which contains configs and services base classes
sometimes used as singletons
I find this a bit confusing...
Should it be core/gui /config/ services/ 
this would make imports src/fm/core/gui/common_widgets/select_options_group.py or similar.

This could be used similar to:

class SelectSaveLocation(SelectOptionGroup):
    
    def __init__(self, parent=None):
        super().__init__(options=SaveOptions, label_text="Save Location", button_text="Select Save Location", parent=parent)
        
    