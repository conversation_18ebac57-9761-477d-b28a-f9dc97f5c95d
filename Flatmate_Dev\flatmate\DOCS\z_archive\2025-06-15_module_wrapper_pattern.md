# Module Wrapper Pattern

## Overview
The Module Wrapper Pattern is a core architectural pattern in FlatMate that provides clean interfaces between modules and core systems. It ensures modularity, maintainability, and proper separation of concerns.

## Core Concepts

### 1. Separation of Concerns
- Modules shouldn't know about core implementation details
- Core systems shouldn't know about module-specific needs
- Wrappers bridge this gap with clean interfaces

### 2. Types of Wrappers

#### Config Wrapper
```
module_name/config/
├── __init__.py         # Exports module_config
├── local_config.py     # Config wrapper
├── defaults.toml       # Module defaults
└── README.md          # Documentation
```

#### Service Wrapper
```
module_name/services/
├── __init__.py         # Exports module_services
├── local_services.py   # Service wrapper
└── README.md          # Documentation
```

## Implementation

### Config Wrapper Example
```python
# local_config.py
from ....core.config import config as core_config
from ....core.services.logger import log_this, LogLevel

class ModuleConfig:
    """Module-specific configuration wrapper."""
    
    def __init__(self):
        self._core = core_config
        
    @log_this(LogLevel.DEBUG)
    def get_module_path(self, key):
        """Get module-specific path with validation."""
        path = self._core.get_path(key)
        if not path.exists():
            path.mkdir(parents=True)
        return path
        
    def get_value(self, key, default=None):
        """Get config value with module context."""
        return self._core.get_value(key, default)

# Global instance
module_config = ModuleConfig()
```

### Service Wrapper Example
```python
# local_services.py
from ....core.services import core_service
from ....core.services.logger import log_this, LogLevel

class ModuleServices:
    """Module-specific service wrapper."""
    
    def __init__(self):
        self._core = core_service
        
    @log_this(LogLevel.INFO)
    def module_specific_action(self, data):
        """Add module-specific processing."""
        validated_data = self._validate(data)
        return self._core.process(validated_data)
        
    def _validate(self, data):
        """Module-specific validation."""
        # Add validation logic
        return data

# Global instance
module_services = ModuleServices()
```

## Usage in Modules

### In Presenters
```python
from .config import module_config
from .services import module_services

class ModulePresenter:
    def __init__(self):
        self.config = module_config
        self.services = module_services
        
    def handle_action(self, data):
        if self.config.get_value('feature.enabled'):
            self.services.module_specific_action(data)
```

## Benefits

### 1. Encapsulation
- Core implementation details hidden from modules
- Module-specific logic contained in wrappers
- Clean separation of concerns

### 2. Maintainability
- Changes to core systems only affect wrappers
- Module code remains stable
- Easy to update or replace core implementations

### 3. Testability
- Easy to mock wrappers for testing
- Clear boundaries for unit tests
- Module-specific validation in one place

### 4. Flexibility
- Add module-specific methods without affecting core
- Customize behavior for module needs
- Share common functionality across modules

## Best Practices

### 1. Wrapper Design
- Keep wrappers focused and minimal
- Add only necessary module-specific logic
- Document wrapper functionality clearly
- Use consistent naming across modules

### 2. Configuration
- Use module-specific TOML for defaults
- Keep sensitive config in core
- Document config keys and usage
- Validate config access

### 3. Services
- Add validation in wrappers
- Keep core services generic
- Document service methods
- Handle errors appropriately

### 4. Logging
- Log important operations
- Use appropriate log levels
- Include relevant context
- Handle errors gracefully

## Adding New Wrappers

### 1. Create Directory Structure
```bash
mkdir -p module_name/{config,services}
touch module_name/config/{__init__.py,local_config.py,defaults.toml,README.md}
touch module_name/services/{__init__.py,local_services.py,README.md}
```

### 2. Implement Wrappers
1. Create wrapper classes
2. Add module-specific methods
3. Document functionality
4. Create global instances

### 3. Update Documentation
1. Update module README.md
2. Document new methods
3. Add usage examples
4. Update architecture docs

## Example: Update Data Module

### Config Wrapper
```python
# update_data/config/local_config.py
class UpdateDataConfig:
    def get_master_path(self):
        """Get master file path with validation."""
        return self._core.get_path('paths.master')
```

### Service Wrapper
```python
# update_data/services/local_services.py
class UpdateDataServices:
    def update_master_location(self, file_path):
        """Update master with validation."""
        if self._validate_master(file_path):
            return self._core.update_location(file_path)
```

## Conclusion
The Module Wrapper Pattern is a fundamental part of FlatMate's architecture. It provides clean interfaces, maintainable code, and proper separation of concerns. Following these patterns ensures consistent and maintainable module development.
