"""
Column Manager Service

This module provides a centralized service for managing column operations,
including filtering columns by usage, handling user preferences, and
conditional display of columns based on data presence.
"""

import pandas as pd
from typing import List, Dict, Optional, Set, Any
from fm.core.data_services.standards.columns import Columns, Column
from fm.core.database.sql_repository.transaction_repository import Transaction


class ColumnManager:
    """
    Central service for managing column operations across the application.
    
    This class provides methods for:
    - Getting columns by usage category
    - Converting between display and database column names
    - Filtering display columns based on data presence
    - Applying user display preferences
    """
    
    def __init__(self, user_preferences: Optional[Dict[str, Any]] = None):
        """
        Initialize the column manager.
        
        Args:
            user_preferences: Optional dictionary of user preferences for column display
        """
        self.user_preferences = user_preferences or {}
    
    def get_columns_by_usage(self, usage: str) -> List[Column]:
        """
        Get all columns for a specific usage category.

        Args:
            usage: Usage category string (e.g., 'source', 'display')

        Returns:
            List of Column objects that belong to the specified usage category.
        """
        return Columns.get(usage)
    
    def get_statement_handler_columns(self) -> List[Column]:
        """Get columns used in source data (bank statements)"""
        return self.get_columns_by_usage('statement_handler')

    def get_core_transaction_columns(self) -> List[Column]:
        """Get columns that form the core of a transaction record."""
        return self.get_columns_by_usage('core_transaction')

    def get_user_editable_columns(self) -> List[Column]:
        """Get columns that are editable by the user."""
        return self.get_columns_by_usage('user_editable')

    def get_db_system_columns(self) -> List[Column]:
        """Get columns used for internal database system purposes"""
        return self.get_columns_by_usage('db_system')
    
    def get_display_columns(self) -> List[Column]:
        """Get columns used for display in the UI, combining core and user-editable fields."""
        core_cols = self.get_core_transaction_columns()
        user_cols = self.get_user_editable_columns()
        # Combine and remove duplicates, preserving order
        return list(dict.fromkeys(core_cols + user_cols))

    def get_display_columns_with_data(self, df: pd.DataFrame) -> List[Column]:
        """
        Get display columns that actually contain data in the given DataFrame.
        
        This filters the display columns to only include those that have
        non-null values in at least one row of the DataFrame.
        
        Args:
            df: DataFrame containing transaction data
            
        Returns:
            List of display columns that have data
        """
        display_columns = self.get_display_columns()
        columns_with_data = []
        
        for col in display_columns:
            db_name = col.db_name
            # Check if column exists in DataFrame and has non-null values
            if db_name in df.columns and not df[db_name].isna().all():
                columns_with_data.append(col)
        
        return columns_with_data
    
    def convert_to_db_columns(self, display_columns: List[str]) -> List[str]:
        """
        Convert display column names to database column names.
        
        Args:
            display_columns: List of display column names
            
        Returns:
            List of corresponding database column names
        """
        result = []
        for display_name in display_columns:
            col = Columns.from_display_name(display_name)
            if col:
                result.append(col.db_name)
        return result
    
    def convert_to_display_columns(self, db_columns: List[str]) -> List[str]:
        """
        Convert database column names to display column names.
        
        Args:
            db_columns: List of database column names
            
        Returns:
            List of corresponding display column names
        """
        result = []
        for db_name in db_columns:
            col = Columns.from_db_name(db_name)
            if col:
                result.append(col.display_name)
        return result
    
    def apply_user_preferences(self, columns: List[Column]) -> List[Dict[str, Any]]:
        """
        Apply user preferences to column definitions.
        
        This method applies user preferences such as custom display names,
        column order, and visibility to the provided columns.
        
        Args:
            columns: List of Column objects to apply preferences to
            
        Returns:
            List of column definitions with user preferences applied
        """
        result = []
        
        # Get user preferences for column display
        custom_names = self.user_preferences.get('custom_names', {})
        column_order = self.user_preferences.get('column_order', [])
        hidden_columns = self.user_preferences.get('hidden_columns', [])
        
        # Create column definitions with user preferences applied
        for col_obj in columns:
            # Skip hidden columns
            if col_obj.id in hidden_columns:
                continue

            # Create column definition
            col_def = {
                'db_name': col_obj.db_name,
                'display_name': custom_names.get(col_obj.id, col_obj.display_name),
                'editable': col_obj.editable,
                'width': col_obj.width,
                'description': col_obj.description
            }
            result.append(col_def)
        
        # Sort columns according to user preference if specified
        if column_order:
            # Create a mapping of column names to their position in the order
            order_map = {name: idx for idx, name in enumerate(column_order)}
            
            # Sort the result based on the order map
            result.sort(key=lambda x: order_map.get(x['db_name'], float('inf')))
        
        return result
    
    def get_editable_columns(self) -> List[Column]:
        """
        Get columns that are editable by the user.

        Returns:
            List of columns that are editable
        """
        return self.get_columns_by_usage('user_editable')
    
    def get_column_widths(self) -> Dict[str, int]:
        """
        Get column widths for UI display.
        
        Returns:
            Dictionary mapping display names to widths
        """
        # Start with standard widths
        widths = {col.display_name: col.width for col in Columns}
        
        # Apply user preferences if any
        custom_widths = self.user_preferences.get('column_widths', {})
        widths.update(custom_widths)
        
        return widths
    
    def update_user_preferences(self, preferences: Dict[str, Any]) -> None:
        """
        Update user preferences.
        
        Args:
            preferences: Dictionary of user preferences to update
        """
        self.user_preferences.update(preferences)
    
    def get_df_with_display_columns(self, df: pd.DataFrame, only_columns_with_data: bool = True) -> pd.DataFrame:
        """
        Get a DataFrame with display column names.
        
        This converts the database column names in the DataFrame to display names
        and optionally filters to only include columns with data.
        
        Args:
            df: DataFrame with database column names
            only_columns_with_data: If True, only include columns with data
            
        Returns:
            DataFrame with display column names
        """
        # Make a copy to avoid modifying the original
        result_df = df.copy()
        
        # Get columns to include
        if only_columns_with_data:
            columns = self.get_display_columns_with_data(df)
        else:
            columns = self.get_display_columns()
        
        # Filter to only include display columns that exist in the DataFrame
        db_names = [col.db_name for col in columns if col.db_name in df.columns]
        
        # Create a mapping of db names to display names
        name_mapping = {col.db_name: col.display_name for col in columns}
        
        # Filter and rename columns
        result_df = result_df[db_names].rename(columns=name_mapping)
        
        return result_df

    @staticmethod
    def convert_transactions_to_dataframe(transactions: List[Transaction], ensure_columns: Optional[List[str]] = None) -> pd.DataFrame:
        """
        Convert a list of Transaction objects to a pandas DataFrame.

        This is a utility function to shape in-memory data. For fetching data
        from the database as a DataFrame, use DBIOService.

        Args:
            transactions: List of Transaction objects.
            ensure_columns: Optional list of database column names to ensure exist in the DataFrame.

        Returns:
            A pandas DataFrame with db_names as columns.
        """
        from fm.core.services.logger import log
        
        log(f"convert_transactions_to_dataframe called with {len(transactions)} transactions", level="info")
        log(f"ensure_columns: {ensure_columns}", level="debug")
        
        if not transactions:
            log("No transactions provided to convert_transactions_to_dataframe", level="warning")
            # If ensure_columns is provided, return an empty DataFrame with those columns
            if ensure_columns:
                log(f"Returning empty DataFrame with columns: {ensure_columns}", level="debug")
                return pd.DataFrame(columns=ensure_columns)
            log("Returning empty DataFrame with no columns", level="debug")
            return pd.DataFrame()

        # Log first transaction as sample
        if transactions:
            log(f"First transaction type: {type(transactions[0])}", level="debug")
            log(f"First transaction attributes: {dir(transactions[0])}", level="debug")
            log(f"First transaction dict: {getattr(transactions[0], '__dict__', 'No __dict__')}", level="debug")
            log(f"First transaction str: {str(transactions[0])}", level="debug")

        # Convert transactions to a list of dictionaries using the to_dict method
        try:
            data = []
            for i, tx in enumerate(transactions):
                tx_dict = tx.to_dict()
                log(f"Transaction {i} dict: {tx_dict}", level="debug")
                data.append(tx_dict)
            
            log(f"Created data list with {len(data)} items", level="debug")
            
            df = pd.DataFrame(data)
            log(f"Created DataFrame with shape: {df.shape}, columns: {df.columns.tolist()}", level="debug")
            
            # Log sample data if available
            if not df.empty:
                log(f"Sample data:\n{df.head(1).to_string()}", level="debug")
            
            # Ensure all required columns exist, adding any that are missing
            if ensure_columns:
                log(f"Ensuring columns exist: {ensure_columns}", level="debug")
                for col in ensure_columns:
                    if col not in df.columns:
                        log(f"Adding missing column: {col}", level="debug")
                        df[col] = None
            
            log(f"Final DataFrame columns: {df.columns.tolist()}", level="debug")
            return df
            
        except Exception as e:
            log(f"Error converting transactions to DataFrame: {str(e)}", level="error")
            log(exc_info=True)
            raise
