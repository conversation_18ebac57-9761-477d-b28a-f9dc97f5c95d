# Categorize Module – Initial Scaffold & Processing-Shim Design

_Last updated: 2025-06-15_

## 1  Goals
1. Deliver a working **Categorize** module quickly.  
2. Avoid risky refactors now, but prepare for a cleaner shared core later.  
3. Provide a **single import point** for statement-processing logic so future moves touch only that shim.

## 2  Directory Layout (after scaffold)
```
fm/
├── statement_processing/          # NEW shim/alias package
│   ├── __init__.py                # re-exports update_data.utils APIs
│   └── (empty – real code lives in update_data for now)
└── modules/
    ├── update_data/               # existing importer module
    └── categorize/
        ├── core/
        │   ├── processor.py       # calls statement_processing.process_files()
        │   ├── categorizer.py     # pattern matcher
        │   └── models.py          # Transaction dataclass helpers
        ├── _view/
        │   └── cat_view.py        # Qt table view
        ├── categorize_presenter.py
        ├── resources/
        │   └── patterns.json
        └── scaffold_design.md     # (this document)
```

## 3  The Shim API (`fm.statement_processing`)
Purpose: act as a stable facade.  
For MVP it simply forwards to `fm.modules.update_data.utils.dw_director` (and friends).  
Later we physically move the utils here and delete the indirection.

```python
# fm/statement_processing/__init__.py

"""Statement-processing façade. Import this *everywhere* instead of the
legacy update_data.utils modules. Later we can move the real code here
without touching callers."""

from fm.modules.update_data.utils.dw_director import process_files  # public API

__all__ = [
    "process_files",
]
```

*Optionally we can also re-export other helpers:* `load_csv_file`, `statement_handlers`, etc., as needed by future code.

## 4  Categorize Core Components
1. **processor.py**  
   ```python
   from fm.statement_processing import process_files
   from .categorizer import TransactionCategorizer

   def categorize_files(file_paths):
       df = process_files(file_paths)  # existing pipeline → DataFrame
       tc = TransactionCategorizer()
       df["category"] = df.apply(tc.categorize_row, axis=1)
       return df
   ```

2. **categorizer.py** – loads `resources/patterns.json`, performs regex / substring matching, returns category or "Uncategorised".
3. **models.py** – convenience dataclass wrappers if/when needed.

## 5  Presenter & View Responsibilities
Presenter loads files (or pulls from DB), calls `categorize_files`, pushes results to DB, and emits signals for UI refresh.  
View displays a table with editable category cells and save button.

## 6  Implementation Roadmap
1. Create **shim package** (`statement_processing`) with minimal re-export.  
2. Scaffold empty files & folders as per layout above.  
3. Register `categorize` module in `ModuleCoordinator`.  
4. Implement basic view that simply loads DataFrame rows into a `QTableView`.  
5. Wire category edits to DB.  
6. Iteratively enhance categoriser patterns and UI.

## 7  Future Cleanup
After MVP is stable:  
* Move `update_data/utils` code into `fm/statement_processing/` proper.  
* Update `update_data` imports to the shim (one place).  
* Remove shim re-export layer.

---
*End of document*
