# Statement Handler Identification Report

**Date:** 2025-07-12

## 1. Problem

The `KiwibankBasicCSVHandler` incorrectly identifies files from other banks, causing `ValueError` exceptions during processing due to format mismatches. The current identification logic is too permissive.

## 2. Solution: Signature-Based Identification

Implement a generic, signature-based `can_handle_file` method in the base `StatementHandler`. This makes handlers purely declarative, defining *what* they handle, not *how*.

The method will use a prioritised sequence of checks based on attributes defined in each handler:

1.  **Account Number in Content (Highest Confidence):** Search file preview for an account number matching the handler's `account.pattern`.
2.  **Account Number in Filename + Column Count (High Confidence):** Match the account number in the filename *and* verify the total column count.
3.  **Column Headers (Medium Confidence):** Match the column headers in the file against the handler's `source_col_names`.

This layered approach provides a robust, elegant, and maintainable solution, centralising identification logic in the base class.

## 3. Next Steps

1.  Implement the proposed `can_handle_file` logic in `_base_statement_handler.py`.
2.  Ensure all handlers have accurate attributes for the new logic to consume.
3.  Run the test suite to validate the fix.

## 4. Code Examples

Here is what the refined implementation would look like, incorporating boolean flags and a 'hook' for bespoke filename logic.

### 1. The `MatchAttributes` Dataclass

The dataclass uses simple booleans to declare which checks to perform.

```python
# In: fm.modules.update_data.utils.statement_handlers._base_statement_handler.py

@dataclass
class MatchAttributes:
    """Declarative signature for file identification using boolean flags."""
    acc_pattern_in_metadata: bool = False
    acc_pattern_in_data: bool = False
    acc_pattern_in_filename: bool = False
    col_names_match: bool = False
    n_cols_match: bool = False
```

### 2. The Final `can_handle_file` Method

This method combines a high-confidence check for account numbers in the data with flexible, declarative fallback checks. It correctly handles various CSV header formats by using the `colnames_in_header` attribute.

```python
# In: StatementHandler class in _base_statement_handler.py

class StatementHandler(ABC):
    # ... existing attributes ...
    statement_attrs: ClassVar[StatementAttributes]
    match_attrs: ClassVar[MatchAttributes] = MatchAttributes() # Default

    @classmethod
    def can_handle_file(cls, filepath: str) -> bool:
        """Check if handler can process file using a robust, signature-based matching strategy."""
        if not os.path.exists(filepath) or os.path.getsize(filepath) == 0:
            return False

        col_attrs = cls.statement_attrs.columns_attrs
        acc_attrs = cls.statement_attrs.account_attrs
        match_cfg = cls.match_attrs

        # --- 1. High-Confidence Check: Account Number in Data ---
        if match_cfg.acc_pattern_in_data and acc_attrs.pattern and acc_attrs.in_data:
            try:
                # Read a preview without assuming headers to get raw cell value
                df_preview = pd.read_csv(filepath, header=None, nrows=acc_attrs.location[0] + 1, encoding=col_attrs.file_encoding, skip_blank_lines=True)
                if df_preview is not None and not df_preview.empty:
                    cell_value = str(df_preview.iat[acc_attrs.location[0], acc_attrs.location[1]])
                    if re.search(acc_attrs.pattern, cell_value):
                        return True  # Strong match, accept immediately
            except Exception as e:
                log.debug(f"[{cls.__name__}] Failed account pattern check for {os.path.basename(filepath)}: {e}")

        # --- 2. Fallback Checks: All other specified conditions must pass ---
        checks_to_pass = {}

        # Check column count
        if match_cfg.n_cols_match:
            try:
                df_preview = pd.read_csv(filepath, header=None, nrows=5, encoding=col_attrs.file_encoding, skip_blank_lines=True)
                checks_to_pass['n_cols'] = (len(df_preview.columns) == col_attrs.n_source_cols)
            except Exception:
                checks_to_pass['n_cols'] = False

        # Check column names
        if match_cfg.col_names_match and col_attrs.has_col_names and col_attrs.source_col_names:
            try:
                if col_attrs.colnames_in_header:
                    df_header = pd.read_csv(filepath, header=col_attrs.col_names_row, nrows=1, encoding=col_attrs.file_encoding, skip_blank_lines=True)
                    actual_headers = {str(h).strip().lower() for h in df_header.columns}
                else: # Headers are in a data row
                    df_header = pd.read_csv(filepath, header=None, nrows=col_attrs.col_names_row + 1, encoding=col_attrs.file_encoding, skip_blank_lines=True)
                    actual_headers = {str(h).strip().lower() for h in df_header.iloc[col_attrs.col_names_row]}
                
                expected_headers = {str(h).strip().lower() for h in col_attrs.source_col_names}
                checks_to_pass['col_names'] = expected_headers.issubset(actual_headers)
            except Exception:
                checks_to_pass['col_names'] = False

        # Check account number in filename
        if match_cfg.acc_pattern_in_filename and acc_attrs.pattern and acc_attrs.in_file_name:
            try:
                filename = os.path.basename(filepath)
                checks_to_pass['acc_in_filename'] = bool(re.search(acc_attrs.pattern, filename, re.IGNORECASE))
            except Exception:
                checks_to_pass['acc_in_filename'] = False

        # If no checks were configured to run, it's not a match.
        if not checks_to_pass:
            return False

        # All configured checks must have passed.
        return all(checks_to_pass.values())
```

### 3. Example Handlers

The handlers remain simple and declarative.

```python
# In: kiwibank_basic_csv_handler.py
class KiwibankBasicCSVHandler(StatementHandler):
    # ... attributes ...
    account = AccountNumberAttributes(
        pattern=r'Bank\s+12;\s*Branch\s+(\d+);\s*Account\s+(\d+-?\d+)', # For data
        in_metadata=True,
        location=(1, 0)
    )
    match_attrs = MatchAttributes(
        acc_pattern_in_metadata=True, # Highest confidence check
        n_cols_match=True,         # Fallback check
    )

# In: asb_standard_csv_handler.py
class ASBStandardCSVHandler(StatementHandler):
    # ... attributes defined here ...
    account = AccountNumberAttributes(
        pattern=r'Bank\s+12;\s*Branch\s+(\d+);\s*Account\s+(\d+-?\d+)', # For data
        in_metadata=True,
        location=(1, 0),
        filename_pattern=r'Export-(\d+-\d+).csv' # For filename
    )
    match_attrs = MatchAttributes(
        acc_pattern_in_data=True,
        acc_pattern_in_filename=True,
        col_names_match=True,
    )
```

## 5. Next Steps

1.  Implement the proposed `can_handle_file` logic and `MatchAttributes` class in `_base_statement_handler.py`.
2.  Update all handlers to use the new `match_attrs` system.
3.  Run the test suite to validate the solution.
