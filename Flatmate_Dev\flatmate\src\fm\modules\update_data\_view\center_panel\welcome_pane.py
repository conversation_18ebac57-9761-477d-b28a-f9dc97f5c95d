"""
Welcome pane component for the Update Data module.
"""

from PySide6.QtCore import Qt
from PySide6.QtGui import QFont
from PySide6.QtWidgets import QLabel, QVBoxLayout

from fm.gui._shared_components.base.base_pane import BasePane


class WelcomePane(BasePane):
    """Pane component for displaying welcome information."""
    
    def __init__(self, parent=None):
        """Initialize the welcome pane."""
        super().__init__(parent)
        self._init_ui()
    
    def _init_ui(self):
        """Initialize the UI components."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Title
        self.title_label = QLabel("Welcome to Update Data")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        self.title_label.setFont(title_font)
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.title_label)
        
        # Add some space
        layout.addSpacing(20)
        
        # Description
        self.description = QLabel(
            "This module helps you process and update your data files.\n\n"
            "Steps:\n"
            "1. Select your source files or folder\n"
            "2. Choose a save location\n"
            "3. Click Process to begin"
        )
        self.description.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.description)
        
        # Add stretch to push content to the top
        layout.addStretch()
    
    def show_component(self):
        """Show this component."""
        self.show()
    
    def hide_component(self):
        """Hide this component."""
        self.hide()
    
    def set_description(self, text: str):
        """Set the description text.
        
        Args:
            text: Description text to display
        """
        self.description.setText(text)
