#!/usr/bin/env python3
"""
Test script to identify and verify fixes for the filter persistence issues.
"""

import sys
import os
import pandas as pd
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PySide6.QtCore import Qt

# Add the flatmate source to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'flatmate', 'src'))

from fm.gui._shared_components.table_view_v2.fm_table_view import CustomTableView_v2
from fm.gui._shared_components.table_view_v2.components.table_config_v2 import TableConfig


class FilterTestWindow(QMainWindow):
    """Test window for filter issues."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Filter Issues Test")
        self.setGeometry(100, 100, 1000, 700)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Create test data with realistic transaction data
        self.test_data = pd.DataFrame({
            'date': ['2025-01-01', '2025-01-02', '2025-01-03', '2025-01-04', '2025-01-05', '2025-01-06'],
            'details': ['Coffee Shop Purchase', 'Grocery Store', 'Gas Station', 'Coffee Shop Refund', 'Restaurant Bill', 'Online Shopping'],
            'amount': [4.50, -85.20, -45.00, 4.50, -32.75, -125.99],
            'account': ['Checking', 'Checking', 'Credit Card', 'Checking', 'Credit Card', 'Savings'],
            'tags': ['Food', 'Groceries', 'Transport', 'Food', 'Food', 'Shopping']
        })
        
        # Create table view with persistence enabled
        self.table_view = CustomTableView_v2()
        
        # Create config with filter persistence
        config = TableConfig(
            save_filter_state=True,
            default_filter_column="details",
            last_filter_column="details",  # Simulate saved state
            last_filter_pattern="Coffee"   # Simulate saved pattern
        )
        
        self.table_view.configure(
            auto_size_columns=True,
            max_column_width=40,
            show_toolbar=True
        )
        
        # Apply the config with saved filter state
        self.table_view._config = config
        
        # Set the test data
        self.table_view.set_dataframe(self.test_data)
        layout.addWidget(self.table_view)
        
        # Add test controls
        controls_layout = QVBoxLayout()
        
        # Status label
        self.status_label = QLabel("Testing filter issues...")
        controls_layout.addWidget(self.status_label)
        
        # Test buttons
        test_default_button = QPushButton("Test Default Column Selection")
        test_default_button.clicked.connect(self.test_default_column)
        controls_layout.addWidget(test_default_button)
        
        test_restoration_button = QPushButton("Test Filter Restoration")
        test_restoration_button.clicked.connect(self.test_filter_restoration)
        controls_layout.addWidget(test_restoration_button)
        
        test_performance_button = QPushButton("Test All Columns Performance")
        test_performance_button.clicked.connect(self.test_all_columns_performance)
        controls_layout.addWidget(test_performance_button)
        
        test_and_logic_button = QPushButton("Test AND Logic: 'Coffee Shop'")
        test_and_logic_button.clicked.connect(lambda: self.test_filter_logic("Coffee Shop"))
        controls_layout.addWidget(test_and_logic_button)
        
        layout.addLayout(controls_layout)
        
    def test_default_column(self):
        """Test if the default column is set correctly."""
        try:
            if hasattr(self.table_view.toolbar, 'filter_group'):
                filter_group = self.table_view.toolbar.filter_group
                column_selector = filter_group.column_selector
                
                # Check what columns are available
                available_columns = []
                for i in range(column_selector.count()):
                    item_text = column_selector.itemText(i)
                    item_data = column_selector.itemData(i)
                    available_columns.append(f"{item_text} ({item_data})")
                
                current_selection = column_selector.currentData()
                current_text = column_selector.currentText()
                
                self.status_label.setText(
                    f"Default column test:\n"
                    f"Available: {', '.join(available_columns)}\n"
                    f"Current: {current_text} ({current_selection})\n"
                    f"Expected: Details (details)"
                )
                
                print(f"Available columns: {available_columns}")
                print(f"Current selection: {current_text} ({current_selection})")
                
            else:
                self.status_label.setText("Error: Filter group not found")
                
        except Exception as e:
            self.status_label.setText(f"Error testing default column: {e}")
            print(f"Error: {e}")
            import traceback
            traceback.print_exc()
    
    def test_filter_restoration(self):
        """Test if filter restoration works correctly."""
        try:
            config = self.table_view._config
            
            # Check config state
            config_info = (
                f"Config state:\n"
                f"save_filter_state: {config.save_filter_state}\n"
                f"default_filter_column: {config.default_filter_column}\n"
                f"last_filter_column: {config.last_filter_column}\n"
                f"last_filter_pattern: {config.last_filter_pattern}"
            )
            
            # Check UI state
            if hasattr(self.table_view.toolbar, 'filter_group'):
                filter_group = self.table_view.toolbar.filter_group
                current_column, current_pattern = filter_group.get_filter_state()
                
                ui_info = (
                    f"\nUI state:\n"
                    f"Current column: {current_column}\n"
                    f"Current pattern: {current_pattern}"
                )
                
                # Check if filter is actually applied
                proxy_model = self.table_view.table_view.model()
                visible_rows = proxy_model.rowCount()
                total_rows = len(self.test_data)
                
                filter_info = (
                    f"\nFilter application:\n"
                    f"Total rows: {total_rows}\n"
                    f"Visible rows: {visible_rows}\n"
                    f"Filter applied: {'Yes' if visible_rows < total_rows else 'No'}"
                )
                
                self.status_label.setText(config_info + ui_info + filter_info)
                
            else:
                self.status_label.setText(config_info + "\nError: Filter group not found")
                
        except Exception as e:
            self.status_label.setText(f"Error testing restoration: {e}")
            print(f"Error: {e}")
            import traceback
            traceback.print_exc()
    
    def test_all_columns_performance(self):
        """Test performance of All Columns search."""
        try:
            if hasattr(self.table_view.toolbar, 'filter_group'):
                filter_group = self.table_view.toolbar.filter_group
                
                # Set to All Columns
                filter_group.column_selector.set_selected_column("all_columns")
                
                # Apply a filter and measure response
                import time
                start_time = time.time()
                
                filter_group.filter_input.setText("Coffee")
                
                # Wait a moment for processing
                QApplication.processEvents()
                
                end_time = time.time()
                response_time = (end_time - start_time) * 1000  # Convert to milliseconds
                
                proxy_model = self.table_view.table_view.model()
                visible_rows = proxy_model.rowCount()
                
                self.status_label.setText(
                    f"All Columns performance test:\n"
                    f"Response time: {response_time:.2f}ms\n"
                    f"Visible rows: {visible_rows}\n"
                    f"Performance: {'Good' if response_time < 100 else 'Slow'}"
                )
                
            else:
                self.status_label.setText("Error: Filter group not found")
                
        except Exception as e:
            self.status_label.setText(f"Error testing performance: {e}")
            print(f"Error: {e}")
            import traceback
            traceback.print_exc()
    
    def test_filter_logic(self, pattern):
        """Test filter logic with specific pattern."""
        try:
            if hasattr(self.table_view.toolbar, 'filter_group'):
                filter_group = self.table_view.toolbar.filter_group
                
                # Set to details column
                filter_group.column_selector.set_selected_column("details")
                
                # Apply the pattern
                filter_group.filter_input.setText(pattern)
                
                # Check results
                proxy_model = self.table_view.table_view.model()
                visible_rows = proxy_model.rowCount()
                
                # Get visible data for verification
                visible_data = []
                for row in range(visible_rows):
                    details_index = proxy_model.index(row, 1)  # Assuming details is column 1
                    details_value = proxy_model.data(details_index, Qt.DisplayRole)
                    visible_data.append(details_value)
                
                self.status_label.setText(
                    f"Filter logic test '{pattern}':\n"
                    f"Visible rows: {visible_rows}\n"
                    f"Visible data: {', '.join(visible_data) if visible_data else 'None'}"
                )
                
            else:
                self.status_label.setText("Error: Filter group not found")
                
        except Exception as e:
            self.status_label.setText(f"Error testing filter logic: {e}")
            print(f"Error: {e}")
            import traceback
            traceback.print_exc()


def main():
    """Main test function."""
    app = QApplication(sys.argv)
    
    # Create and show test window
    window = FilterTestWindow()
    window.show()
    
    print("=== Filter Issues Test ===")
    print("1. Test Default Column Selection - should default to 'Details'")
    print("2. Test Filter Restoration - should restore saved filter state")
    print("3. Test All Columns Performance - should not be laggy")
    print("4. Test AND Logic - should show only rows with both terms")
    
    # Run the application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
