#!/usr/bin/env python3
"""
UI Preview tool with dark mode enabled.
Usage: python preview_ui.py path/to/ui/file.ui
"""

import sys
from pathlib import Path
from PySide6.QtWidgets import QApplication, QMainWindow
from PySide6.QtUiTools import QUiLoader
from PySide6.QtCore import Qt
from PySide6.QtGui import QPalette, QColor

def setup_dark_mode(app):
    """Setup dark mode palette."""
    app.setStyle("Fusion")
    palette = QPalette()
    palette.setColor(QPalette.Window, QColor(53, 53, 53))
    palette.setColor(QPalette.WindowText, Qt.white)
    palette.setColor(QPalette.Base, QColor(25, 25, 25))
    palette.setColor(QPalette.AlternateBase, QColor(53, 53, 53))
    palette.setColor(QPalette.ToolTipBase, Qt.white)
    palette.setColor(QPalette.ToolTipText, Qt.white)
    palette.setColor(QPalette.Text, Qt.white)
    palette.setColor(QPalette.Button, QColor(53, 53, 53))
    palette.setColor(QPalette.ButtonText, Qt.white)
    palette.setColor(QPalette.BrightText, Qt.red)
    palette.setColor(QPalette.Link, QColor(42, 130, 218))
    palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
    palette.setColor(QPalette.HighlightedText, Qt.black)
    app.setPalette(palette)

def main():
    """Main preview function."""
    if len(sys.argv) < 2:
        print("Usage: python preview_ui.py path/to/ui/file.ui")
        sys.exit(1)
        
    ui_file = Path(sys.argv[1])
    if not ui_file.exists():
        print(f"Error: UI file not found: {ui_file}")
        sys.exit(1)
        
    app = QApplication(sys.argv)
    setup_dark_mode(app)
    
    # Load and show the UI
    loader = QUiLoader()
    window = loader.load(str(ui_file))
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
