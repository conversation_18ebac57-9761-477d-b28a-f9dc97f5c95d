"""
Shared GUI components for the Flatmate application.

This package provides reusable UI components that maintain consistency
across different modules and provide common functionality.

Organized by category:
- base: Abstract base classes and interfaces
- widgets: Concrete UI components (buttons, forms, etc.)
- panes: Reusable pane components for panels
"""

from .base import (
    BasePanelComponent,
    BasePane
)
from .widgets import (
    LabeledCheckBox,
    ActionButton,
    SecondaryButton,
    ExitButton,
    OptionMenuWithLabel,
    OptionMenuWithLabelAndButton
)
from .panes import (
    DevSettingsPaneCore,
    DevSettingsPaneWithButton
)

__all__ = [
    # Base classes
    'BasePanelComponent',
    'BasePane',

    # Widgets
    'LabeledCheckBox',
    'ActionButton',
    'SecondaryButton',
    'ExitButton',
    'OptionMenuWithLabel',
    'OptionMenuWithLabelAndButton',

    # Panes
    'DevSettingsPaneCore',
    'DevSettingsPaneWithButton'
]
