"""
Column Order Service - Manages column ordering with user preference support.

This service implements the hybrid preference system:
- per-module user preference → global user preference → FM-standard default
"""

from typing import List, Optional
from pathlib import Path
import yaml

from .column_order import StandardColumnsOrder


class ColumnOrderService:
    """
    Service for managing column order with user preference hierarchy.
    
    Order Resolution Priority:
    1. Per-module user preference (e.g., 'categorize.display.column_order')
    2. Global user preference (e.g., 'display.column_order_global') 
    3. FM-standard default (from StandardColumnsOrder enum)
    """
    
    def __init__(self, preferences_path: Optional[Path] = None):
        """
        Initialize the column order service.
        
        Args:
            preferences_path: Path to user preferences file. 
                            Defaults to ~/.flatmate/preferences.yaml
        """
        if preferences_path is None:
            preferences_path = Path.home() / '.flatmate' / 'preferences.yaml'
        
        self.preferences_path = preferences_path
        self._preferences_cache = None
    
    def _load_preferences(self) -> dict:
        """Load user preferences from YAML file."""
        if self._preferences_cache is not None:
            return self._preferences_cache
            
        if not self.preferences_path.exists():
            self._preferences_cache = {}
            return self._preferences_cache
        
        try:
            with open(self.preferences_path, 'r', encoding='utf-8') as f:
                self._preferences_cache = yaml.safe_load(f) or {}
        except Exception as e:
            print(f"Warning: Could not load preferences from {self.preferences_path}: {e}")
            self._preferences_cache = {}
        
        return self._preferences_cache
    
    def _save_preferences(self, preferences: dict):
        """Save preferences to YAML file."""
        try:
            # Ensure directory exists
            self.preferences_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.preferences_path, 'w', encoding='utf-8') as f:
                yaml.safe_dump(preferences, f, default_flow_style=False, sort_keys=True)
            
            # Update cache
            self._preferences_cache = preferences
            
        except Exception as e:
            print(f"Error saving preferences to {self.preferences_path}: {e}")
            raise
    
    def get_column_order(self, module_name: str = None) -> List[str]:
        """
        Get column order for a module following preference hierarchy.
        
        Args:
            module_name: Module name (e.g., 'categorize'). If None, returns global order.
            
        Returns:
            List of column names (db_names) in display order
        """
        preferences = self._load_preferences()
        
        # 1. Try per-module user preference
        if module_name:
            module_key = f"{module_name}.display.column_order"
            if module_key in preferences:
                return preferences[module_key].copy()
        
        # 2. Try global user preference  
        global_key = "display.column_order_global"
        if global_key in preferences:
            return preferences[global_key].copy()
        
        # 3. Fall back to FM-standard default
        return self.get_default_order()
    
    def set_column_order(self, column_order: List[str], module_name: str = None):
        """
        Set column order preference.
        
        Args:
            column_order: List of column names (db_names) in desired order
            module_name: Module name for per-module preference. 
                        If None, sets global preference.
        """
        preferences = self._load_preferences()
        
        if module_name:
            key = f"{module_name}.display.column_order"
        else:
            key = "display.column_order_global"
        
        preferences[key] = column_order.copy()
        self._save_preferences(preferences)
    
    def get_default_order(self) -> List[str]:
        """
        Get the FM-standard default column order.
        
        Returns:
            List of column names (db_names) in FM-standard order
        """
        # Convert enum names to db_names (lowercase)
        ordered_enum_names = StandardColumnsOrder.get_ordered_column_names()
        return [name.lower() for name in ordered_enum_names]
    
    def reset_to_default(self, module_name: str = None):
        """
        Reset column order to FM-standard default.
        
        Args:
            module_name: Module name to reset. If None, resets global preference.
        """
        preferences = self._load_preferences()
        
        if module_name:
            key = f"{module_name}.display.column_order"
        else:
            key = "display.column_order_global"
        
        # Remove the preference key to fall back to default
        if key in preferences:
            del preferences[key]
            self._save_preferences(preferences)
    
    def sort_columns_by_order(self, column_names: List[str], module_name: str = None) -> List[str]:
        """
        Sort a list of column names according to the order for a module.
        
        Args:
            column_names: List of column names to sort
            module_name: Module name for preference lookup
            
        Returns:
            Sorted list of column names
        """
        order = self.get_column_order(module_name)
        
        # Create order mapping
        order_map = {name: idx for idx, name in enumerate(order)}
        
        # Sort columns, unknown columns go to end
        def get_sort_key(col_name: str) -> int:
            return order_map.get(col_name, 999)
        
        return sorted(column_names, key=get_sort_key)
    
    def get_user_preference(self, module_name: str = None) -> Optional[List[str]]:
        """
        Get the current user preference (without falling back to default).
        
        Args:
            module_name: Module name to check
            
        Returns:
            User preference list or None if no preference is set
        """
        preferences = self._load_preferences()
        
        if module_name:
            key = f"{module_name}.display.column_order"
            return preferences.get(key)
        else:
            key = "display.column_order_global"
            return preferences.get(key)
    
    def has_user_preference(self, module_name: str = None) -> bool:
        """
        Check if user has set a preference for column order.
        
        Args:
            module_name: Module name to check
            
        Returns:
            True if user preference exists
        """
        return self.get_user_preference(module_name) is not None
