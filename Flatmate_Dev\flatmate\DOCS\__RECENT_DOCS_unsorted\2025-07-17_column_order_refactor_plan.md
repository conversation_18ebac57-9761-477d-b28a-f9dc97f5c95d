# Column Order Refactor Plan

## Objective
Refactor the column ordering system to provide a robust, explicit, and user-friendly mechanism for both FM-standard and user-customised column orders, supporting both global and per-module preferences.

---

## Key Principles
- **Explicit FM-standard order:** Defined via Enum and Columns registry.
- **User preference:** Hybrid config system (global default and per-module overrides).
- **Separation of concerns:** Order logic encapsulated in a dedicated service/helper.
- **Developer-friendly:** Minimal boilerplate, clear extension path.

---

## Steps

### 1. Define Canonical Order
- [ ] Create a new Enum (e.g. `StandardColumnsOrder`) reflecting FM-standard column order.
- [ ] Update the `Columns` registry to reference the Enum for canonical order.

### 2. Refactor Column Registry
- [ ] Ensure all columns are registered in a single source of truth (the registry).
- [ ] Add any missing columns to Enum and registry.

### 3. Implement Order Resolution Service
- [ ] Create a `ColumnOrderService` (or similar) to:
    - Resolve order using: per-module user preference → global user preference → FM-standard default
    - Provide API for UI/backend to fetch correct order
    - Provide API to update user preferences

### 4. Integrate with UI and Backend
- [ ] Update table and dropdown rendering to use the new service/helper for column order.
- [ ] Ensure UI updates user preferences when column order changes.

### 5. User Preference Storage
- [ ] Define config schema for user column order (YAML/JSON/local DB/etc)
- [ ] Implement logic to persist and retrieve user preferences (global and per-module)

### 6. Testing & Validation
- [ ] Unit tests for order resolution logic
- [ ] Integration tests for UI and backend
- [ ] Manual verification for default, global, and per-module scenarios

### 7. Documentation
- [ ] Update developer docs to cover new system
- [ ] Update user-facing docs/settings UI if applicable

---

## Notes & Risks
- Backwards compatibility: migrate any existing user prefs if needed
- Ensure order logic is DRY and not duplicated in UI/backend
- Keep FM-standard order as the fallback for all scenarios

---

*Draft created 2025-07-17. Refine as implementation proceeds.*
