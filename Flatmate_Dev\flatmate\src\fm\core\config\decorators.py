import tomli_w
from pathlib import Path
import inspect

def generate_module_config(config_dir='configs'):
    def decorator(cls):
        def write_default_config(self):
            # Get the module's directory
            module_dir = Path(inspect.getfile(self.__class__)).parent
            
            # Create configs directory if it doesn't exist
            config_path = module_dir / config_dir
            config_path.mkdir(parents=True, exist_ok=True)
            
            # Get all class attributes that are not methods or private
            config_data = {
                k: v for k, v in self.__dict__.items() 
                if not k.startswith('_') and not callable(v)
            }
            
            # Write to TOML file
            default_config_path = config_path / 'defaults.toml'
            with open(default_config_path, 'wb') as f:
                tomli_w.dump(config_data, f)
            
            return config_data

        # Add method to class
        cls.write_default_config = write_default_config
        return cls
    
    return decorator
