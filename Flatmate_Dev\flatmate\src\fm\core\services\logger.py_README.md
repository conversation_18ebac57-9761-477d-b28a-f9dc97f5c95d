# Core Services

This directory contains core services that provide shared functionality across the Flatmate application.

## Services

### Logger (`logger.py`)

The logger service provides application-wide logging with accurate module detection and flexible output options.

#### Features
- **Accurate module detection** via stack inspection
- **Multiple log levels** (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- **Dual output** to both console and file
- **Event bus integration** for log event notifications
- **Clean, readable output** with timestamps and module names

#### Usage

**Basic logging:**
```python
from fm.core.services.logger import log

log.info("Application started")
log.warning("Configuration file not found, using defaults")
log.error("Failed to connect to database")
```

**With different log levels:**
```python
from fm.core.services.logger import log

log.debug("Detailed debugging information")
log.info("General information")
log.warning("Warning message")
log.error("Error occurred")
log.critical("Critical system error")
```

**Using decorators:**
```python
from fm.core.services.logger import log_this, LogLevel

@log_this(LogLevel.DEBUG)
def process_data(data):
    """This function call will be automatically logged."""
    return processed_data
```

**Direct logging functions:**
```python
from fm.core.services.logger import log_debug, log_info, log_warning, log_error, log_critical

log_info("Using direct logging function")
log_error("Something went wrong")
```

#### Configuration

The logger respects the global configuration system:

```python
# In your config
ConfigKeys.Logging.LEVEL = 'logging.level'  # Controls minimum log level
ConfigKeys.Logging.FILE_PATH = 'logging.file_path'  # Log file location
```

#### Output Format

Console and file output format:
```
[2025-06-15 02:23:45] [INFO] [fm.gui.main_window] Application initialized successfully
[2025-06-15 02:23:46] [WARNING] [fm.modules.update_data] No data files found in directory
[2025-06-15 02:23:47] [ERROR] [fm.core.config] Failed to load preferences: File not found
```

#### Module Detection

The logger automatically detects the calling module using stack inspection:
- Accurately identifies the source module/file
- Works across different call depths
- Handles edge cases and fallbacks gracefully

#### Event Integration

The logger integrates with the global event bus:
- Emits log events for other components to listen to
- Allows for custom log handling and filtering
- Supports reactive logging features

#### Best Practices

1. **Use appropriate log levels:**
   - `DEBUG`: Detailed diagnostic information
   - `INFO`: General application flow
   - `WARNING`: Unexpected but recoverable situations
   - `ERROR`: Error conditions that don't stop the application
   - `CRITICAL`: Serious errors that may cause the application to stop

2. **Keep messages concise but informative:**
   ```python
   # Good
   log.info(f"Loaded {len(data)} records from {filename}")
   
   # Avoid
   log.info("Loading data...")  # Too vague
   log.info(f"Loading data from file {filename} with {len(data)} records and processing with options {options}...")  # Too verbose
   ```

3. **Use structured logging for complex data:**
   ```python
   log.info(f"User action: {action}, target: {target}, result: {result}")
   ```

4. **Don't log sensitive information:**
   ```python
   # Good
   log.info(f"User {user_id} logged in successfully")
   
   # Avoid
   log.info(f"User {username} logged in with password {password}")
   ```

#### Troubleshooting

**Logger not working:**
- Check that the logger is properly imported
- Verify log level configuration allows your messages
- Ensure log file directory exists and is writable

**Module detection issues:**
- The logger uses stack inspection to determine the calling module
- If module names appear incorrect, check the call stack depth
- Fallback behavior shows generic module names

**Performance considerations:**
- Logging is generally fast but avoid excessive debug logging in tight loops
- File I/O is handled efficiently but consider log rotation for long-running applications
- Event bus integration adds minimal overhead

#### Recent Changes

**2025-06-15 Refactor:**
- Simplified logger implementation for better readability
- Removed Python logging backend dependency for clarity
- Improved module detection accuracy
- Consolidated imports and removed side effects
- Made directory creation explicit rather than hidden
- Removed verbose debug output during startup

The logger is now cleaner, more reliable, and easier to understand while maintaining all essential functionality.
