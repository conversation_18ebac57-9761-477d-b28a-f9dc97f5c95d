#!/usr/bin/env python3
"""
File Date Renamer

A utility script to rename files by appending their modification/creation date in ISO format.
Accepts files via file dialog, command-line arguments, or drag-and-drop.
"""

import sys
import shutil
import argparse
import tkinter as tk
from tkinter import filedialog, messagebox
from pathlib import Path
from datetime import datetime
from typing import List, Tuple, Optional, Literal


def get_file_date(file_path: Path, date_type: Literal['modified', 'created'] = 'modified') -> str:
    """Get the specified date of a file in YYYY-MM-DD format.
    
    Args:
        file_path: Path to the file
        date_type: Either 'modified' (default) or 'created'
    """
    if date_type == 'created':
        timestamp = file_path.stat().st_ctime
    else:  # modified
        timestamp = file_path.stat().st_mtime
    return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d')


def rename_file_with_date(
    file_path: Path, 
    date_type: Literal['modified', 'created'] = 'modified',
    date_position: Literal['prefix', 'suffix'] = 'prefix',
    dry_run: bool = True
) -> Tuple[bool, str, str]:
    """
    Rename a file by appending its modification/creation date.
    
    Args:
        file_path: Path to the file
        date_type: Whether to use 'modified' or 'created' date
        date_position: Whether to add date as 'prefix' or 'suffix'
        dry_run: If True, only show what would be done
        
    Returns:
        Tuple of (success, original_path, new_path)
    """
    if not file_path.exists():
        return False, str(file_path), "Error: File does not exist"
    
    if not file_path.is_file():
        return False, str(file_path), "Error: Not a regular file"
    
    # Get file parts
    parent = file_path.parent
    name = file_path.stem
    ext = file_path.suffix
    
    # Get date and construct new filename
    date_str = get_file_date(file_path, date_type)
    
    # Handle existing date in filename
    name_parts = name.rsplit('_', 1)
    if len(name_parts) > 1 and name_parts[1].replace('-', '').isdigit():
        name = name_parts[0]
    
    # Create new filename based on position
    if date_position == 'prefix':
        new_name = f"{date_str}_{name}{ext}"
    else:  # suffix
        new_name = f"{name}_{date_str}{ext}"
    new_path = parent / new_name
    
    # Skip if already has date prefix
    if file_path.name.startswith(f"{date_str}_"):
        return True, str(file_path), "Already has date prefix, skipping"
    
    # Check for existing file with same name
    if new_path.exists():
        return False, str(file_path), f"Error: {new_path} already exists"
    
    # Perform the rename
    if not dry_run:
        try:
            file_path.rename(new_path)
            return True, str(file_path), str(new_path)
        except Exception as e:
            return False, str(file_path), f"Error: {str(e)}"
    else:
        return True, str(file_path), str(new_path)


def process_files(
    file_paths: List[str],
    date_type: str = 'modified',
    date_position: str = 'prefix',
    dry_run: bool = True
) -> None:
    """Process multiple files with feedback.
    
    Args:
        file_paths: List of file paths to process
        date_type: Type of date to use ('modified' or 'created')
        date_position: Where to place the date ('prefix' or 'suffix')
        dry_run: If True, only show what would be done
    """
    print(f"\n{'DRY RUN - No changes will be made' if dry_run else 'RENAMING FILES'}")
    print(f"Date type: {date_type}, Position: {date_position}\n")
    print("-" * 120)
    print(f"{'Original Path':<80} | Status")
    print("-" * 120)
    
    success_count = 0
    failure_count = 0
    
    print(f"\nProcessing {len(file_paths)} files...")
    print("-" * 50)
    
    for file_path in file_paths:
        path = Path(file_path).resolve()
        success, original, message = rename_file_with_date(
            path, 
            date_type=date_type,
            date_position=date_position,
            dry_run=dry_run
        )
        
        if success and "skipping" not in message.lower():
            print(f"✓ {original} -> {message}")
            success_count += 1
        elif "skipping" in message.lower():
            print(f"⏩ {original}: {message}")
        else:
            print(f"✗ {original}: {message}")
            failure_count += 1
    
    print("-" * 50)
    print(f"Summary:")
    print(f"  Successfully processed: {success_count}")
    print(f"  Skipped: {len(file_paths) - success_count - failure_count}")
    print(f"  Failed: {failure_count}")
    if dry_run:
        print("\nNote: This was a dry run. No files were actually modified.")
        print("      Check the output above and run with --apply to actually rename files.")


def select_files_gui() -> List[str]:
    """Open a file dialog to select multiple files."""
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    
    file_paths = filedialog.askopenfilenames(
        title="Select files to rename",
        filetypes=[("All files", "*.*")]
    )
    
    return list(file_paths)


def show_gui() -> None:
    """Show the GUI for file selection and options."""
    root = tk.Tk()
    root.title("File Date Renamer")
    
    # Set window to start maximized
    root.state('zoomed')  # This will maximize the window
    
    # Set minimum size
    root.minsize(800, 600)
    
    # Set window icon if available
    try:
        root.iconbitmap(default='icon.ico')  # Optional: add an icon if you have one
    except:
        pass  # Continue without icon if not found
    
    # Variables
    date_type = tk.StringVar(value="modified")
    date_position = tk.StringVar(value="prefix")
    apply_changes = tk.BooleanVar(value=False)
    selected_files = []
    
    def update_file_list():
        """Update the file list display."""
        file_list.delete(0, tk.END)
        for i, file_path in enumerate(selected_files, 1):
            file_list.insert(tk.END, f"{i}. {Path(file_path).name}")
        file_count.set(f"Selected files: {len(selected_files)}")
    
    def select_files():
        """Open file dialog to select files."""
        nonlocal selected_files
        files = filedialog.askopenfilenames(
            title="Select files to rename",
            filetypes=[("All files", "*.*")]
        )
        if files:
            selected_files = list(files)
            update_file_list()
    
    def process_selected():
        """Process the selected files with current options."""
        if not selected_files:
            messagebox.showwarning("No Files", "Please select files first!")
            return
            
        if not apply_changes.get():
            if not messagebox.askyesno(
                "Dry Run", 
                "You're about to do a dry run (no changes will be made).\n\n"
                "Do you want to continue?"
            ):
                return
        else:
            if not messagebox.askyesno(
                "Confirm", 
                f"You're about to rename {len(selected_files)} files.\n\n"
                "This action cannot be undone.\n\n"
                "Are you sure you want to continue?"
            ):
                return
        
        # Process files in the console
        root.destroy()
        process_files(
            selected_files,
            date_type=date_type.get(),
            date_position=date_position.get(),
            dry_run=not apply_changes.get()
        )
        input("\nPress Enter to exit...")
    
    # UI Elements
    tk.Label(root, text="File Date Renamer", font=("Arial", 14, "bold")).pack(pady=10)
    
    # File selection
    file_frame = tk.LabelFrame(root, text="Files", padx=10, pady=10)
    file_frame.pack(fill="both", expand=True, padx=10, pady=5)
    
    file_list = tk.Listbox(file_frame, height=8)
    file_scroll = tk.Scrollbar(file_frame, orient="vertical", command=file_list.yview)
    file_list.configure(yscrollcommand=file_scroll.set)
    
    file_scroll.pack(side="right", fill="y")
    file_list.pack(side="left", fill="both", expand=True)
    
    file_count = tk.StringVar(value="Selected files: 0")
    tk.Label(file_frame, textvariable=file_count).pack(side="bottom", anchor="w")
    
    # Options frame
    options_frame = tk.LabelFrame(root, text="Options", padx=10, pady=10)
    options_frame.pack(fill="x", padx=10, pady=5)
    
    # Date type
    tk.Label(options_frame, text="Use date:").grid(row=0, column=0, sticky="w", padx=5, pady=2)
    tk.Radiobutton(options_frame, text="Modified Date", variable=date_type, value="modified").grid(row=0, column=1, sticky="w", padx=5, pady=2)
    tk.Radiobutton(options_frame, text="Created Date", variable=date_type, value="created").grid(row=0, column=2, sticky="w", padx=5, pady=2)
    
    # Date position
    tk.Label(options_frame, text="Date position:").grid(row=1, column=0, sticky="w", padx=5, pady=2)
    tk.Radiobutton(options_frame, text="Prefix (YYYY-MM-DD_filename)", variable=date_position, value="prefix").grid(row=1, column=1, columnspan=2, sticky="w", padx=5, pady=2)
    tk.Radiobutton(options_frame, text="Suffix (filename_YYYY-MM-DD)", variable=date_position, value="suffix").grid(row=2, column=1, columnspan=2, sticky="w", padx=5, pady=2)
    
    # Apply changes
    tk.Checkbutton(options_frame, text="Apply changes (disable for dry run)", variable=apply_changes).grid(row=3, column=0, columnspan=3, sticky="w", padx=5, pady=5)
    
    # Buttons
    button_frame = tk.Frame(root)
    button_frame.pack(fill="x", padx=10, pady=10)
    
    tk.Button(button_frame, text="Select Files", command=select_files).pack(side="left", padx=5)
    tk.Button(button_frame, text="Process Files", command=process_selected).pack(side="left", padx=5)
    tk.Button(button_frame, text="Exit", command=root.quit).pack(side="right", padx=5)
    
    # Start the GUI
    root.mainloop()


def main() -> None:
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Rename files by appending their modification/creation date."
    )
    parser.add_argument(
        "files", 
        nargs="*", 
        help="Optional: Files to process (if not provided, GUI will open)"
    )
    parser.add_argument(
        "--apply", 
        action="store_true",
        help="Actually rename files (default: dry run)"
    )
    parser.add_argument(
        "--date",
        choices=["modified", "created"],
        default="modified",
        help="Which date to use: 'modified' (default) or 'created'"
    )
    parser.add_argument(
        "--position",
        choices=["prefix", "suffix"],
        default="prefix",
        help="Where to place the date: 'prefix' (default) or 'suffix'"
    )
    
    args = parser.parse_args()
    
    # If no files provided as arguments, show the GUI
    if not args.files and sys.stdin.isatty():
        show_gui()
    else:
        # Process files from command line
        process_files(
            args.files,
            date_type=args.date,
            date_position=args.position,
            dry_run=not args.apply
        )


if __name__ == "__main__":
    # Handle drag and drop by checking if we're running directly (not imported)
    try:
        main()
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        sys.exit(0)
    except Exception as e:
        print(f"\nAn error occurred: {e}", file=sys.stderr)
        if hasattr(e, '__traceback__'):
            import traceback
            traceback.print_exc()
        sys.exit(1)
