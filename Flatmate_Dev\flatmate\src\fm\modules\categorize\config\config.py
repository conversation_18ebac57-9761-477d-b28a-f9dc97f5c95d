"""Configuration manager for the Categorize module.

This is a practical config system based on actual usage patterns:
- No speculative defaults - only keys that are actually used exist
- No complex enum systems - just simple string keys where they're used
- Source tracking for debugging and traceability
- Auto-generated YAML from real usage patterns
- Simple import pattern: from .config import config

Philosophy:
- Config keys are defined WHERE they're used, not in separate enum files
- Discoverability through actual usage, not speculation
- Self-documenting code - you see what config is used where
- Natural refactoring - change keys where they're used

Usage:
    from .config import config

    # Define defaults where you use them
    config.ensure_defaults({
        'categorize.display.description_width': 200,
        'categorize.display.date_width': 100
    })

    # Use the values
    width = config.get_value('categorize.display.description_width')
"""

from pathlib import Path
from fm.core.config.base_local_config_v2 import BaseLocalConfigV2


class CategorizeConfig(BaseLocalConfigV2):
    """Configuration manager for categorize module using the enhanced V2 base class.

    This inherits all the enhanced functionality from BaseLocalConfigV2:
    - Override hierarchy (user prefs > component defaults > runtime defaults)
    - Source tracking for debugging
    - Usage-based config definition
    - Auto-generated documentation
    """

    # Required by BaseLocalConfigV2
    MODULE_NAME = "categorize"

    def get_defaults_file_path(self) -> Path:
        """Get the path to the categorize defaults.yaml file."""
        return Path(__file__).parent / "defaults.yaml"


# Create the global instance
config = CategorizeConfig()

# Export for easy import
__all__ = ['config']
