# This system was chaotic and messy. 
I have attempted, with the help of AI, to siplify and clean it up.
The new system is in fm/core/config 
The wrapper is fm/core/config.py 
Each module has it's own local_config.py whic wraps the core.config.py
And inherits its methods and attributes

The flow is:

Core config manages all settings/paths centrally
Each module has its own config wrapper
Module code uses its local wrapper to access core config
This is cleaner because:

Modules don't need to know about core config details
Each module's config needs are isolated
Core config provides consistent storage/retrieval
Would you like me to:

Show how to simplify this further?
Or explain any specific part in more detail?

# the main reason I did this is I didnt like the messy imports, and some modules may need config that is not relevant anywhere else... although Id like the central confid toml to have all relevant switches 

