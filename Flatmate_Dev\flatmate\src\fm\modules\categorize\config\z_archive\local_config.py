"""Configuration manager for the Categorize module."""

import inspect
from pathlib import Path
from typing import Dict, Any

from fm.core.config.base_local_config import BaseLocalConfig
from fm.core.services.event_bus import global_event_bus, Events
from .cat_keys import CatKeys


class CatConfig(BaseLocalConfig[CatKeys]):
    """Enhanced configuration manager for categorize module.

    Key improvements:
    1. NO speculative defaults - only keys that are actually ensure_defaults() exist
    2. Tracks where each config key was set (for YAML traceability)
    3. Simple import pattern: from .config import config
    4. Cross-module sharing support
    """

    def __init__(self):
        # Track where config keys are set from
        self._key_origins = {}

        super().__init__()
        self.events = global_event_bus

        # Log initialization
        self.events.publish(
            Events.LOG_EVENT,
            {
                "level": "DEBUG",
                "module": "categorize.config",
                "message": "Categorize Configuration initialized (no speculative defaults)",
            },
        )

    def get_defaults(self) -> dict:
        """Override to return EMPTY - no speculative defaults!

        All defaults must be explicitly set via ensure_defaults() calls
        throughout the codebase where they're actually needed.
        """
        return {}

    def get_defaults_file_path(self) -> Path:
        """Get the path to the Categorize defaults.yaml file."""
        return Path(__file__).parent / "defaults.yaml"

    def ensure_defaults(self, defaults_dict: Dict[str, Any], source: str = None) -> None:
        """Enhanced ensure_defaults that tracks where keys come from.

        Args:
            defaults_dict: Dictionary of default key-value pairs to ensure
            source: Optional source description (auto-detected if None)
        """
        # Auto-detect source from call stack if not provided
        if source is None:
            frame = inspect.currentframe().f_back
            filename = frame.f_code.co_filename
            line_number = frame.f_lineno
            function_name = frame.f_code.co_name

            # Create readable source description
            file_path = Path(filename)
            relative_path = str(file_path).replace(str(Path(__file__).parent.parent.parent.parent), "")
            source = f"{relative_path}:{function_name}():{line_number}"

        # Track origins and ensure defaults
        for key, default_value in defaults_dict.items():
            # Only set if the key doesn't already exist
            if self.get_value(key) is None:
                self.set_value(key, default_value)

                # Track where this key was set
                self._key_origins[key] = {
                    'source': source,
                    'default_value': default_value,
                    'module': 'categorize'
                }

                self.events.publish(
                    Events.LOG_EVENT,
                    {
                        "level": "DEBUG",
                        "module": "categorize.config",
                        "message": f"Created config key: {key} = {default_value} (from {source})",
                    },
                )

    def get_key_origins(self) -> Dict[str, Dict[str, Any]]:
        """Get information about where each config key was set."""
        return self._key_origins.copy()

    def generate_documented_yaml(self) -> str:
        """Generate YAML with comments showing where each key was set."""
        lines = ["# Categorize Module Configuration", "# Auto-generated from actual usage", ""]

        # Group keys by namespace
        namespaces = {}
        for key, origin in self._key_origins.items():
            parts = key.split('.')
            if len(parts) >= 2:
                namespace = '.'.join(parts[:-1])
                if namespace not in namespaces:
                    namespaces[namespace] = []
                namespaces[namespace].append((key, origin))

        for namespace, keys in sorted(namespaces.items()):
            lines.append(f"# {namespace}")
            for key, origin in keys:
                lines.append(f"# Set by: {origin['source']}")
                yaml_key = key.replace(f"{namespace}.", "")
                value = self.get_value(key)
                lines.append(f"{yaml_key}: {value}")
                lines.append("")

        return "\n".join(lines)


# Global instance
cat_config = CatConfig()
