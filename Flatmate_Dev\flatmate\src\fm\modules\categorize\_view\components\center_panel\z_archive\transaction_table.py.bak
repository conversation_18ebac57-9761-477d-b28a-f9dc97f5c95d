"""DEPRECATED: Transaction table component for the Categorize module.

⚠️  THIS FILE IS DEPRECATED ⚠️

Use TransactionViewPanel instead (transaction_view_panel.py)
This file contains the old basic QTableWidget implementation.
The new TransactionViewPanel uses EnhancedTableWidget with:
- Top bar with filtering controls
- Column management and resizing
- Export functionality
- Better integration with the column manager system

TODO: Remove this file once all references are updated.
"""

import pandas as pd
from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import QTableWidget, QTableWidgetItem, QHeaderView

from fm.modules.categorize.config import config


class TransactionTable(QTableWidget):
    """Table widget for displaying and editing transactions."""
    
    # Signals
    transaction_selected = Signal(int)
    tags_updated = Signal(int, str)
    
    def __init__(self, parent=None):
        """Initialize the transaction table."""
        super().__init__(parent)

        # Ensure config defaults for table display FIRST
        config.ensure_defaults({
            'categorize.display.description_width': 40,
            'categorize.display.date_width': 12,
            'categorize.display.amount_width': 12,
            'categorize.display.account_width': 15,
            'categorize.display.tags_width': 20,
            'categorize.display.date_format': '%Y-%m-%d',
            'categorize.display.amount_decimals': 2,
            'categorize.display.default_columns': ['Date', 'Description', 'Amount', 'Account', 'Tags'],
            'categorize.display.truncate_description': True,
            'categorize.display.truncate_suffix': '...'
        })

        # Get config values BEFORE calling _init_ui
        self.description_width = config.get_value('categorize.display.description_width')
        self.date_format = config.get_value('categorize.display.date_format')
        self.amount_decimals = config.get_value('categorize.display.amount_decimals')
        self.default_columns = config.get_value('categorize.display.default_columns')
        self.truncate_description = config.get_value('categorize.display.truncate_description')
        self.truncate_suffix = config.get_value('categorize.display.truncate_suffix')

        # Now initialize UI with config values available
        self._init_ui()
        self._connect_signals()

    def _init_ui(self):
        """Initialize the UI components."""
        # Set up table properties using config
        self.setColumnCount(len(self.default_columns))
        self.setHorizontalHeaderLabels(self.default_columns)
        self.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setEditTriggers(QTableWidget.DoubleClicked | QTableWidget.EditKeyPressed)
    
    def _connect_signals(self):
        """Connect internal signals."""
        self.cellClicked.connect(self._on_cell_clicked)
        self.cellChanged.connect(self._on_cell_changed)
    
    def set_transactions(self, df: pd.DataFrame):
        """Set the transactions dataframe to display."""
        # Temporarily block signals to prevent cellChanged during setup
        self.blockSignals(True)
        
        # Clear existing data
        self.setRowCount(0)
        
        # Add rows
        self.setRowCount(len(df))
        
        # Fill data
        for row_idx, (_, row) in enumerate(df.iterrows()):
            # Date - use configured format
            date_value = row.get('date', '')
            if hasattr(date_value, 'strftime'):
                date_str = date_value.strftime(self.date_format)
            else:
                date_str = str(date_value)
            date_item = QTableWidgetItem(date_str)
            date_item.setFlags(date_item.flags() & ~Qt.ItemIsEditable)
            self.setItem(row_idx, 0, date_item)

            # Description - use configured truncation
            desc = row.get('description', '')
            if self.truncate_description and len(desc) > self.description_width:
                desc = desc[:self.description_width-len(self.truncate_suffix)] + self.truncate_suffix
            desc_item = QTableWidgetItem(desc)
            desc_item.setFlags(desc_item.flags() & ~Qt.ItemIsEditable)
            self.setItem(row_idx, 1, desc_item)

            # Amount - use configured decimal places
            amount_format = f"{{:.{self.amount_decimals}f}}"
            amount_item = QTableWidgetItem(amount_format.format(row.get('amount', 0)))
            amount_item.setFlags(amount_item.flags() & ~Qt.ItemIsEditable)
            self.setItem(row_idx, 2, amount_item)
            
            # Account
            account_item = QTableWidgetItem(row.get('account', ''))
            account_item.setFlags(account_item.flags() & ~Qt.ItemIsEditable)
            self.setItem(row_idx, 3, account_item)
            
            # Tags - editable
            tags_item = QTableWidgetItem(row.get('tags', ''))
            self.setItem(row_idx, 4, tags_item)
            
            # Store transaction ID as data
            self.setItem(row_idx, 0, QTableWidgetItem(str(row.get('id', row_idx))))
            self.item(row_idx, 0).setData(Qt.UserRole, row.get('id', row_idx))
        
        # Re-enable signals
        self.blockSignals(False)
    
    def _on_cell_clicked(self, row, column):
        """Handle cell click event."""
        # Get transaction ID from the first column's user data
        transaction_id = self.item(row, 0).data(Qt.UserRole)
        self.transaction_selected.emit(transaction_id)
    
    def _on_cell_changed(self, row, column):
        """Handle cell edit event."""
        # Only process tag column edits
        if column == 4:
            transaction_id = self.item(row, 0).data(Qt.UserRole)
            new_tags = self.item(row, column).text()
            self.tags_updated.emit(transaction_id, new_tags)
    
    def clear(self):
        """Clear the table data."""
        self.setRowCount(0)

    def disconnect_signals(self):
        """Disconnect all signals for cleanup."""
        try:
            self.cellClicked.disconnect()
            self.cellChanged.disconnect()
            self.transaction_selected.disconnect()
            self.tags_updated.disconnect()
        except (TypeError, RuntimeError):
            # Signals may already be disconnected or never connected
            pass


