# Database Feature Pattern

## Overview
This document defines the standard pattern for implementing database features in the FlatMate application. This pattern builds upon the existing widget pattern and provides a consistent approach to database interactions, configuration, and resource management.

## Core Problems Solved
- **Inconsistent database access** - Varying patterns for database operations
- **Resource leaks** - Unreleased database connections and cursors
- **Configuration complexity** - Scattered and inconsistent configuration
- **Testing challenges** - Difficult to mock and test database interactions
- **Error handling** - Inconsistent error handling and reporting

## Design Principles

### 1. Separation of Concerns
- **Configuration** - Runtime behavior and defaults
- **Data Access** - Database connections and queries
- **Business Logic** - Feature-specific operations
- **Resource Management** - Connection and transaction handling

### 2. Consistent API Surface
- Standardized method signatures
- Clear public vs. internal interfaces
- Type hints and comprehensive docstrings

### 3. Lifecycle Management
- Explicit initialization and cleanup
- Context manager support
- Transaction management

### 4. Testability
- Dependency injection
- Mockable components
- Isolated test cases

## Base Implementation

### BaseDatabaseFeature
```python
from abc import ABC, abstractmethod
from contextlib import contextmanager
from typing import Any, Dict, Optional, TypeVar, Generic

T = TypeVar('T')

class BaseDatabaseFeature(ABC, Generic[T]):
    """Base class for all database features.
    
    Provides common functionality for database operations including:
    - Configuration management
    - Resource lifecycle
    - Error handling
    - Transaction management
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize with optional configuration."""
        self._config = self._get_default_config()
        if config:
            self.configure(**config)
        self._is_initialized = False
        self._setup()
    
    @classmethod
    def _get_default_config(cls) -> Dict[str, Any]:
        """Return default configuration for this feature."""
        return {
            'log_level': 'INFO',
            'auto_initialize': True,
            'auto_cleanup': True,
            'auto_commit': True,
        }
    
    def configure(self, **kwargs) -> 'BaseDatabaseFeature':
        """Update feature configuration."""
        for key, value in kwargs.items():
            if key in self._config:
                self._config[key] = value
            else:
                raise ValueError(f"Unknown configuration option: {key}")
        return self
    
    def initialize(self) -> 'BaseDatabaseFeature':
        """Initialize database resources."""
        if not self._is_initialized:
            self._initialize()
            self._is_initialized = True
        return self
    
    def cleanup(self) -> None:
        """Clean up database resources."""
        if self._is_initialized:
            self._cleanup()
            self._is_initialized = False
    
    @contextmanager
    def transaction(self):
        """Context manager for database transactions."""
        self.begin_transaction()
        try:
            yield self
            if self._config['auto_commit']:
                self.commit()
        except Exception:
            self.rollback()
            raise
    
    def begin_transaction(self) -> None:
        """Begin a new transaction."""
        self._begin_transaction()
    
    def commit(self) -> None:
        """Commit the current transaction."""
        self._commit()
    
    def rollback(self) -> None:
        """Roll back the current transaction."""
        self._rollback()
    
    # Abstract methods
    @abstractmethod
    def _setup(self) -> None:
        """Set up feature resources."""
        pass
    
    @abstractmethod
    def _initialize(self) -> None:
        """Initialize database connection."""
        pass
    
    @abstractmethod
    def _cleanup(self) -> None:
        """Clean up database resources."""
        pass
    
    @abstractmethod
    def _begin_transaction(self) -> None:
        """Begin a new transaction."""
        pass
    
    @abstractmethod
    def _commit(self) -> None:
        """Commit the current transaction."""
        pass
    
    @abstractmethod
    def _rollback(self) -> None:
        """Roll back the current transaction."""
        pass
    
    # Context manager support
    def __enter__(self):
        if self._config['auto_initialize']:
            self.initialize()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self._config['auto_cleanup']:
            self.cleanup()
```

## SQLite Implementation

### SQLiteRepository
```python
import sqlite3
from pathlib import Path
from typing import Any, Dict, List, Optional, TypeVar

from fm.core.config import config
from fm.core.services.logger import log

T = TypeVar('T', bound='BaseDatabaseFeature')

class SQLiteRepository(BaseDatabaseFeature[T]):
    """SQLite implementation of a database repository."""
    
    @classmethod
    def _get_default_config(cls) -> Dict[str, Any]:
        """Return default configuration for SQLite repository."""
        return {
            **super()._get_default_config(),
            'db_path': None,  # Will use default if None
            'auto_create_tables': True,
            'auto_migrate': True,
            'journal_mode': 'WAL',
            'synchronous': 'NORMAL',
            'foreign_keys': True,
            'timeout': 30.0,
        }
    
    def _setup(self) -> None:
        """Set up repository resources."""
        self._connection = None
        self._in_transaction = False
    
    def _initialize(self) -> None:
        """Initialize database connection and ensure schema exists."""
        if self._connection is not None:
            return
            
        # Get database path from config or use default
        if not self._config['db_path']:
            data_dir = config.get_path('data')
            self._config['db_path'] = data_dir / 'transactions.db'
        
        # Ensure directory exists
        self._config['db_path'].parent.mkdir(parents=True, exist_ok=True)
        
        # Connect to database
        self._connect()
        
        # Initialize schema if needed
        if self._config['auto_create_tables']:
            self._ensure_schema_exists()
            
        # Run migrations if needed
        if self._config['auto_migrate']:
            self._run_migrations()
    
    def _connect(self) -> None:
        """Establish database connection with configured settings."""
        self._connection = sqlite3.connect(
            str(self._config['db_path']),
            timeout=self._config['timeout'],
            detect_types=sqlite3.PARSE_DECLTYPES | sqlite3.PARSE_COLNAMES
        )
        self._connection.row_factory = sqlite3.Row
        
        # Configure connection
        with self._connection:
            self._connection.execute(f"PRAGMA journal_mode = {self._config['journal_mode']}")
            self._connection.execute(f"PRAGMA synchronous = {self._config['synchronous']}")
            self._connection.execute(f"PRAGMA foreign_keys = {'ON' if self._config['foreign_keys'] else 'OFF'}")
    
    def _cleanup(self) -> None:
        """Clean up database resources."""
        if self._connection is not None:
            if self._in_transaction:
                try:
                    self._connection.rollback()
                except sqlite3.Error:
                    pass  # Connection might already be closed
            self._connection.close()
            self._connection = None
    
    def _begin_transaction(self) -> None:
        """Begin a new transaction."""
        if self._in_transaction:
            raise RuntimeError("Transaction already in progress")
        self._connection.execute("BEGIN")
        self._in_transaction = True
    
    def _commit(self) -> None:
        """Commit the current transaction."""
        if not self._in_transaction:
            raise RuntimeError("No transaction in progress")
        self._connection.commit()
        self._in_transaction = False
    
    def _rollback(self) -> None:
        """Roll back the current transaction."""
        if not self._in_transaction:
            return
        self._connection.rollback()
        self._in_transaction = False
    
    def _ensure_schema_exists(self) -> None:
        """Ensure database tables exist."""
        # Implementation for creating tables based on models
        pass
    
    def _run_migrations(self) -> None:
        """Run any pending database migrations."""
        # Implementation for database migrations
        pass
```

## Transaction Repository Implementation

### TransactionRepository
```python
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Optional, TypeVar

from fm.core.models import Transaction

T = TypeVar('T', bound='Transaction')

class TransactionRepository(Generic[T]):
    """Interface for transaction data storage."""
    
    @abstractmethod
    def add_transactions(self, transactions: List[T]) -> Dict[str, int]:
        """Add new transactions to the repository."""
        pass
    
    @abstractmethod
    def get_transactions(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        account: Optional[str] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None
    ) -> List[T]:
        """Retrieve transactions matching the filters."""
        pass
    
    @abstractmethod
    def update_transaction(self, transaction_id: int, data: Dict) -> bool:
        """Update a transaction by ID."""
        pass
    
    @abstractmethod
    def delete_transaction(self, transaction_id: int) -> bool:
        """Delete a transaction by ID."""
        pass
    
    @abstractmethod
    def get_statistics(self) -> Dict[str, Any]:
        """Get statistics about stored transactions."""
        pass
```

## Database Service Implementation

### DatabaseService
```python
from pathlib import Path
from typing import Any, Dict, List, Optional, Type, TypeVar

from fm.core.models import Transaction

T = TypeVar('T', bound='BaseDatabaseFeature')

class DatabaseService(BaseDatabaseFeature):
    """High-level database service with feature-based design."""
    
    @classmethod
    def _get_default_config(cls) -> Dict[str, Any]:
        """Return default configuration for database service."""
        return {
            **super()._get_default_config(),
            'repository_class': None,  # Must be set by subclass
            'repository_config': None,
        }
    
    def _setup(self) -> None:
        """Set up service resources."""
        if self._config['repository_class'] is None:
            raise ValueError("repository_class must be specified in configuration")
        self._repository = None
    
    def _initialize(self) -> None:
        """Initialize repository."""
        if self._repository is None:
            repo_config = self._config.get('repository_config', {})
            self._repository = self._config['repository_class'](repo_config)
            
            if self._config['auto_initialize']:
                self._repository.initialize()
    
    def _cleanup(self) -> None:
        """Clean up repository resources."""
        if self._repository is not None and self._config['auto_cleanup']:
            self._repository.cleanup()
            self._repository = None
    
    # Transaction management
    def _begin_transaction(self) -> None:
        self._repository.begin_transaction()
    
    def _commit(self) -> None:
        self._repository.commit()
    
    def _rollback(self) -> None:
        self._repository.rollback()
    
    # High-level operations
    def import_csv(self, file_path: Path, mapping: Optional[Dict[str, str]] = None) -> Dict[str, int]:
        """Import transactions from a CSV file."""
        if not self._is_initialized:
            self.initialize()
            
        # Implementation...
        pass
    
    def export_csv(self, output_path: Path, **filters) -> None:
        """Export transactions to a CSV file."""
        if not self._is_initialized:
            self.initialize()
            
        # Implementation...
        pass
    
    def get_transactions(self, **filters) -> List[Transaction]:
        """Retrieve transactions matching the filters."""
        if not self._is_initialized:
            self.initialize()
            
        return self._repository.get_transactions(**filters)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get database statistics."""
        if not self._is_initialized:
            self.initialize()
            
        return self._repository.get_statistics()
```

## Example Usage

### Configuration
```python
# config/database.py
from pathlib import Path

from fm.core.config import config

# Get data directory from config
data_dir = config.get_path('data')

# Database configuration
DATABASE_CONFIG = {
    'db_path': data_dir / 'transactions.db',
    'auto_create_tables': True,
    'auto_migrate': True,
    'journal_mode': 'WAL',
    'synchronous': 'NORMAL',
}

# Repository configuration
REPOSITORY_CONFIG = {
    'repository_class': 'fm.core.database.SQLiteTransactionRepository',
    'repository_config': DATABASE_CONFIG,
}
```

### Feature Implementation
```python
# features/transactions/transaction_service.py
from pathlib import Path
from typing import Dict, List, Optional

from fm.core.database import DatabaseService
from fm.core.models import Transaction

class TransactionService(DatabaseService):
    """Service for transaction-related operations."""
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize with optional configuration."""
        default_config = {
            'repository_class': 'fm.core.database.SQLiteTransactionRepository',
        }
        if config:
            default_config.update(config)
        super().__init__(default_config)
    
    def import_from_bank_statement(
        self,
        file_path: Path,
        bank_name: str,
        account_id: str
    ) -> Dict[str, int]:
        """Import transactions from a bank statement."""
        if not self._is_initialized:
            self.initialize()
            
        # Implementation...
        pass
    
    def get_transactions_by_category(
        self,
        category: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[Transaction]:
        """Get transactions by category."""
        if not self._is_initialized:
            self.initialize()
            
        # Implementation...
        pass
```

### Usage Example
```python
# main.py
from pathlib import Path

from fm.core.config import load_config
from features.transactions.transaction_service import TransactionService

# Load configuration
config = load_config('config/database.py')

# Create and use the service
with TransactionService(config['REPOSITORY_CONFIG']) as service:
    # Import transactions
    result = service.import_from_bank_statement(
        file_path=Path('data/statement.csv'),
        bank_name='Example Bank',
        account_id='********'
    )
    
    # Query transactions
    transactions = service.get_transactions_by_category(
        category='Groceries',
        start_date='2023-01-01',
        end_date='2023-12-31'
    )
    
    # Print results
    print(f"Imported {result['imported']} transactions")
    print(f"Found {len(transactions)} transactions in category 'Groceries'")
```

## Best Practices

### 1. Configuration Management
- Use hierarchical configuration with sensible defaults
- Document all configuration options
- Validate configuration values
- Support environment variable overrides

### 2. Error Handling
- Use specific exception types
- Provide meaningful error messages
- Log errors with context
- Implement proper cleanup on failure

### 3. Testing
- Use dependency injection for testability
- Mock external dependencies
- Test error conditions
- Use in-memory SQLite for fast tests

### 4. Performance
- Use connection pooling
- Implement batching for bulk operations
- Use transactions appropriately
- Monitor and optimize slow queries

### 5. Security
- Use parameterized queries to prevent SQL injection
- Validate all inputs
- Implement proper authentication and authorization
- Encrypt sensitive data

## Migration Guide

### From Old Implementation
1. **Update Dependencies**
   - Add new database dependencies
   - Remove deprecated code

2. **Update Configuration**
   - Convert old config to new format
   - Set up new configuration files

3. **Update Code**
   - Replace direct repository access with service calls
   - Update transaction management
   - Update error handling

4. **Testing**
   - Update unit tests
   - Add integration tests
   - Test migration paths

## Related Documents

- [Widget Pattern](./APP_WIDE_WIDGET_PATTERN.md)
- [Feature Development Guide](./FEATURE_DEVELOPMENT_GUIDE.md)
- [Coding Standards](../CODING_STANDARDS.md)
- [Testing Guidelines](../TESTING.md)
