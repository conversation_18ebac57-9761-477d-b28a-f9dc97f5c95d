"""Test script for the new config system.

This demonstrates realistic usage patterns where different classes and functions
call config.ensure_defaults() as they would in real code.

Key features being tested:
1. No speculative defaults
2. Traceable config origins
3. Simple import pattern
4. YAML generation from actual usage
"""

import sys
from pathlib import Path


# Add the src directory to the path so we can import our modules
src_path = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(src_path))


# Simulate real classes and functions that would use config
class CenterPanelComponent:
    """Simulates the center panel component."""

    def __init__(self):
        from fm.modules.categorize.config.config import config

        # Each component ensures its own defaults when it initializes
        config.ensure_defaults({
            'categorize.display.table_margin': 10,
            'categorize.display.show_grid_lines': True,
            'categorize.display.row_height': 25,
            'categorize.display.header_height': 30
        })

        self.config = config

    def setup_layout(self):
        """Setup method that needs more config values."""
        self.config.ensure_defaults({
            'categorize.display.scroll_bar_width': 15,
            'categorize.display.border_width': 1
        })


class TransactionTableWidget:
    """Simulates the transaction table widget."""

    def __init__(self):
        from fm.modules.categorize.config.config import config
        self.config = config

    def setup_columns(self):
        """Column setup needs specific widths."""
        self.config.ensure_defaults({
            'categorize.display.description_width': 200,
            'categorize.display.date_width': 100,
            'categorize.display.amount_width': 120,
            'categorize.display.category_width': 150
        })

    def configure_sorting(self):
        """Sorting configuration."""
        self.config.ensure_defaults({
            'categorize.table.default_sort_column': 'date',
            'categorize.table.sort_ascending': False,
            'categorize.table.allow_multi_sort': True
        })


class FilterPanelManager:
    """Simulates the filter panel."""

    def initialize(self):
        from fm.modules.categorize.config.config import config

        config.ensure_defaults({
            'categorize.filters.default_days': 30,
            'categorize.filters.remember_last_filter': True,
            'categorize.filters.show_advanced': False,
            'categorize.display.show_filter_panel': True  # Cross-namespace
        })

        return config


def setup_categorize_presenter():
    """Function that sets up the presenter."""
    from fm.modules.categorize.config.config import config

    config.ensure_defaults({
        'categorize.data.page_size': 100,
        'categorize.data.auto_refresh_seconds': 30,
        'categorize.performance.lazy_load': True
    })

    return config


def initialize_keyboard_shortcuts():
    """Function for keyboard shortcuts."""
    from fm.modules.categorize.config.config import config

    config.ensure_defaults({
        'categorize.shortcuts.save_key': 'Ctrl+S',
        'categorize.shortcuts.filter_key': 'Ctrl+F',
        'categorize.shortcuts.refresh_key': 'F5',
        'categorize.shortcuts.delete_key': 'Delete'
    })

def test_new_config_system():
    """Test the new config system with realistic usage."""
    print("🧪 Testing New Config System - Realistic Usage")
    print("=" * 60)

    try:
        # Import config to check initial state
        from fm.modules.categorize.config.config import config
        print("✅ Successfully imported config")

        # Check that we start with no speculative defaults
        origins = config.get_key_origins()
        print(f"📊 Initial config keys: {len(origins)}")

        # Simulate real component initialization
        print("\n🏗️  Initializing Components...")

        print("  📱 Creating CenterPanelComponent...")
        center_panel = CenterPanelComponent()
        center_panel.setup_layout()

        print("  � Creating TransactionTableWidget...")
        table = TransactionTableWidget()
        table.setup_columns()
        table.configure_sorting()

        print("  � Initializing FilterPanelManager...")
        filter_manager = FilterPanelManager()
        filter_manager.initialize()

        print("  🎯 Setting up CategorizePresenter...")
        setup_categorize_presenter()

        print("  ⌨️  Initializing keyboard shortcuts...")
        initialize_keyboard_shortcuts()

        # Show what we collected
        origins = config.get_key_origins()
        print(f"\n📊 Total config keys created: {len(origins)}")

        # Group by namespace for better display
        namespaces = {}
        for key, origin in origins.items():
            namespace = '.'.join(key.split('.')[:-1])
            if namespace not in namespaces:
                namespaces[namespace] = []
            namespaces[namespace].append((key, origin))

        print(f"\n📋 Config Keys by Namespace:")
        for namespace, keys in sorted(namespaces.items()):
            print(f"  🏷️  {namespace}: {len(keys)} keys")
            for key, origin in keys[:3]:  # Show first 3 as examples
                short_key = key.split('.')[-1]
                print(f"    • {short_key}: {origin['default_value']}")
            if len(keys) > 3:
                print(f"    ... and {len(keys) - 3} more")

        # Generate and save YAML
        print("\n💾 Generating defaults.yaml...")
        defaults_yaml_path = config.save_defaults_yaml()
        print(f"✅ Defaults YAML saved to: {defaults_yaml_path}")

        # Show a preview of the YAML
        yaml_content = config.generate_documented_yaml()
        lines = yaml_content.split('\n')
        print(f"\n� YAML Preview (first 15 lines):")
        for line in lines[:15]:
            print(f"  {line}")
        if len(lines) > 15:
            print(f"  ... and {len(lines) - 15} more lines")

        print("\n✅ All tests passed!")
        return True

    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_comparison_with_old_system():
    """Show the difference between old and new systems."""
    print("\n" + "=" * 50)
    print("📊 Comparison: Old vs New System")
    print("=" * 50)
    
    print("\n🔴 OLD SYSTEM PROBLEMS:")
    print("  ❌ Speculative keys defined in enums")
    print("  ❌ Hard to know which keys are actually used")
    print("  ❌ YAML files get out of sync")
    print("  ❌ No traceability of where keys come from")
    print("  ❌ Complex import paths")
    
    print("\n🟢 NEW SYSTEM BENEFITS:")
    print("  ✅ Only keys that are actually used exist")
    print("  ✅ Clear traceability of where each key was set")
    print("  ✅ YAML can be auto-generated from actual usage")
    print("  ✅ Simple import: from .config import config")
    print("  ✅ Cross-module sharing supported")
    print("  ✅ No speculation - only reality!")


if __name__ == "__main__":
    success = test_new_config_system()
    test_comparison_with_old_system()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 New config system is working perfectly!")
        print("💡 Ready to replace speculative config throughout the app")
    else:
        print("🔧 Need to fix issues before proceeding")
    
    sys.exit(0 if success else 1)
