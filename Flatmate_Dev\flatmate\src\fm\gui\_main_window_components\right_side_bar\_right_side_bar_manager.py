"""
Right side bar manager component for the application.
Manages the layout and components in the right side bar.
"""

from PySide6.QtCore import QObject, Signal, Qt
from PySide6.QtWidgets import QVBoxLayout, QSpacerItem, QSizePolicy, QWidget

from .nav_pane import NavPane
from .utilities_pane import UtilitiesPane


class RightSideBarManager(QObject):
    """
    Manager for the right side bar components and layout.
    Handles the arrangement of navigation and utilities panes.
    """
    # Forward signals from child components
    navigationSelected = Signal(str)
    utilitySelected = Signal(str)
    
    def __init__(self, parent_widget):
        """
        Initialize the right side bar manager.
        
        Args:
            parent_widget: The parent widget that will contain the side bar components
        """
        super().__init__(parent_widget)
        
        self.parent_widget = parent_widget
        
        # Use the existing layout that was prepared in the main window
        self.layout = parent_widget.layout()
        
        # Make sure we have a valid layout
        if not self.layout:
            # Create a new layout if none exists (shouldn't happen, but just in case)
            self.layout = QVBoxLayout(parent_widget)
            self.layout.setContentsMargins(0, 0, 0, 0)  # Zero margins for clean appearance
            self.layout.setSpacing(0)
            self.layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        
        # Ensure the parent widget has sufficient minimum width to display icons properly
        # The NavButton icon size is 32px, plus we need padding on both sides
        self.parent_widget.setMinimumWidth(50)
        
        # Create and add components
        self._setup_components()
        
    def _setup_components(self):
        """Set up the components in the right side bar."""
        # Create navigation pane
        self.nav_pane = NavPane()
        self.layout.addWidget(self.nav_pane)
        
        # Add minimal spacer for visual separation (just 4px)
        self.layout.addItem(
            QSpacerItem(0, 4, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed)
        )
        
        # Create utilities pane
        self.utilities_pane = UtilitiesPane()
        self.layout.addWidget(self.utilities_pane)
        
        # Add expanding spacer to push everything to the top
        self.layout.addItem(
            QSpacerItem(0, 0, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)
        )
        
        # Connect signals
        self.nav_pane.navigationSelected.connect(self._on_navigation_selected)
        self.utilities_pane.utilitySelected.connect(self.utilitySelected)
        
    def _on_navigation_selected(self, item_id):
        """Handle navigation selection from NavPane and forward the signal.
        
        Args:
            item_id: ID of the selected navigation item
        """
        print(f"[RightSideBarManager] Forwarding navigationSelected signal with item_id: '{item_id}'")
        self.navigationSelected.emit(item_id)
    
    def get_nav_pane(self):
        """Get the navigation pane instance."""
        return self.nav_pane
    
    def get_utilities_pane(self):
        """Get the utilities pane instance."""
        return self.utilities_pane
