"""
Enhanced Database I/O Service - High-level API for database operations.

This module provides a unified interface for database operations including:
- Import/export functionality (CSV, DataFrame)
- Query helpers for common operations
- Statistics and reporting functions
- Column management integration

It serves as the primary entry point for all database interactions,
wrapping the lower-level repository classes and using the ColumnManager
for all column-related operations.
"""

from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Tuple
import threading

import pandas as pd

from fm.core.services.logger import log
from fm.core.database.sql_repository.cached_sqlite_repository import CachedSQLiteRepository
from fm.core.database.sql_repository.transaction_repository import ITransactionRepository, ImportResult, Transaction
from .standards.columns import Columns


class DBIOService:
    """
    High-level service for database I/O operations.

    This class provides a unified API for all database operations,
    including import/export, queries, and statistics.

    Implemented as a singleton to ensure single database connection
    and shared cache across the application.
    """

    _instance = None
    _lock = threading.RLock()
    _initialized = False

    def __new__(cls):
        """Ensure only one instance exists (singleton pattern)."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(DBIOService, cls).__new__(cls)
        return cls._instance

    @classmethod
    def get_instance(cls):
        """
        Get the singleton instance of DBIOService.

        Returns:
            DBIOService: The singleton instance
        """
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def __init__(self):
        """Initialize the DB I/O Service (singleton initialization)."""
        # Prevent re-initialization
        if self._initialized:
            return

        with self._lock:
            if self._initialized:
                return

            log.info("Initializing DBIOService singleton...")

            # Initialize repository with caching
            self.repo = CachedSQLiteRepository()

            # Warm the cache synchronously during initialization
            cache_success = self.repo.warm_cache()
            if cache_success:
                cache_info = self.repo.get_cache_info()
                log.info(f"DBIOService initialized with cache: {cache_info['total_transactions']} transactions")
            else:
                log.warning("DBIOService initialized without cache - will use direct database access")

            self._initialized = True

    # --- Import/export (df) --------------------------------------------------

    def update_database(
        self,
        df: pd.DataFrame,
        source_file: Optional[str] = None
    ) -> ImportResult:
        """
        Import transactions from a pandas DataFrame.

        Args:
            df: DataFrame containing transaction data
            source_file: Optional source file path for reference

        Returns:
            ImportResult with counts of added, duplicate, and error records
        """
        if df is None or df.empty:
            return ImportResult(
                added_count=0,
                duplicate_count=0,
                error_count=0,
                errors=["No data to import"]
            )

        try:
            df = df.copy()

            df_columns = set(df.columns)
            display_names = {c.display_name for c in Columns.get_all_columns()}
            if any(col in display_names for col in df_columns):
                df = self._convert_display_to_db_columns(df)

            balance_col = Columns.BALANCE.db_name
            source_uid_col = Columns.SOURCE_UID.db_name
            unique_id_col = Columns.UNIQUE_ID.db_name  # Legacy support

            # Check for balance OR any unique identifier (source_uid, unique_id)
            has_balance = balance_col in df.columns
            has_source_uid = source_uid_col in df.columns
            has_unique_id = unique_id_col in df.columns

            if not (has_balance or has_source_uid or has_unique_id):
                error_msg = "Import failed: DataFrame must contain either a 'balance', 'source_uid', or 'unique_id' column."
                log.error(error_msg)
                return ImportResult(added_count=0, duplicate_count=0, error_count=len(df), errors=[error_msg])

            date_col_db = Columns.DATE.db_name
            if date_col_db in df.columns and pd.api.types.is_datetime64_any_dtype(df[date_col_db]):
                df[date_col_db] = df[date_col_db].dt.strftime('%Y-%m-%d')

            if source_file:
                source_file_col = Columns.SOURCE_FILE.db_name
                df[source_file_col] = source_file

            return self.repo.add_transactions_from_df(df, source_file=source_file)
        except Exception as e:
            log.error(f"Critical error in DBIOService.update_database: {e}", exc_info=True)
            return ImportResult(added_count=0, duplicate_count=0, error_count=len(df), errors=[f"Error importing DataFrame: {str(e)}"])

    def get_transactions(
        self,
        only_columns_with_data: bool = True,
        use_display_names: bool = True,
        **filters
    ) -> pd.DataFrame:
        """
        Get transactions as a DataFrame with optional filtering and formatting.
        """
        df = self._fetch_raw_transactions_df(
            all_cols=not only_columns_with_data,
            **filters
        )

        if use_display_names:
            df = Columns.apply_display_names_to_df(df)
            if only_columns_with_data:
                display_cols_with_data = [c.display_name for c in Columns.get_display_columns() if c.db_name in df.columns and not df[c.db_name].isna().all()]
                return df[display_cols_with_data]

        return df

    def _fetch_raw_transactions_df(
            self,
            *, 
            all_cols: bool = False,
            only_columns_with_data: bool = True,
            **filters
        ) -> pd.DataFrame:
        """
        Internal method to fetch raw transactions as a DataFrame with database column names.
        """
        df = self.repo.get_transactions_as_df(filters=filters)
        if df.empty:
            return pd.DataFrame()

        if not all_cols:
            core_columns = [col.db_name for col in Columns.get_display_columns()]
            core_columns = [col for col in core_columns if col in df.columns]
            df = df[core_columns]

        if only_columns_with_data:
            cols_with_data = [col for col in df.columns if not df[col].isna().all()]
            df = df[cols_with_data]

        return df

    # --- Debugging and testing ------------------------------------

    def dump_raw_db_content(
        self,
        format: str = 'dataframe',
        file_path: Optional[Path] = None
    ) -> Union[pd.DataFrame, Dict, str, None]:
        """
        Dump raw database content for debugging and testing purposes.
        """
        transactions = self.list_transactions()
        if not transactions:
            return pd.DataFrame() if format == 'dataframe' else {} if format == 'json' else ''

        df = pd.DataFrame([t.to_dict() for t in transactions])

        if format == 'dataframe':
            result = df
        elif format == 'json':
            result = df.to_json(orient='records')
        elif format == 'csv':
            result = df.to_csv(index=False)
        else:
            raise ValueError(f"Unsupported format: {format}")

        if file_path:
            if format == 'json':
                df.to_json(file_path, orient='records')
            elif format == 'csv':
                df.to_csv(file_path, index=False)
            elif format == 'dataframe':
                df.to_pickle(file_path)
            return None

        return result

    def _convert_display_to_db_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Convert DataFrame columns from display names to database names.
        """
        display_to_db = {col.display_name: col.db_name for col in Columns.get_all_columns()}
        rename_cols = {col: display_to_db[col] for col in df.columns if col in display_to_db}

        if rename_cols:
            df = df.rename(columns=rename_cols)

        return df

    # --- Query helpers ---------------------------------------------

    def list_transactions(self, **filters) -> List[Transaction]:
        """
        List transactions with optional filtering.
        """
        return self.repo.get_transactions(filters)

    def get_transactions_dataframe(self, **filters) -> pd.DataFrame:
        """
        Get transactions as a pandas DataFrame with optional filtering.

        Uses cached repository for optimal performance.

        Args:
            **filters: Optional filters to apply (start_date, end_date, account_number, etc.)

        Returns:
            pandas DataFrame with transactions
        """
        # Repository handles caching transparently
        return self._fetch_raw_transactions_df(
            all_cols=True,
            only_columns_with_data=False,
            **filters
        )

    def get_cache_info(self) -> Dict[str, Any]:
        """
        Get information about the current cache state.

        Returns:
            Dictionary with cache statistics
        """
        return self.repo.get_cache_info()

    def get_database_stats(self) -> Dict[str, Any]:
        """
        Get database statistics.

        Returns:
            Dictionary with database statistics (total transactions, accounts, date ranges, etc.)
        """
        return self.repo.get_statistics()

    def get_transaction_by_id(self, transaction_id: int) -> Optional[Transaction]:
        """
        Get a transaction by its ID.
        """
        return self.repo.get_transaction_by_id(transaction_id)

    def update_transaction(self, transaction_id: int, updates: Dict[str, Any]) -> bool:
        """
        Update a transaction.
        """
        db_updates = {}
        for key, value in updates.items():
            col = Columns.from_display_name(key)
            if col:
                db_updates[col.db_name] = value
            else:
                db_updates[key] = value

        return self.repo.update_transaction(transaction_id, db_updates)

    def update_transaction_tags(self, transaction_id: int, tags: str) -> bool:
        """
        Update the tags for a transaction.
        """
        return self.update_transaction(transaction_id, {Columns.TAGS.db_name: tags})

    def get_unique_account_numbers(self) -> List[str]:
        """
        Get a list of unique account numbers.

        Uses cached repository for optimal performance.
        """
        return self.repo.get_unique_account_numbers()

    # --- Statistics and reporting ---------------------------------

    def get_db_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the transaction database.
        """
        transactions = self.list_transactions()
        if not transactions:
            return {
                'total_count': 0,
                'date_range': (None, None),
                'account_counts': {},
                'total_amount': 0.0
            }

        date_col = Columns.DATE.db_name
        account_col = Columns.ACCOUNT.db_name
        amount_col = Columns.AMOUNT.db_name

        dates = [getattr(t, date_col) for t in transactions if hasattr(t, date_col) and getattr(t, date_col)]
        min_date = min(dates) if dates else None
        max_date = max(dates) if dates else None

        account_counts = {}
        for t in transactions:
            account = getattr(t, account_col, None)
            if account:
                account_counts[account] = account_counts.get(account, 0) + 1

        total_amount = sum(getattr(t, amount_col, 0) for t in transactions)

        return {
            'total_count': len(transactions),
            'date_range': (min_date, max_date),
            'account_counts': account_counts,
            'total_amount': total_amount
        }

    def delete_all_transactions(self) -> int:
        """
        Delete all transactions from the database.
        """
        return self.repo.delete_all_transactions()

    @staticmethod
    def convert_transactions_to_dataframe(transactions: List[Transaction], ensure_columns: Optional[List[str]] = None) -> pd.DataFrame:
        """
        Convert a list of Transaction objects to a pandas DataFrame.

        Args:
            transactions: List of Transaction objects.
            ensure_columns: Optional list of database column names to ensure exist in the DataFrame.

        Returns:
            A pandas DataFrame with db_names as columns.
        """
        if not transactions:
            return pd.DataFrame(columns=ensure_columns) if ensure_columns else pd.DataFrame()

        try:
            data = [tx.to_dict() for tx in transactions]
            df = pd.DataFrame(data)

            if ensure_columns:
                for col in ensure_columns:
                    if col not in df.columns:
                        df[col] = None
            return df
        except Exception as e:
            log.error(f"Error converting transactions to DataFrame: {str(e)}", exc_info=True)
            raise