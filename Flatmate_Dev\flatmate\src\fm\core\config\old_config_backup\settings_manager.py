"""
Settings and Profile Management for FlatMate Application.

Handles application settings, profiles, and configuration versioning.
"""

import json
from pathlib import Path
from typing import Any, Dict, Optional, List
from datetime import datetime

from dynaconf import Dynaconf
import tomli
import tomli_w

from .path_manager import path_manager


class SettingsManager:
    """
    Manages application settings, profiles, and configuration.

    Provides methods for loading, saving, and managing application configurations.
    """

    CURRENT_PROFILE_VERSION = "1.0"

    def __init__(self, paths=None):
        """
        Initialize settings manager with optional path configuration.

        Args:
            paths (Dict, optional): Dictionary of application paths.
        """
        self.paths = paths or path_manager.app_paths
        self._config = self._load_configuration()
        self._active_profile = None
        self._load_active_profile()

    def _load_configuration(self) -> Dynaconf:
        """
        Load application configuration using Dynaconf.

        Returns:
            Dynaconf: Configured Dynaconf settings object.
        """
        return Dynaconf(
            envvar_prefix="FLATMATE",
            settings_files=[
                self.paths['default_settings_path'],
                self.paths['user_settings_path']
            ],
            environments=True,
            load_dotenv=True
        )

    def _load_active_profile(self):
        """
        Load the currently active user profile.

        Attempts to load the most recently used profile or sets a default.
        """
        profiles_dir = self.paths['profiles_dir']
        profiles_dir.mkdir(parents=True, exist_ok=True)

        # Look for the most recently used profile
        profile_files = list(profiles_dir.glob('*.json'))
        if profile_files:
            latest_profile = max(profile_files, key=lambda p: p.stat().st_mtime)
            with open(latest_profile, 'r', encoding='utf-8') as f:
                self._active_profile = json.load(f)
        else:
            # Create a default profile if none exists
            self._active_profile = {
                'version': self.CURRENT_PROFILE_VERSION,
                'name': 'default',
                'created_at': str(datetime.now()),
                'settings': {}
            }
            self._save_active_profile()

    def _save_active_profile(self):
        """
        Save the current active profile to a timestamped JSON file.
        """
        profiles_dir = self.paths['profiles_dir']
        profile_file = (
            profiles_dir / 
            f"{self._active_profile['name']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        )
        
        with open(profile_file, 'w', encoding='utf-8') as f:
            json.dump(self._active_profile, f, indent=2)

    def get_setting(self, key: str, default: Any = None) -> Any:
        """
        Retrieve a specific setting from the configuration.

        Args:
            key (str): Configuration key to retrieve.
            default (Any, optional): Default value if key is not found.

        Returns:
            Any: Configuration value or default.
        """
        return self._config.get(key, default)

    def update_setting(self, key: str, value: Any):
        """
        Update a specific setting in the configuration.

        Args:
            key (str): Configuration key to update.
            value (Any): New value for the setting.
        """
        # Update in-memory configuration
        self._config[key] = value

        # Update user settings file
        user_settings_path = self.paths['user_settings_path']
        try:
            with open(user_settings_path, 'rb') as f:
                settings = tomli.load(f)
        except FileNotFoundError:
            settings = {}

        settings[key] = value

        with open(user_settings_path, 'wb') as f:
            tomli_w.dump(settings, f)

    def create_profile(self, name: str, profile_data: Optional[Dict[str, Any]] = None) -> bool:
        """
        Create a new user profile.

        Args:
            name (str): Name of the profile to create
            profile_data (Optional[Dict[str, Any]]): Optional initial profile data

        Returns:
            bool: True if profile created successfully, False if profile already exists
        """
        profile_dir = Path(self.paths['profiles_dir']) / name
        if profile_dir.exists():
            return False

        # Create profile directory
        profile_dir.mkdir(parents=True, exist_ok=False)

        # Prepare profile metadata
        profile_metadata = {
            'name': name,
            'created_at': str(datetime.now()),
            'last_accessed': str(datetime.now()),
            'version': self.CURRENT_PROFILE_VERSION
        }

        # Merge provided data with metadata
        if profile_data:
            profile_metadata.update(profile_data)

        # Save profile configuration
        profile_config_path = profile_dir / 'profile.toml'
        with open(profile_config_path, 'wb', encoding='utf-8') as f:
            tomli_w.dump(profile_metadata, f)

        return True

    def switch_profile(self, profile_name: str):
        """
        Switch to a different profile.

        Args:
            profile_name (str): Name of the profile to switch to
        """
        profile_dir = Path(self.paths['profiles_dir']) / profile_name
        if not profile_dir.exists():
            raise ValueError(f"Profile '{profile_name}' does not exist")

        # Update active profile configuration
        profile_config_path = profile_dir / 'profile.toml'
        with open(profile_config_path, 'rb', encoding='utf-8') as f:
            profile_data = tomli.load(f)

        # Update last accessed time
        profile_data['last_accessed'] = str(datetime.now())

        with open(profile_config_path, 'wb', encoding='utf-8') as f:
            tomli_w.dump(profile_data, f)

        # Update settings to reflect new active profile
        self._active_profile = profile_data
        self._config.set('user_profile.active_profile', profile_name)

    def list_profiles(self) -> List[str]:
        """
        List available profiles.

        Returns:
            List[str]: Names of available profiles
        """
        profiles_dir = Path(self.paths['profiles_dir'])
        return [profile.name for profile in profiles_dir.iterdir() if profile.is_dir()]

    def get_active_profile(self) -> Dict:
        """
        Retrieve the currently active profile.

        Returns:
            Dict: Active user profile information.
        """
        return self._active_profile or {}

# Create a singleton instance
settings_manager = SettingsManager()
