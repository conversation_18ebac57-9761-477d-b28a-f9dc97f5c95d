"""
Master File Location Tracking for FlatMate Application.

Manages and tracks master file locations and history.
"""

import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Union

from .path_manager import path_manager

class MasterFileTracker:
    """
    Tracks and manages master file locations.
    
    Provides methods to update, retrieve, and manage master file history.
    """
    _instance = None

    def __new__(cls):
        """Singleton pattern implementation."""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        """Initialize master file tracker."""
        self.FM_MASTER_LOCATION = path_manager.app_paths['app_data_dir'] / 'fm_master_location.json'

    def update_master_location(self, file_path: Union[str, Path]) -> None:
        """
        Track the location of the primary master file.
        
        Maintains a history of master file locations.
        """
        print(f"Attempting to update master location with path: {file_path}")
        
        if not file_path:
            raise ValueError("File path cannot be None")
        
        file_path = Path(file_path)
        if not file_path.exists():
            raise ValueError(f"File does not exist: {file_path}")
        
        # Ensure app data directory exists
        self.FM_MASTER_LOCATION.parent.mkdir(parents=True, exist_ok=True)
        
        # Read existing history or initialize
        try:
            with open(self.FM_MASTER_LOCATION, 'r') as f:
                master_history = json.load(f)
            print(f"Existing master history: {master_history}")
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Error reading master history: {e}")
            master_history = {'current': None, 'history': []}
        
        # Update history
        current_time = str(datetime.now())
        new_entry = {
            'path': str(file_path),
            'timestamp': current_time
        }
        
        # Update current and history
        if master_history['current']:
            master_history['history'].append(master_history['current'])
        master_history['current'] = new_entry
        
        # Write updated history
        try:
            with open(self.FM_MASTER_LOCATION, 'w') as f:
                json.dump(master_history, f, indent=2)
            print("Master location updated successfully")
        except Exception as e:
            print(f"Error writing master location: {e}")

    def get_master_location(self) -> Dict:
        """
        Retrieve the current and historical master file locations.
        
        Returns tracking information for master file management.
        """
        print(f"Attempting to get master location from: {self.FM_MASTER_LOCATION}")
        
        try:
            with open(self.FM_MASTER_LOCATION, 'r') as f:
                master_location = json.load(f)
                print(f"Loaded master location: {master_location}")
                return master_location
        except FileNotFoundError:
            print("Master location file not found. Returning default.")
            return {'current': None, 'history': []}
        except json.JSONDecodeError as e:
            print(f"JSON Decode Error: {e}")
            # If the file exists but is not valid JSON, log its contents
            try:
                with open(self.FM_MASTER_LOCATION, 'r') as f:
                    print(f"File contents: {f.read()}")
            except Exception as read_error:
                print(f"Could not read file contents: {read_error}")
            return {'current': None, 'history': []}

# Create a singleton instance
master_file_tracker = MasterFileTracker()
