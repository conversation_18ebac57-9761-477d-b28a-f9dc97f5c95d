/* Import color palette */
@import url("palette.qss");

/* Global styles */
* {
    font-family: ".AppleSystemUIFont", "Helvetica Neue", Arial, sans-serif;
    font-size: 14px;  /* Base font size */
}

QWidget {
    background-color: var(--color-bg-dark);
    color: var(--color-text-primary);
}

/* Main panels */
#left_panel, #right_panel {
    background-color: var(--color-nav-bg);
    min-width: 150px;
}

/* Right side bar - no min-width to conform to content */
#right_side_bar {
    background-color: var(--color-nav-bg);
}

#left_panel QLabel {
    background: transparent;
}

#content_area {
    background-color: var(--color-bg-dark);
}

/* Primary Action Buttons */
QPushButton[type="primary"] {
    background-color: var(--color-primary);
    color: var(--color-text-primary);
    border: none;
    border-radius: 6px;
    padding: 6px;
    height: 35px;
    font-weight: bold;
}

QPushButton[type="primary"]:hover {
    background-color: var(--color-primary-hover);
}

QPushButton[type="primary"]:pressed {
    background-color: var(--color-primary-pressed);
}

/* Secondary Buttons */
QPushButton[type="secondary"] {
    background-color: var(--color-secondary);
    color: var(--color-text-primary);
    border: none;
    border-radius: 6px;
    padding: 6px;
    height: 35px;
}

QPushButton[type="secondary"]:hover {
    background-color: var(--color-secondary-hover);
}

QPushButton[type="secondary"]:pressed {
    background-color: var(--color-secondary-pressed);
}

/* File Tree */
#file_tree {
    border: 1px solid var(--color-border);
    border-radius: 4px;
    background-color: var(--color-bg-dark);
    color: var(--color-text-primary);
}

#file_tree::item {
    padding: 4px;
    border-bottom: 1px solid var(--color-border);
}

#file_tree::item:selected {
    background-color: var(--color-secondary);
    color: var(--color-text-primary);
}

#file_tree::item:hover {
    background-color: var(--color-panel-bg);
}

#file_tree QHeaderView::section {
    background-color: var(--color-panel-bg);
    color: var(--color-text-primary);
    padding: 4px;
    border: none;
    border-right: 1px solid var(--color-border);
    border-bottom: 1px solid var(--color-border);
}
