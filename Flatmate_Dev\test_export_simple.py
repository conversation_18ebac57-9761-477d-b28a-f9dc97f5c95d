#!/usr/bin/env python3
"""
Simple test to verify the export functionality implementation.
"""

import sys
import os

# Add the flatmate source to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'flatmate', 'src'))

def test_method_exists():
    """Test that the get_visible_dataframe method exists."""
    try:
        from fm.gui._shared_components.table_view_v2.components.table_view_core import TableViewCore
        
        # Check if the method exists
        if hasattr(TableViewCore, 'get_visible_dataframe'):
            print("✅ get_visible_dataframe() method exists in TableViewCore")
            
            # Check method signature
            import inspect
            sig = inspect.signature(TableViewCore.get_visible_dataframe)
            print(f"✅ Method signature: {sig}")
            
            # Check docstring
            docstring = TableViewCore.get_visible_dataframe.__doc__
            if docstring and "respects all active filters" in docstring:
                print("✅ Method has correct docstring")
            else:
                print("⚠️  Method docstring may be incomplete")
                
        else:
            print("❌ get_visible_dataframe() method NOT found in TableViewCore")
            return False
            
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_export_method_updated():
    """Test that the _export_data method has been updated."""
    try:
        from fm.gui._shared_components.table_view_v2.components.table_view_core import TableViewCore
        import inspect
        
        # Get the source code of the _export_data method
        source = inspect.getsource(TableViewCore._export_data)
        
        if "get_visible_dataframe()" in source:
            print("✅ _export_data() method uses get_visible_dataframe()")
        else:
            print("❌ _export_data() method still uses old get_dataframe()")
            return False
            
        if "QMessageBox" in source:
            print("✅ _export_data() method includes error handling")
        else:
            print("⚠️  _export_data() method may lack proper error handling")
            
        if "df.empty" in source:
            print("✅ _export_data() method checks for empty results")
        else:
            print("⚠️  _export_data() method may not handle empty results")
            
        return True
        
    except Exception as e:
        print(f"❌ Error checking _export_data method: {e}")
        return False

def test_signal_connections():
    """Test that signal connections exist."""
    try:
        from fm.gui._shared_components.table_view_v2.fm_table_view import CustomTableView_v2
        import inspect
        
        # Check if the export handler methods exist
        if hasattr(CustomTableView_v2, '_export_csv'):
            print("✅ _export_csv() handler method exists")
        else:
            print("❌ _export_csv() handler method NOT found")
            return False
            
        if hasattr(CustomTableView_v2, '_export_excel'):
            print("✅ _export_excel() handler method exists")
        else:
            print("❌ _export_excel() handler method NOT found")
            return False
            
        # Check the source code for signal connections
        source = inspect.getsource(CustomTableView_v2._connect_signals)
        
        if "csv_export_requested.connect(self._export_csv)" in source:
            print("✅ CSV export signal connection exists")
        else:
            print("❌ CSV export signal connection NOT found")
            return False
            
        if "excel_export_requested.connect(self._export_excel)" in source:
            print("✅ Excel export signal connection exists")
        else:
            print("❌ Excel export signal connection NOT found")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error checking signal connections: {e}")
        return False

def main():
    """Run all tests."""
    print("=== Export Functionality Implementation Test ===\n")
    
    tests = [
        ("Method Existence", test_method_exists),
        ("Export Method Update", test_export_method_updated),
        ("Signal Connections", test_signal_connections)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"Running {test_name} test...")
        result = test_func()
        results.append((test_name, result))
        print(f"{test_name}: {'PASS' if result else 'FAIL'}\n")
    
    # Summary
    print("=== Test Summary ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Export functionality implementation is complete.")
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
