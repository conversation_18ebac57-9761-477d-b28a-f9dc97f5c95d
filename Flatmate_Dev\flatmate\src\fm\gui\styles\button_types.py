"""
Button type definitions for consistent styling across the application.
"""
from enum import Enum, auto

class ButtonType(Enum):
    """Button types for consistent styling."""
    PRIMARY = "primary"     # Main action buttons (e.g., Submit, Process)
    SECONDARY = "secondary" # Less prominent actions (e.g., Select, Choose)
    CANCEL = "cancel"      # Cancel/Back actions
    EXIT = "exit"          # Application exit/close
    
    def as_prop(self) -> str:
        """Get the type as a Qt property value."""
        return self.value

# Common button configurations
BUTTON_CONFIGS = {
    ButtonType.PRIMARY: {
        "background": "#3B8A45",  # From Colors.PRIMARY
        "hover": "#4BA357",       # From Colors.PRIMARY_HOVER
        "pressed": "#2E6E37",     # From Colors.PRIMARY_PRESSED
        "font_weight": "bold",
    },
    ButtonType.SECONDARY: {
        "background": "#3B7443",  # From Colors.SECONDARY
        "hover": "#488E52",       # From Colors.SECONDARY_HOVER
        "pressed": "#2E5A35",     # From Colors.SECONDARY_PRESSED
        "font_weight": "normal",
    },
    ButtonType.CANCEL: {
        "background": "#3B7443",  # From Colors.SECONDARY
        "hover": "#488E52",       # From Colors.SECONDARY_HOVER
        "pressed": "#2E5A35",     # From Colors.SECONDARY_PRESSED
        "font_weight": "bold",
    },
    ButtonType.EXIT: {
        "background": "#3B7443",  # From Colors.SECONDARY
        "hover": "#488E52",       # From Colors.SECONDARY_HOVER
        "pressed": "#2E5A35",     # From Colors.SECONDARY_PRESSED
        "font_weight": "bold",
    }
}
