# Developer Notes - Categorize Module

*Last Updated: 2025-06-17*

## Architecture Overview

The Categorize module follows the same architectural patterns as the Update Data module, specifically:

1. **MVP Pattern**: Clear separation between Model, View, and Presenter
2. **Composite Pattern**: UI components use the `BasePanelComponent` hierarchy
3. **Signal-based Communication**: Components communicate via Qt signals rather than direct method calls

## Important Implementation Details

### Panel Managers

All panel managers should inherit from `BasePanelComponent` (from `fm.modules.update_data._view._common.base_panel_manager`), not directly from `QWidget` or `QObject`. This ensures:

- Consistent interface across modules
- Proper integration with the main window layout system
- Support for the composite pattern (panels containing other panels)

Example:
```python
from fm.modules.update_data._view._common.base_panel_manager import BasePanelComponent

class SomeManager(BasePanelComponent):
    def show_component(self):
        self.show()
        
    def hide_component(self):
        self.hide()
```

### Enhanced Table Widget

The `EnhancedTableWidget` provides advanced features like:
- Column filtering
- Column visibility toggle
- Sorting
- Custom formatting

When using it, remember to:
1. Set display columns explicitly with `set_display_columns(columns, column_names)`
2. Set editable columns with `set_editable_columns(['column_name'])`
3. Connect to the proper signals (`row_selected`, `cell_edited`)

### Signal Connections

Always implement a `disconnect_signals()` method in your components to prevent memory leaks and signal connection issues when navigating between modules. This should:

1. Disconnect all signals connected in the component
2. Call `disconnect_signals()` on any child components
3. Handle exceptions that might occur if signals aren't connected

## Common Pitfalls

1. **QObject vs QWidget**: Remember that only QWidget subclasses can be added to layouts with `addWidget()`
2. **Circular Imports**: Use local imports within methods when necessary to avoid circular dependencies
3. **Signal Disconnection**: Always wrap signal disconnections in try/except blocks to handle cases where signals aren't connected

## Future Enhancements

1. **Refactor Common Components**: Consider moving `BasePanelComponent` and other shared classes to a common location like `fm.gui.components.base`
2. **Standardize Table Widgets**: Create a factory or builder pattern for configuring `EnhancedTableWidget` consistently across modules
3. **Improve Error Handling**: Add more robust error handling and user feedback for data operations

## Testing Notes

When testing UI changes:
1. Check both small and large datasets
2. Verify all signals are properly connected and disconnected
3. Test navigation between modules to ensure proper cleanup
4. Verify that filtering and sorting work as expected

---

*Document created by Flatmate Development Team*