"""
Enhanced Table Model

Data storage and manipulation component for the Enhanced Table View System.
Provides editable/readonly column support and pandas DataFrame integration.
"""

from typing import Dict, List
import pandas as pd
from datetime import datetime

from PySide6.QtCore import Qt
from PySide6.QtGui import QStandardItemModel, QStandardItem


class EnhancedTableModel(QStandardItemModel):
    """Enhanced table model with additional features for data handling."""
    
    def __init__(self, parent=None):
        """Initialize the enhanced table model."""
        super().__init__(parent)
        self._editable_columns = set()
        self._column_types = {}
        self._original_data = None
        self._display_columns = None
    
    def set_editable_columns(self, columns: List[int]):
        """Set which columns should be editable."""
        self._editable_columns = set(columns)
    
    def set_column_types(self, column_types: Dict[int, str]):
        """Set column data types for proper sorting and filtering."""
        self._column_types = column_types
    
    def flags(self, index):
        """Return item flags based on column editability."""
        flags = super().flags(index)
        if index.column() in self._editable_columns:
            return flags | Qt.ItemIsEditable
        return flags & ~Qt.ItemIsEditable

    def _format_value_for_display(self, value):
        """Format a value for display in the table."""
        if pd.isna(value):
            return ""
        elif isinstance(value, (datetime, pd.Timestamp)) or hasattr(value, 'strftime'):
            # Use DateFormatService for consistent date formatting
            try:
                from fm.core.data_services.standards.date_format_service import DateFormatService
                return DateFormatService.format_date_for_display(value)
            except:
                # Fallback to ISO format if DateFormatService not available
                return value.strftime("%Y-%m-%d") if hasattr(value, 'strftime') else str(value)
        else:
            return str(value)
    
    def set_dataframe(self, df: pd.DataFrame):
        """Set the model data from a pandas DataFrame."""
        self._original_data = df.copy()
        
        # Clear existing data
        self.clear()
        
        # Set headers
        self.setHorizontalHeaderLabels([str(col) for col in df.columns])
        
        # Add data rows
        for row_idx, row in df.iterrows():
            items = []
            for col_idx, value in enumerate(row):
                # Format value for display (handles datetime objects properly)
                display_text = self._format_value_for_display(value)
                item = QStandardItem(display_text)
                # Store original value as user data for sorting
                item.setData(value, Qt.UserRole)
                items.append(item)
            self.appendRow(items)
    
    def get_dataframe(self) -> pd.DataFrame:
        """Get the current data as a pandas DataFrame."""
        if self._original_data is None:
            return pd.DataFrame()
        
        # Create a copy of the original DataFrame
        df = self._original_data.copy()
        
        # Update with edited values
        for row in range(self.rowCount()):
            for col in range(self.columnCount()):
                if col in self._editable_columns:
                    item = self.item(row, col)
                    if item:
                        df.iloc[row, col] = item.text()
        
        return df
