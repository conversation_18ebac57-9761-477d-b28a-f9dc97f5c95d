# Core Configuration System

This directory contains the core configuration infrastructure for the Flatmate application.

## Architecture Overview

The configuration system has two main layers:

1. **Core Config** (`config.py`) - Global singleton configuration manager
2. **Local Config** (`base_local_config.py`) - Component-specific configuration abstraction

## Files

### `config.py`
- **Core singleton config manager** - handles global application configuration
- **SystemPaths** - manages application directory paths
- **UserPaths** - manages user-specific paths (~/.flatmate/*)
- **UserPreferences** - handles user preference loading/saving with YAML serialization
- **Environment** - manages environment-specific settings
- **ConfigManager** - main singleton providing unified config access

### `base_local_config.py`
- **BaseLocalConfig** - abstract base class for component-specific configuration
- **Provides config hierarchy:** hardcoded defaults → component YAML → user preferences
- **Type-safe key management** through generics
- **Event bus integration** for configuration changes
- **Used by:** GUI, modules, and other configurable components

### `keys.py`
- **ConfigKeys** - centralized configuration key definitions
- **Organized by category:** Window, Logging, Reports, UpdateData, etc.
- **Type-safe enum-based keys** for configuration access

### `paths.py`
- **Path management utilities** for application directories
- **Handles path resolution** and directory creation
- **Platform-independent path handling**

## Usage Patterns

### For Components Needing Configuration

1. **Create component-specific keys:**
```python
# my_component/config/my_keys.py
class MyKeys:
    class Settings(str, Enum):
        SETTING_ONE = 'my_component.setting_one'
        SETTING_TWO = 'my_component.setting_two'
```

2. **Create component config class:**
```python
# my_component/config/my_config.py
from ...core.config.base_local_config import BaseLocalConfig
from .my_keys import MyKeys

class MyConfig(BaseLocalConfig[MyKeys]):
    def get_defaults(self) -> dict:
        return {
            MyKeys.Settings.SETTING_ONE: 'default_value',
            MyKeys.Settings.SETTING_TWO: True
        }
```

3. **Use in component:**
```python
# my_component/my_component.py
from .config.my_config import MyConfig

class MyComponent:
    def __init__(self):
        self.config = MyConfig()
        
    def get_setting(self):
        return self.config.get_value(MyKeys.Settings.SETTING_ONE)
```

### For Global Configuration Access

```python
from fm.core.config.config import config

# Get global config values
value = config.get_value('some.key', default='fallback')

# Set global config values
config.set_value('some.key', 'new_value')

# Get user preferences
pref = config.get_pref(ConfigKeys.Window.HEIGHT, default=600)
```

## Configuration Hierarchy

The system uses a clear hierarchy for configuration values:

1. **User Preferences** (highest priority) - `~/.flatmate/preferences.yaml`
2. **Component Defaults** - `component/config/defaults.yaml`
3. **Hardcoded Defaults** (lowest priority) - defined in `get_defaults()`

## Key Design Decisions

### Why Two Config Layers?

- **Core Config:** Global, singleton, handles system-wide settings
- **Local Config:** Component-specific, handles local defaults and type safety
- **Separation of concerns:** Core handles persistence, Local handles component logic

### Why Not Merge Them?

- **Singleton pattern is fragile** - changing it breaks everything
- **Different responsibilities** - global vs component-specific
- **Type safety** - components need their own key types
- **Config hierarchy** - components need local defaults from YAML files

### Refactoring History

- **Previously:** `BaseModuleConfig` in `modules/base/config/`
- **Now:** `BaseLocalConfig` in `core/config/`
- **Reason:** Better naming (not just modules) and logical grouping
- **Lesson:** Working > Perfect - don't break singleton patterns

## Best Practices

1. **Use BaseLocalConfig** for any component needing configuration
2. **Define component-specific keys** with clear naming
3. **Provide sensible defaults** in `get_defaults()`
4. **Use type-safe enum keys** rather than string literals
5. **Don't modify core config singleton** - use the abstraction layer
6. **Keep config hierarchy simple** - avoid deep nesting

## Troubleshooting

### Common Issues

1. **Import errors after refactor:**
   - Update imports to use `from ...core.config.base_local_config import BaseLocalConfig`
   
2. **Config not loading:**
   - Check YAML file paths and syntax
   - Verify `get_defaults()` implementation
   
3. **Enum serialization errors:**
   - Core config handles enum serialization automatically
   - Don't manually serialize enums in preferences

### Debug Tips

- Config loading is now silent except for errors
- Check logs for "Error processing config key" messages
- Verify YAML file permissions and syntax
- Use `config.get_value()` to check if values are set correctly

## Migration Guide

### From BaseModuleConfig to BaseLocalConfig

1. **Update imports:**
```python
# Old
from ....modules.base.config.base_module_config import BaseModuleConfig

# New  
from ....core.config.base_local_config import BaseLocalConfig
```

2. **Update class inheritance:**
```python
# Old
class MyConfig(BaseModuleConfig[MyKeys]):

# New
class MyConfig(BaseLocalConfig[MyKeys]):
```

3. **Update documentation references:**
- Replace "module-specific" with "component-specific"
- Update file paths in documentation
