"""Configuration keys for the Categorize module."""

from enum import Enum
from typing import Dict, Any


class CatKeys:
    """Configuration keys for the Categorize module."""

    class Display(str, Enum):
        """Display settings."""
        DESCRIPTION_WIDTH = "categorize.display.description_width"
        DATE_WIDTH = "categorize.display.date_width"
        AMOUNT_WIDTH = "categorize.display.amount_width"
        ACCOUNT_WIDTH = "categorize.display.account_width"
        TAGS_WIDTH = "categorize.display.tags_width"
        CATEGORY_WIDTH = "categorize.display.category_width"
        NOTES_WIDTH = "categorize.display.notes_width"

        # Column visibility settings
        VISIBLE_COLUMNS = "categorize.display.visible_columns"
        DEFAULT_SORT_COLUMN = "categorize.display.default_sort_column"
        DEFAULT_SORT_ORDER = "categorize.display.default_sort_order"

        # Format settings
        DATE_FORMAT = "categorize.display.date_format"
        AMOUNT_DECIMALS = "categorize.display.amount_decimals"

    class Filters(str, Enum):
        """Filter settings"""
        DEFAULT_DAYS = 'categorize.filters.default_days'
        REMEMBER_LAST = 'categorize.filters.remember_last'
    
    class Tags(str, Enum):
        """Tag settings"""
        AUTO_SUGGEST = 'categorize.tags.auto_suggest'
        RECENT_TAGS = 'categorize.tags.recent_tags'
    
    class Patterns(str, Enum):
        """Pattern settings"""
        FILE_PATH = 'categorize.patterns.file_path'
        AUTO_LEARN = 'categorize.patterns.auto_learn'
    
    @classmethod
    def get_defaults(cls) -> Dict[str, Any]:
        """Get default values for all settings."""
        return {
            # Display settings
            cls.Display.DESCRIPTION_WIDTH: 40,  # Characters
            cls.Display.DATE_WIDTH: 12,
            cls.Display.AMOUNT_WIDTH: 12,
            cls.Display.ACCOUNT_WIDTH: 15,
            cls.Display.TAGS_WIDTH: 20,
            cls.Display.CATEGORY_WIDTH: 20,
            cls.Display.NOTES_WIDTH: 30,
            cls.Display.VISIBLE_COLUMNS: ["date", "description", "amount", "account", "tags", "category"],
            cls.Display.DEFAULT_SORT_COLUMN: "date",
            cls.Display.DEFAULT_SORT_ORDER: "descending",
            
            # Other existing defaults
            cls.Display.DATE_FORMAT: 'yyyy-MM-dd',
            cls.Display.AMOUNT_DECIMALS: 2,
            cls.Filters.DEFAULT_DAYS: 30,
            cls.Filters.REMEMBER_LAST: True,
            cls.Tags.AUTO_SUGGEST: True,
            cls.Tags.RECENT_TAGS: [],
            cls.Patterns.FILE_PATH: '~/.flatmate/data/categorize/patterns.json',
            cls.Patterns.AUTO_LEARN: True
        }


