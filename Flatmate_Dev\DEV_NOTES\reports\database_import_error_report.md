# Database Import Error Report - `no such column: tr.hash`

**Date:** 2025-07-10

## 1. Issue Summary

The data pipeline fails during the final step of updating the database from the newly created master file. The process terminates with a `sqlite3.OperationalError`, reporting `no such column: tr.hash`.

This prevents any new transactions from being added to the central `transactions.db` database, breaking a critical part of the data update workflow.

## 2. Root Cause Analysis

The error originates in the `add_transactions_from_df` method of the `SQLiteTransactionRepository` class, located in `flatmate/src/fm/core/database/sql_repository/sqlite_repository.py`.

This method is designed to prevent duplicate entries by performing the following steps:
1.  It creates a temporary table to hold the incoming transactions from the master file.
2.  It generates a unique `hash` for each incoming transaction.
3.  It then attempts to find and exclude duplicates by comparing the hashes in the temporary table with the hashes in the main `transactions` table (`tr`).

The exact failing SQL queries are:

- **Line 180 (for finding duplicates):**
  ```sql
  SELECT t.hash FROM temp_import t JOIN transactions tr ON t.hash = tr.hash;
  ```

- **Line 193 (for inserting non-duplicates):**
  ```sql
  INSERT INTO transactions (...) SELECT ... WHERE NOT EXISTS (SELECT 1 FROM transactions tr WHERE tr.hash = t.hash);
  ```

The error occurs because the `transactions` table in the database at `C:\Users\<USER>\.flatmate\data\transactions.db` **does not have a `hash` column**. The application's logic has evolved to use this column for duplicate detection, but the database schema has not been updated to reflect this change.

## 3. Proposed Solution

To resolve this issue, the `transactions` table schema must be updated to include the `hash` column. 

### Recommended Action:

1.  **Add `hash` Column to `transactions` Table:**
    - The column should be of type `TEXT` or `VARCHAR`.
    - It should be indexed, and ideally have a `UNIQUE` constraint to enforce data integrity at the database level.

2.  **Implementation Strategy:**
    - A database migration script should be created to apply this schema change. This ensures the change is repeatable and can be applied to any environment (development, production, etc.).
    - Alternatively, for a quick fix, the column can be added manually using a DB browser tool, but a migration script is the recommended best practice.

This change will align the database schema with the application's data model, allowing the duplicate-checking mechanism to function as intended and resolving the import error.
