"""
Transaction repository interface.
Defines the contract for transaction data storage.
"""
from __future__ import annotations

from abc import ABC, abstractmethod
from dataclasses import dataclass, field, fields, make_dataclass
from datetime import datetime
from typing import Dict, List, Optional, Any, Union

# Import Columns here to build the dynamic Transaction class
from fm.core.data_services.standards.columns import Columns


@dataclass
class ImportResult:
    """Result of an import operation."""
    total_to_import: int = 0
    added_count: int = 0
    duplicate_count: int = 0
    error_count: int = 0
    errors: List[str] = field(default_factory=list)

    def __post_init__(self):
        if self.errors is None:
            self.errors = []


def _create_transaction_class():
    """Dynamically create the Transaction dataclass from the Columns registry."""
    
    dynamic_fields = []

    # Add fields from the Columns registry using the dedicated helper
    for col in Columns.get_transaction_columns():
        # The type hint for the dataclass field
        field_type = Optional[col.dtype]
        
        # The default value for the field
        default_value = field(default=None)
        if col.dtype == str:
            default_value = field(default="")
        
        dynamic_fields.append((col.db_name, field_type, default_value))

    # Create the dataclass
    transaction_cls = make_dataclass('Transaction', fields=dynamic_fields, frozen=False)

    # Add class methods for serialization
    def to_dict(self) -> Dict[str, Any]:
        """Convert transaction to a dictionary, handling None and datetime."""
        data = {}
        for f in fields(self):
            value = getattr(self, f.name)
            if isinstance(value, datetime):
                data[f.name] = value.isoformat() if value else None
            else:
                data[f.name] = value
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Transaction":
        """Create transaction from a dictionary."""
        def _parse_date(d):
            if d and isinstance(d, str):
                try:
                    return datetime.fromisoformat(d.replace('Z', '+00:00'))
                except (ValueError, TypeError):
                    return None
            return d

        init_data = {}
        for f in fields(cls):
            if f.name in data:
                value = data[f.name]
                field_type = f.type
                # Handle Optional types (e.g., Optional[datetime])
                if hasattr(field_type, '__origin__') and field_type.__origin__ is Union:
                    field_type = field_type.__args__[0]

                if field_type is datetime:
                    init_data[f.name] = _parse_date(value)
                else:
                    init_data[f.name] = value
        return cls(**init_data)

    setattr(transaction_cls, 'to_dict', to_dict)
    setattr(transaction_cls, 'from_dict', from_dict)

    return transaction_cls


# Create the dynamic Transaction class
Transaction = _create_transaction_class()


class ITransactionRepository(ABC):
    """Interface for transaction data storage."""
    
    @abstractmethod
    def add_transactions(self, transactions: List["Transaction"]) -> ImportResult:
        """
        Add new transactions to the repository.
        
        Args:
            transactions: List of transactions to add
            
        Returns:
            ImportResult with counts of added and duplicate transactions
        """
        pass
    
    @abstractmethod
    def get_transactions(self, filters: Optional[Dict] = None) -> List["Transaction"]:
        """
        Retrieve transactions matching the filters.
        
        Args:
            filters: Dictionary of filter criteria
            
        Returns:
            List of matching transactions
        """
        pass
    
    @abstractmethod
    def update_transaction(self, transaction_id: Optional[int], data: Dict) -> bool:
        """
        Update a specific transaction.
        
        Args:
            transaction_id: ID of the transaction to update
            data: Dictionary of fields to update
            
        Returns:
            True if update was successful
        """
        pass
    
    @abstractmethod
    def delete_transaction(self, transaction_id: int) -> bool:
        """
        Mark a transaction as deleted.
        
        Args:
            transaction_id: ID of the transaction to delete
            
        Returns:
            True if deletion was successful
        """
        pass
        
    @abstractmethod
    def delete_all_transactions(self) -> int:
        """
        Mark all transactions as deleted.
        
        Returns:
            Number of transactions deleted
        """
        pass
    
    @abstractmethod
    def get_statistics(self) -> Dict:
        """
        Get statistics about the stored transactions.
        
        Returns:
            Dictionary with transaction statistics
        """
        pass
        
    @abstractmethod
    def add_transactions_from_df(self, df, source_file: Optional[str] = None) -> ImportResult:
        """
        Add new transactions from a pandas DataFrame.
        
        Args:
            df: Pandas DataFrame containing transaction data
            source_file: Optional source file path for reference
            
        Returns:
            ImportResult with counts of added and duplicate transactions
        """
        pass
