# Bank Transaction Processor Module

## Overview
This document outlines the design for a self-contained bank transaction processor module that handles statement processing and transaction categorization. The module will be extracted from the existing Flatmate application into a reusable component.

## Architecture

### Module Structure
```
fmbankprocessor/
├── __init__.py
├── core/
│   ├── __init__.py
│   ├── processor.py         # Main processing logic
│   ├── categorizer.py       # Categorization logic
│   └── models.py           # Data models (Transaction, Category, etc.)
├── handlers/
│   ├── __init__.py
│   ├── base_handler.py     # Base StatementHandler
│   ├── kiwibank.py         # Kiwibank handlers
│   └── coop.py             # Co-op bank handlers
├── utils/
│   ├── __init__.py
│   ├── patterns.py         # Pattern matching utilities
│   └── file_utils.py       # File handling utilities
└── config.py              # Module configuration
```

## Core Components

### 1. Processor

```python
class BankProcessor:
    """Main entry point for bank statement processing."""
    
    def __init__(self, config: Optional[Dict] = None):
        self.handlers: List[Type[StatementHandler]] = []
        self.categorizer = TransactionCategorizer()
        self._load_handlers()
        
    def process_file(self, file_path: str) -> List[Transaction]:
        """Process a bank statement file and return transactions."""
        handler = self._get_handler(file_path)
        if not handler:
            raise ValueError("No suitable handler found for file")
        return handler.process(file_path)
    
    def categorize_transactions(self, transactions: List[Transaction]) -> List[Transaction]:
        """Categorize transactions using available patterns."""
        return [self.categorizer.categorize(t) for t in transactions]
```

### 2. Statement Handlers

```python
class StatementHandler(ABC):
    """Base class for bank statement handlers."""
    
    @classmethod
    @abstractmethod
    def can_handle(cls, file_path: str) -> bool:
        """Check if this handler can process the file."""
        pass
    
    @abstractmethod
    def process(self, file_path: str) -> List[Transaction]:
        """Process the file and return transactions."""
        pass
```

### 3. Categorization

```python
class TransactionCategorizer:
    """Handles transaction categorization using patterns and ML."""
    
    def __init__(self, patterns_file: Optional[str] = None):
        self.patterns = self._load_patterns(patterns_file)
        self.ml_model = self._load_ml_model()
    
    def categorize(self, transaction: Transaction) -> Transaction:
        """Categorize a single transaction."""
        # 1. Try exact matches
        # 2. Try pattern matching
        # 3. Use ML model if available
        # 4. Return uncategorized if no match found
        return transaction
```

## Data Models

```python
@dataclass
class Transaction:
    date: datetime
    amount: float
    description: str
    category: str = "Uncategorized"
    metadata: Dict = field(default_factory=dict)
    
    def to_dict(self) -> Dict:
        return asdict(self)
```

## Configuration

Configuration will be handled through a simple dictionary or configuration file:

```yaml
# config.yaml
handlers:
  - fmbankprocessor.handlers.kiwibank.KiwibankBasicHandler
  - fmbankprocessor.handlers.coop.CoopStandardHandler

categorization:
  patterns_file: patterns.json
  use_ml: true
  ml_model_path: models/classifier.pkl
```

## Usage Example

```python
from fmbankprocessor import BankProcessor

# Initialize with default config
processor = BankProcessor()

# Process a bank statement
transactions = processor.process_file("statement.csv")

# Categorize transactions
categorized = processor.categorize_transactions(transactions)

# Save to database or export
```

## Integration with Flatmate

The module can be integrated into the Flatmate application by:

1. Installing it as a dependency
2. Initializing the processor in the application startup
3. Updating the existing code to use the new module

## Future Enhancements

1. **Machine Learning**
   - Train models on transaction data
   - Implement active learning from user corrections
   
2. **Advanced Features**
   - Duplicate detection
   - Transaction rules engine
   - Multi-currency support
   
3. **Performance**
   - Batch processing
   - Async processing for large files
   - Caching of patterns and models
