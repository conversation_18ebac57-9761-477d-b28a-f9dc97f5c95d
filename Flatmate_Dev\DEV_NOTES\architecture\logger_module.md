# FlatMate Logger Module

## Overview
The logger module provides a centralized logging system for the FlatMate application, supporting both console and file output with configurable log levels.

## Features
- Multiple log levels (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- Shorthand level notation (e.g., 'e' for ERROR, 'd' for DEBUG)
- Automatic module detection
- Exception logging with traceback
- Function decorator for automatic entry/exit logging
- Event-based architecture using global event bus
- Configurable log directory and levels

## Installation & Setup
```python
# The logger is automatically set up when importing from the core package
from fm.core.services.logger import log, Logger, log_this
```

## Log Levels
| Level | Shorthand | Description |
|-------|-----------|-------------|
| DEBUG | 'd' | Detailed debug information |
| INFO | 'i' | General information about application progress |
| WARNING | 'w' | Indication of potential issues |
| ERROR | 'e' | Serious problems that prevented normal execution |
| CRITICAL | 'c' | Severe errors that may cause application failure |

## Basic Usage

### Using the log function
```python
from fm.core.services.logger import log, LogLevel

# Basic usage
log("Starting application")

# With explicit level
log("Debug information", "debug")  # Shorthand
log("Error occurred", LogLevel.ERROR)  # Using enum

# With exception logging
try:
    # Some code that might fail
    pass
except Exception as e:
    log("Operation failed", "error", exc_info=True)
```

### Using the Logger class
```python
from fm.core.services.logger import Logger

Logger.debug("Debug message")
Logger.info("Information message")
Logger.warning("Warning message")
Logger.error("Error message")
Logger.critical("Critical error")

# With custom module name
Logger.info("Custom module log", module="CUSTOM_MODULE")
```

### Function Decorator
```python
from fm.core.services.logger import log_this

@log_this('debug')
def process_data():
    # Function entry/exit will be logged at DEBUG level
    return "result"

@log_this(level='info')
def another_function():
    # Entry/exit logged at INFO level
    pass
```

## Configuration
Logging is configured through the global config manager. The following settings are available:

- `Paths.LOGS`: Directory where log files will be stored
- Log level can be set in `preferences.yaml` with `console_level`

## Log File Format
Log files are automatically created with the following format:
```
flatmate-YYYY-MM-DD_HH-MM-SS.log
```

Each log entry includes:
- Timestamp
- Log level
- Module name
- Message
- Exception traceback (if any)

## Best Practices
1. Use appropriate log levels
2. Include relevant context in log messages
3. Use the `@log_this` decorator for function tracing
4. Set `exc_info=True` when logging exceptions
5. Use module-specific logging when appropriate

## Example Output
```
[INFO] [main] Application started
[DEBUG] [data_processor] Processing data chunk
[ERROR] [api] Failed to connect to database
    Traceback (most recent call last):
      File "api.py", line 42, in connect
        db.connect()
    ConnectionError: Connection refused
```

## Dependencies
- Python's built-in `logging` module
- `global_event_bus` for event handling
- `fm.core.config` for configuration management
