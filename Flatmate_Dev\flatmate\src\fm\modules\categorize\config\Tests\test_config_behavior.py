"""Test config system behavior from another file.

This tests:
1. Reading config values from another file
2. Ensuring defaults that already exist
3. Handling changed values
4. Cross-file config sharing
"""

import sys
from pathlib import Path

# Add the src directory to the path so we can import our modules
src_path = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(src_path))


class ExternalComponent:
    """Simulates a component in another file using the config system."""
    
    def __init__(self):
        from fm.modules.categorize.config.config import config
        self.config = config
        
        # This component ensures its own defaults
        self.config.ensure_defaults({
            'categorize.external.timeout_seconds': 60,
            'categorize.external.retry_count': 3,
            'categorize.external.debug_mode': False
        })
    
    def read_existing_config(self):
        """Test reading config values that should already exist."""
        # These should exist from the main test
        table_margin = self.config.get_value('categorize.display.table_margin')
        description_width = self.config.get_value('categorize.display.description_width')
        
        print(f"  📖 Reading existing values:")
        print(f"    table_margin: {table_margin}")
        print(f"    description_width: {description_width}")
        
        return table_margin, description_width
    
    def test_override_existing_default(self):
        """Test what happens when we try to ensure a default that already exists."""
        print(f"  🔄 Testing override behavior...")
        
        # First, read the current value
        current_value = self.config.get_value('categorize.display.table_margin')
        print(f"    Current table_margin: {current_value}")
        
        # Try to ensure a different default for the same key
        self.config.ensure_defaults({
            'categorize.display.table_margin': 999  # Different from original 10
        })
        
        # Read the value again - should it change or stay the same?
        new_value = self.config.get_value('categorize.display.table_margin')
        print(f"    After ensure_defaults(999): {new_value}")
        
        return current_value, new_value
    
    def manually_change_value(self):
        """Test manually changing a config value."""
        print(f"  ✏️  Testing manual value change...")
        
        # Read current value
        current = self.config.get_value('categorize.display.description_width')
        print(f"    Current description_width: {current}")
        
        # Manually set a new value
        self.config.set_value('categorize.display.description_width', 500)
        
        # Read it back
        new_value = self.config.get_value('categorize.display.description_width')
        print(f"    After set_value(500): {new_value}")
        
        # Now try ensure_defaults again - what happens?
        self.config.ensure_defaults({
            'categorize.display.description_width': 300  # Different value
        })
        
        final_value = self.config.get_value('categorize.display.description_width')
        print(f"    After ensure_defaults(300): {final_value}")
        
        return current, new_value, final_value


def test_cross_file_behavior():
    """Test config behavior across files."""
    print("🧪 Testing Config Behavior Across Files")
    print("=" * 50)
    
    try:
        # First, run the main test to populate some config values
        print("📋 Step 1: Running main config test to populate values...")
        from fm.modules.categorize.config.Tests.test_new_config import test_new_config_system
        main_success = test_new_config_system()
        
        if not main_success:
            print("❌ Main test failed, cannot continue")
            return False
        
        print("\n" + "=" * 50)
        print("📋 Step 2: Testing from external component...")
        
        # Create external component
        external = ExternalComponent()
        
        # Test reading existing values
        print("\n🔍 Test 1: Reading existing config values")
        margin, width = external.read_existing_config()
        
        # Test override behavior
        print("\n🔍 Test 2: Override existing default")
        old_margin, new_margin = external.test_override_existing_default()
        
        print(f"\n📊 Override Results:")
        print(f"  Original value: {old_margin}")
        print(f"  After ensure_defaults with different value: {new_margin}")
        print(f"  Behavior: {'PRESERVED' if old_margin == new_margin else 'CHANGED'}")
        
        # Test manual changes
        print("\n🔍 Test 3: Manual value changes")
        original, manual_change, after_ensure = external.manually_change_value()
        
        print(f"\n📊 Manual Change Results:")
        print(f"  Original: {original}")
        print(f"  After manual set_value: {manual_change}")
        print(f"  After ensure_defaults: {after_ensure}")
        
        # Show final config state
        print("\n📋 Final Config State:")
        from fm.modules.categorize.config.config import config
        origins = config.get_key_origins()
        print(f"  Total keys: {len(origins)}")
        
        # Save updated YAML
        print("\n💾 Saving updated test_defaults.yaml...")
        test_yaml_path = config.save_test_yaml("test_defaults_after_behavior_test.yaml")
        print(f"✅ Updated YAML saved to: {test_yaml_path}")
        
        print("\n✅ Cross-file behavior tests completed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_config_isolation():
    """Test that config changes don't interfere with each other."""
    print("\n" + "=" * 50)
    print("🧪 Testing Config Isolation")
    print("=" * 50)
    
    try:
        from fm.modules.categorize.config.config import config
        
        # Create a unique key for this test
        test_key = 'categorize.isolation.test_value'
        
        # Ensure it doesn't exist initially
        initial_value = config.get_value(test_key)
        print(f"Initial value for {test_key}: {initial_value}")
        
        # Set a default
        config.ensure_defaults({test_key: 'first_value'})
        first_read = config.get_value(test_key)
        print(f"After first ensure_defaults: {first_read}")
        
        # Try to set a different default
        config.ensure_defaults({test_key: 'second_value'})
        second_read = config.get_value(test_key)
        print(f"After second ensure_defaults: {second_read}")
        
        # Manually change it
        config.set_value(test_key, 'manual_value')
        manual_read = config.get_value(test_key)
        print(f"After manual set_value: {manual_read}")
        
        # Try ensure_defaults again
        config.ensure_defaults({test_key: 'third_value'})
        final_read = config.get_value(test_key)
        print(f"After final ensure_defaults: {final_read}")
        
        print(f"\n📊 Isolation Test Results:")
        print(f"  ensure_defaults preserves existing values: {first_read == second_read}")
        print(f"  set_value overrides values: {manual_read == 'manual_value'}")
        print(f"  ensure_defaults after set_value: {final_read}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Isolation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success1 = test_cross_file_behavior()
    success2 = test_config_isolation()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("🎉 All behavior tests passed!")
        print("💡 Config system behavior is working as expected")
    else:
        print("🔧 Some tests failed - need to review config behavior")
    
    sys.exit(0 if (success1 and success2) else 1)
