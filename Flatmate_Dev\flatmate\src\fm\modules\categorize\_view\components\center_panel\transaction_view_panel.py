"""Transaction view panel for the Categorize module."""

import pandas as pd
from PySide6.QtCore import Signal, Qt
from PySide6.QtWidgets import <PERSON><PERSON>oxLayout, QWidget
from fm.core.services.logger import log
from fm.core.utils.timing_decorator import timing_decorator

from fm.gui._shared_components.table_view_v2.fm_table_view import CustomTableView_v2 as TableView
from fm.core.data_services.standards.columns import Columns, Column

from fm.modules.categorize.config import config



class TransactionViewPanel(QWidget):
    """Transaction view panel with enhanced table functionality.

    This panel provides:
    - Enhanced table with filtering, column management, and export
    - Top bar with filter controls and column selection
    - Resizable columns with memory
    - Integration with the column manager system
    """
    
    # Signals
    transaction_selected = Signal(int)
    tags_updated = Signal(int, str)
    
    def __init__(self, parent=None):
        """Initialize the transaction view panel."""
        super().__init__(parent)
        log.debug("Initializing TransactionViewPanel")
        self._init_ui()
        self._connect_signals()

    @timing_decorator
    def _init_ui(self):
        """Initialize the UI components."""
        log.debug("Setting up TransactionViewPanel UI")

        # ! Ensure required config defaults exist (no speculation!)
            
        ''' # ! TODO: This Will Need Updating potentially 
        seems to be hard coding some column names and not referencing standard db_col names '''
        
        config.ensure_defaults({
            'categorize.display.table_margin': 2,  # Reduced from 10 to 2
            'categorize.display.show_grid_lines': True,
            'categorize.display.row_height': 25,
            'categorize.display.default_visible_columns': ['date', 'details', 'amount', 'account', 'tags', 'category'],  # Simple default
            'display.date_format': 'DD/MM/YYYY',  # NZ style date format - configurable by user
            # Note: Column widths now come from StandardColumns.get_standard_column_widths()
        })

        # Use config values
        margin = config.get_value('categorize.display.table_margin', 2)  # Reduced default from 10 to 2

        layout = QVBoxLayout(self)
        layout.setContentsMargins(margin, margin, margin, margin)

        # Create enhanced transaction table with filtering using v2 pattern
        log.debug("Creating CustomTableView_v2 for transactions")
        self.transaction_table = TableView()

        # Configure the table with sensible defaults
        default_visible = config.get_value('categorize.display.default_visible_columns', [])
        self.transaction_table.configure(
            auto_size_columns=True,
            max_column_width=40,
            editable_columns=['tags'],  # Only tags are editable
            show_toolbar=True,
            default_visible_columns=default_visible  # Use config value
        )

        layout.addWidget(self.transaction_table)

        log.debug("TransactionViewPanel UI setup complete")

    def _connect_signals(self):
        """Connect internal signals."""
        log.debug("Connecting TransactionViewPanel signals")
        # Connect row selection signal
        self.transaction_table.table_view.row_selected.connect(
            lambda row: self.transaction_selected.emit(row))
        
        # Connect cell edit signal
        self.transaction_table.table_view.cell_edited.connect(
            lambda row, col, value: self._handle_cell_edit(row, col, value))
        
        log.debug("TransactionViewPanel signals connected")

    def _handle_cell_edit(self, row, col, value):
        """Handle cell edit events."""
        log.debug(f"Cell edited: row={row}, col={col}, value={value}")
        
        # Get the column name from the model
        df = self.transaction_table.get_dataframe()
        if df.empty:
            return
            
        # Get the display columns from the model
        display_columns = self.transaction_table.model._display_columns
        if col >= len(display_columns):
            return
            
        col_name = display_columns[col]
        log.debug(f"Edited column: {col_name}")
        
        # If this is the tags column
        if col_name == 'tags':
            # Get the transaction ID from the model
            if not df.empty and row < len(df):
                transaction_id = df.iloc[row].get('id', row)
                log.debug(f"Tags updated for transaction {transaction_id}: {value}")
                self.tags_updated.emit(transaction_id, value)
    
    def set_transactions(self, df: pd.DataFrame, default_columns=None):
        """Set the transactions dataframe to display.
        
        Args:
            df: DataFrame containing the transactions
            default_columns: Optional list of Column enums or db_names to display by default.
                           If None, uses the default visible columns for the categorize module.
        """
        log.debug(f"Setting transactions: {len(df) if df is not None else 0} rows")
        if df is None or df.empty:
            return
            
        # Get standard column widths from the central Columns registry
        standard_widths = Columns.get_column_widths()
        
        # Get display columns in proper order with user preferences
        if default_columns is None:
            # Use new ordered display columns for categorize module
            ordered_columns = Columns.get_ordered_display_columns('categorize')
            ordered_db_names = [col.db_name for col in ordered_columns]
            visible_columns = [col.display_name for col in ordered_columns]
            log.debug(f"Using ordered display columns: {ordered_db_names}")

            # CRITICAL: Reorder DataFrame columns to match our ordering
            # Only include columns that exist in the DataFrame
            available_ordered_cols = [col for col in ordered_db_names if col in df.columns]
            remaining_cols = [col for col in df.columns if col not in available_ordered_cols]
            final_column_order = available_ordered_cols + remaining_cols

            # Reorder the DataFrame
            df = df[final_column_order]
            log.debug(f"Reordered DataFrame columns: {list(df.columns)}")
        else:
            # Convert provided columns to display names, handling both Column enums and string db_names
            visible_columns = []
            for col in default_columns:
                if isinstance(col, Column):
                    visible_columns.append(col.display_name)
                else:  # Assume it's a db_name string
                    try:
                        visible_columns.append(Columns.from_db_name(col).display_name)
                    except (AttributeError, ValueError):
                        log.warning(f"Column {col} not found in Columns registry")
                        visible_columns.append(col)

        # Get editable columns from the 'user_editable' group
        editable_columns = [col.display_name for col in Columns.get('user_editable')]
        
        # Configure the table view with the specified settings
        self.transaction_table.configure(
            auto_size_columns=True,
            max_column_width=40,
            column_widths=standard_widths,
            editable_columns=editable_columns,  # All user_editable columns
            show_toolbar=True,
            default_visible_columns=visible_columns if visible_columns else None
        ).set_dataframe(df).show()
        
        log.debug(f"Displaying columns: {visible_columns}")

    # Note: _apply_default_column_visibility and _apply_column_widths methods
    # are no longer needed - the new v2 pattern handles this in configure()

    def _save_column_selections(self):
        """Save current column selections to local config."""
        try:
            table_view = self.transaction_table.table_view
            model = table_view._model

            # Get currently visible columns (display names)
            visible_display_names = []
            for col_idx in range(model.columnCount()):
                if not table_view.isColumnHidden(col_idx):
                    col_name = model.headerData(col_idx, Qt.Horizontal)
                    visible_display_names.append(str(col_name))

            # Convert display names back to database names for storage using the Columns registry
            # Get reverse mapping: display_name -> db_name
            display_to_db = Columns.get_reverse_display_mapping(
                # We need to provide the original display column names for an accurate mapping
                visible_display_names
            )

            visible_db_names = []
            for display_name in visible_display_names:
                db_name = display_to_db.get(display_name)
                if db_name:
                    visible_db_names.append(db_name)
                else:
                    # Fallback: convert display name back to db format
                    visible_db_names.append(display_name.lower().replace(' ', '_'))

            # Save to config as last_used_columns (not overriding defaults)
            config.ensure_defaults({
                'categorize.display.last_used_columns': visible_db_names
            })

            log.debug(f"Saved column selections: {visible_db_names}")

        except Exception as e:
            log.error(f"Error saving column selections: {e}")

    def disconnect_signals(self):
        """Clean up signal connections."""
        log.debug("Disconnecting TransactionViewPanel signals")
        if hasattr(self, 'transaction_table') and hasattr(self.transaction_table, 'table_view'):
            try:
                self.transaction_table.table_view.row_selected.disconnect()
                self.transaction_table.table_view.cell_edited.disconnect()
            except (TypeError, RuntimeError) as e:
                # Signal might not be connected
                log.debug(f"Error disconnecting signals: {e}")








