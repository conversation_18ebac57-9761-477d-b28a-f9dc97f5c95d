"""
Utilities pane component for the application.
Provides a vertical utilities bar with system-wide function icons.
"""

from pathlib import Path

from PySide6.QtCore import QSize, Qt, Signal
from PySide6.QtGui import QColor
from PySide6.QtWidgets import (
    QFrame,
    QSizePolicy,
    QToolButton,
    Q<PERSON><PERSON><PERSON>ayout,
    QWidget,
)

from fm.gui.icons.icon_renderer import IconRenderer
from fm.gui.icons.icon_manager import icon_manager

# Utility item identifiers
UTIL_SETTINGS = "settings"


class UtilButton(QToolButton):
    """Custom utility button with icon and optional tooltip."""

    # Define colors for different states
    ACTIVE_COLOR = QColor(72, 142, 82)  # #488E52 - Secondary hover green for active state
    INACTIVE_COLOR = QColor(204, 204, 204)  # #CCCCCC - calm-white for inactive
    HOVER_COLOR = QColor(255, 255, 255)  # White for hover state

    def __init__(self, icon_path=None, tooltip="", parent=None):
        super().__init__(parent)

        # Set up appearance - transparent and borderless
        self.setCheckable(True)
        self.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonIconOnly)
        self.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        self.setIconSize(QSize(32, 32))
        
        # Use the QSS class-based styling instead of inline styles
        self.setProperty("class", "nav_button")  # Use the same class as nav buttons
        
        # Set tooltip if provided
        if tooltip:
            self.setToolTip(tooltip)

        # Store the icon path for later use when changing states
        self.icon_path = icon_path
        self.is_hovered = False
        self.is_active = False
        
        # Set initial icon (inactive state)
        if icon_path:
            self.setIcon(self._create_colored_icon(icon_path, self.INACTIVE_COLOR))

    def _create_colored_icon(self, icon_path, color):
        """Create a colored icon from an SVG file with explicit color control.

        Args:
            icon_path: Path to the SVG icon file
            color: Color to apply to the icon

        Returns:
            QIcon: Colored icon
        """
        try:
            # Use the IconRenderer utility to create a colored icon with the specified color
            return IconRenderer.create_colored_icon(
                icon_path,
                size=self.iconSize(),
                color=color
            )
        except Exception as e:
            print(f"Error creating colored icon: {e}")
            # Return an empty QIcon instead of None to avoid type errors
            from PySide6.QtGui import QIcon
            return QIcon()

    def enterEvent(self, event):
        """Handle mouse enter event to update icon color for hover state."""
        self.is_hovered = True
        self._update_icon_state()
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """Handle mouse leave event to revert icon color."""
        self.is_hovered = False
        self._update_icon_state()
        super().leaveEvent(event)
        
    def setActive(self, active):
        """Set the active state of the button and update icon color accordingly."""
        self.is_active = active
        self._update_icon_state()
        
    def _update_icon_state(self):
        """Update the icon color based on current state (hover, active, normal)."""
        if not self.icon_path:
            return
            
        if self.is_hovered:
            self.setIcon(self._create_colored_icon(self.icon_path, self.HOVER_COLOR))
        elif self.is_active:
            self.setIcon(self._create_colored_icon(self.icon_path, self.ACTIVE_COLOR))
        else:
            self.setIcon(self._create_colored_icon(self.icon_path, self.INACTIVE_COLOR))


class UtilitiesPane(QWidget):
    """
    Utilities pane component that provides a vertical bar with system-wide function icons.
    """
    
    # Signal emitted when a utility item is selected
    utilitySelected = Signal(str)
    
    def __init__(self, parent=None):
        """Initialize the utilities pane.
        
        Args:
            parent: Parent widget
        """
        super().__init__(parent)
        
        # Set size policy to expand vertically to fill available space
        self.setSizePolicy(
            QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Expanding
        )
        
        # Ensure minimum width to properly display buttons
        self.setMinimumWidth(40)
        
        # Set up the UI
        self._setup_utilities_pane()
    
    def _setup_utilities_pane(self):
        """Set up the utilities pane UI."""
        # Set object name for potential styling via QSS
        self.setObjectName("utilities_pane")
        
        # Create main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 0, 8, 8)  # Zero top margin to position closer to nav pane
        layout.setSpacing(8)  # Reduced spacing
        layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        
        # Get settings icon path using icon manager
        settings_icon_path = None
        try:
            # Try to get from utilities pane first
            settings_icon_path = icon_manager.get_utilities_icon("settings")
        except FileNotFoundError:
            try:
                # Fall back to settings folder
                settings_icon_path = icon_manager.get_settings_icon()
            except FileNotFoundError:
                print("utils_pane Settings Icon not found")
        
        # Create settings button at the top of the pane
        tooltip = "Settings"
        settings_button = UtilButton(settings_icon_path, tooltip)
        settings_button.setIconSize(QSize(32, 32))
        settings_button.setObjectName(f"util_{UTIL_SETTINGS}")
        settings_button.clicked.connect(lambda: self._on_button_clicked(settings_button))
        settings_button.setStyleSheet("QToolButton { background-color: transparent; border: none; }")
        
        # Add settings button directly to the main layout (at the top)
        layout.addWidget(settings_button)
        
        # Add spacer
        layout.addSpacing(8)
        
        # Add a stretch to push everything to the top and take remaining space
        layout.addStretch(1)
        
        # Create frame for other utilities (if needed in the future)
        frame = QFrame()
        frame.setObjectName("utilities_frame")
        frame_layout = QVBoxLayout(frame)
        frame_layout.setContentsMargins(0, 0, 0, 0)
        frame_layout.setSpacing(16)
        frame_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        
        # Add frame to main layout
        layout.addWidget(frame)
        
        # Store buttons for later access
        self.buttons = {}
        self.buttons[UTIL_SETTINGS] = settings_button
    
    def _on_button_clicked(self, button):
        """Handle button clicks in the utilities pane.
        
        Args:
            button: The button that was clicked
        """
        # Extract the utility identifier from the button's object name
        if button.objectName().startswith("util_"):
            util_id = button.objectName()[5:]  # Remove "util_" prefix
            
            # Toggle active state
            is_active = not button.is_active
            button.setActive(is_active)
            
            # Emit signal with utility identifier and active state
            self.utilitySelected.emit(util_id)
    
    def clear_all_highlights(self):
        """Clear the active state of all utility buttons."""
        for button in self.buttons.values():
            button.setActive(False)
    
    def highlight_item(self, item_id):
        """Highlight a utility item without emitting the utility signal.
        
        This only updates the visual state of the button without triggering actions.
        
        Args:
            item_id: Identifier of the utility item to highlight
            
        Returns:
            bool: True if the item was highlighted, False if it doesn't exist
        """
        if item_id in self.buttons:
            self.clear_all_highlights()
            self.buttons[item_id].setActive(True)
            return True
        return False
    
    def toggle_settings(self):
        """Toggle the settings button state."""
        settings_button = self.buttons.get(UTIL_SETTINGS)
        if settings_button:
            self._on_button_clicked(settings_button)
    
    def add_icon(self, icon_id, icon_path, tooltip=""):
        """Add a new icon to the utilities pane.
        
        Args:
            icon_id: Unique identifier for the icon
            icon_path: Path to the SVG icon file
            tooltip: Optional tooltip text for the icon
            
        Returns:
            bool: True if the icon was added, False if it already exists
        """
        if icon_id in self.buttons:
            return False
            
        # Create button
        button = UtilButton(icon_path, tooltip)
        button.setObjectName(f"util_{icon_id}")
        button.clicked.connect(lambda: self._on_button_clicked(button))
        
        # Add to layout - find the frame layout
        frame = self.findChild(QFrame, "utilities_frame")
        if frame:
            frame.layout().addWidget(button)
            
        # Store button
        self.buttons[icon_id] = button
        return True
    
    def remove_icon(self, icon_id):
        """Remove an icon from the utilities pane.
        
        Args:
            icon_id: Identifier of the icon to remove
            
        Returns:
            bool: True if the icon was removed, False if it doesn't exist
        """
        if icon_id not in self.buttons:
            return False
            
        # Remove button from layout and delete it
        button = self.buttons[icon_id]
        frame = self.findChild(QFrame, "utilities_frame")
        if frame:
            frame.layout().removeWidget(button)
        button.deleteLater()
        
        # Remove from buttons dictionary
        del self.buttons[icon_id]
        return True
