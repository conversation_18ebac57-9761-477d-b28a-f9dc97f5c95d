"""
File display panel component for the center display.
"""

from PySide6.QtCore import Signal
from PySide6.QtWidgets import QFrame, QLabel, QVBoxLayout

from ..core.base_component import BaseComponent
from ..widgets.widget_file_display import FileDisplayWidget


class FileDisplayPanel(BaseComponent):
    """Panel component for displaying and managing files."""
    
    # Signals
    publish_file_removed = Signal(str)  # Publishes path of removed file
    publish_file_selected = Signal(str)  # Publishes path of selected file
    
    def __init__(self, parent=None):
        """Initialize the file display panel."""
        super().__init__(parent)
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Info section
        info_layout = QVBoxLayout()
        
        # Source folder
        self.source_label = QLabel("Source Folder:")
        self.source_label.setObjectName("subheading")
        self.source_path = QLabel()
        info_layout.addWidget(self.source_label)
        info_layout.addWidget(self.source_path)
        
        # Save location
        self.save_label = QLabel("Save Location:")
        self.save_label.setObjectName("subheading")
        self.save_path = QLabel()
        info_layout.addWidget(self.save_label)
        info_layout.addWidget(self.save_path)
        
        # Add info layout to main layout
        layout.addLayout(info_layout)
        
        # Separator
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(line)
        
        # Files section
        self.files_label = QLabel("Selected Files:")
        self.files_label.setObjectName("subheading")
        layout.addWidget(self.files_label)
        
        # File display widget
        self.file_display = FileDisplayWidget()
        layout.addWidget(self.file_display)
        
        # Add stretch at bottom
        layout.addStretch()
    
    def _connect_signals(self):
        """Connect internal signals."""
        # Proxy signals from file display widget
        self.file_display.publish_file_removed.connect(self.publish_file_removed)
        self.file_display.publish_file_selected.connect(self.publish_file_selected)
    
    def show_component(self):
        """Show this component."""
        self.show()
    
    def hide_component(self):
        """Hide this component."""
        self.hide()
    
    def set_source_path(self, path: str):
        """Set the source folder path."""
        self.source_path.setText(path)
    
    def set_save_path(self, path: str):
        """Set the save location path."""
        self.save_path.setText(path)
    
    def set_files(self, files: list, source_dir: str = ""):
        """Set the files to display in the tree.
        
        Args:
            files: List of file paths
            source_dir: Source directory for relative paths
        """
        self.file_display.set_files(files, source_dir)
    
    def get_files(self) -> list[str]:
        """Get all file paths in the tree."""
        return self.file_display.get_files()
    
    def display_welcome(self):
        """Display welcome message."""
        self.source_path.setText("")
        self.save_path.setText("")
        self.file_display.file_tree.clear()
        self.file_display.folder_items.clear()
        
        # Update the files label with welcome text
        self.files_label.setText("Welcome to Update Data")
        self.files_label.setStyleSheet("color: #2c3e50; font-weight: bold;")
    
    def show_error(self, message: str):
        """Show error message."""
        self.files_label.setText(f"Error: {message}")
        self.files_label.setStyleSheet("color: red;")
    
    def show_success(self, message: str):
        """Show success message."""
        self.files_label.setText(message)
        self.files_label.setStyleSheet("color: green;")
