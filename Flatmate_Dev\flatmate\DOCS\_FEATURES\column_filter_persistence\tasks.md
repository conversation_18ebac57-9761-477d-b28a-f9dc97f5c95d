# Implementation Tasks

## Task 1: Add Filter Persistence Fields to TableConfig
**File**: `flatmate/src/fm/gui/_shared_components/table_view_v2/components/table_config_v2.py`
**Method**: Add new dataclass fields
**Time**: 10 minutes
**Dependencies**: None

**Current Code** (end of TableConfig class, around line 67):
```python
    stretch_last_section: bool = False
    """Whether to stretch the last column to fill available space.
    Set to False to allow horizontal scrolling when content exceeds view."""
```

**New Code** (add after line 67):
```python
    stretch_last_section: bool = False
    """Whether to stretch the last column to fill available space.
    Set to False to allow horizontal scrolling when content exceeds view."""

    # === Filter Persistence ===
    save_filter_state: bool = True
    """Whether to save and restore filter state between sessions."""

    default_filter_column: str = "details"
    """Default column to filter on when no previous state exists."""

    last_filter_column: Optional[str] = None
    """Last used filter column (set at runtime)."""

    last_filter_pattern: Optional[str] = None
    """Last used filter pattern (set at runtime)."""
```

**Testing**:
1. Import TableConfig and create instance
2. Verify new fields exist with correct defaults
3. Verify Optional import is available

---

## Task 2: Add Filter Persistence Methods to CustomTableView_v2
**File**: `flatmate/src/fm/gui/_shared_components/table_view_v2/fm_table_view.py`
**Method**: Add persistence methods and enhance signal handler
**Time**: 20 minutes
**Dependencies**: Task 1

**Current Code** (lines 479-481):
```python
def _on_filter_applied(self, column, pattern):
    """Handle filter applied signal from toolbar."""
    self.table_view.set_column_filter(column, pattern)
```

**New Code** (replace and add methods):
```python
def _on_filter_applied(self, column, pattern):
    """Handle filter applied signal from toolbar."""
    self.table_view.set_column_filter(column, pattern)
    # Save filter state for persistence
    self._save_filter_state(column, pattern)

def _save_filter_state(self, column: str, pattern: str):
    """Save current filter state to config."""
    if self._config.save_filter_state:
        self._config.last_filter_column = column
        self._config.last_filter_pattern = pattern

def _restore_filter_state(self):
    """Restore filter state from config."""
    if (self._config.save_filter_state and
        self._config.last_filter_pattern and
        hasattr(self, 'toolbar') and
        hasattr(self.toolbar, 'filter_group')):

        column = self._config.last_filter_column or self._config.default_filter_column
        pattern = self._config.last_filter_pattern

        # Set filter in UI (without triggering signals)
        self.toolbar.filter_group.set_filter_state(column, pattern)

        # Apply filter to table
        self.table_view.set_column_filter(column, pattern)
```

**Testing**:
1. Apply filter → verify `_save_filter_state` called
2. Check config object has updated values
3. Call `_restore_filter_state` → verify UI and table updated

---

## Task 3: Enhance Filter Logic with AND/EXCLUDE Support
**File**: `flatmate/src/fm/gui/_shared_components/table_view_v2/components/table_utils/enhanced_filter_proxy_model.py`
**Method**: Add helper method and enhance `filterAcceptsRow`
**Time**: 25 minutes
**Dependencies**: None

**Current Code** (lines 71-72):
```python
if pattern.lower() not in str(data).lower():
    return False
```

**New Code** (add helper method and replace logic):
```python
def _parse_filter_pattern(self, pattern: str) -> tuple[list[str], list[str]]:
    """Parse pattern into AND terms and EXCLUDE terms.

    Args:
        pattern: Filter pattern (e.g., "foo bar -exclude")

    Returns:
        Tuple of (and_terms, exclude_terms)
    """
    if not pattern.strip():
        return [], []

    terms = pattern.split()
    and_terms = []
    exclude_terms = []

    for term in terms:
        if term.startswith('-') and len(term) > 1:
            # Exclude term (remove the -)
            exclude_terms.append(term[1:].lower())
        else:
            # AND term (including terms that are just "-")
            and_terms.append(term.lower())

    return and_terms, exclude_terms

# Replace the simple substring check (line 71-72) with:
def _check_pattern_match(self, data_str: str, pattern: str) -> bool:
    """Check if data matches the filter pattern with AND/exclude logic."""
    and_terms, exclude_terms = self._parse_filter_pattern(pattern)

    # If no terms, accept (empty filter)
    if not and_terms and not exclude_terms:
        return True

    data_lower = data_str.lower()

    # All AND terms must be present
    for term in and_terms:
        if term not in data_lower:
            return False

    # No EXCLUDE terms may be present
    for term in exclude_terms:
        if term in data_lower:
            return False

    return True

# Update the filterAcceptsRow method to use new logic:
# Replace: if pattern.lower() not in str(data).lower():
# With:    if not self._check_pattern_match(str(data), pattern):
```

**Testing**:
1. Test "foo bar" → both terms must be present
2. Test "foo -bar" → foo present, bar must not be present
3. Test "-" → treated as literal dash, not exclude
4. Test "test -" → "test" present, "-" present (literal)
5. Test "-test" → exclude rows containing "test"

---

## Task 4: Add FilterGroup State Management Method
**File**: `flatmate/src/fm/gui/_shared_components/table_view_v2/components/toolbar/groups/filter_group.py`
**Method**: Add `set_filter_state` method to FilterGroup class
**Time**: 15 minutes
**Dependencies**: None

**Current Code** (end of FilterGroup class, around line 250):
```python
def _clear_filters(self):
    """Clear all filters."""
    self.filter_input.clear()
    self.filters_cleared.emit()
```

**New Code** (add after existing methods):
```python
def _clear_filters(self):
    """Clear all filters."""
    self.filter_input.clear()
    self.filters_cleared.emit()

def set_filter_state(self, column: str, pattern: str):
    """Set filter state externally (for persistence restore).

    Args:
        column: Column name to select
        pattern: Filter pattern to set
    """
    # Set column selector if it has the method
    if hasattr(self.column_selector, 'set_selected_column'):
        self.column_selector.set_selected_column(column)

    # Set filter text without triggering change signals
    self.filter_input.blockSignals(True)
    self.filter_input.setText(pattern)
    self.filter_input.blockSignals(False)

def get_filter_state(self) -> tuple[str, str]:
    """Get current filter state.

    Returns:
        Tuple of (column, pattern)
    """
    column = self.column_selector.get_selected_column() if hasattr(self.column_selector, 'get_selected_column') else "details"
    pattern = self.filter_input.text()
    return column, pattern
```

**Testing**:
1. Call `set_filter_state("amount", "test")` → verify UI updates
2. Call `get_filter_state()` → verify returns correct values
3. Verify signals not emitted during `set_filter_state`

---

## Task 5: Update FilterInput Placeholder Text
**File**: `flatmate/src/fm/gui/_shared_components/table_view_v2/components/toolbar/groups/filter_group.py`
**Method**: Update FilterInput.__init__
**Time**: 5 minutes
**Dependencies**: None

**Current Code** (line 83):
```python
self.setPlaceholderText("Enter filter text...")
```

**New Code**:
```python
self.setPlaceholderText("foo bar = AND, -foo = exclude (space-separated)")
```

**Testing**:
1. Create FilterInput → verify new placeholder text appears
2. Verify text is clear and explains the syntax

---

## Task 6: Add Filter State Restoration to Widget Initialization
**File**: `flatmate/src/fm/gui/_shared_components/table_view_v2/fm_table_view.py`
**Method**: Call restoration in appropriate initialization method
**Time**: 10 minutes
**Dependencies**: Tasks 1, 2, 4

**Current Code** (find appropriate initialization method, likely in `_apply_configuration` around line 350):
```python
def _apply_configuration(self):
    """Apply all configuration to the UI components."""
    # ... existing configuration logic ...
```

**New Code** (add at end of method):
```python
def _apply_configuration(self):
    """Apply all configuration to the UI components."""
    # ... existing configuration logic ...

    # Restore filter state if enabled
    if self._config.save_filter_state:
        self._restore_filter_state()
```

**Testing**:
1. Set filter, restart widget → verify filter restored
2. Disable `save_filter_state` → verify filter not restored
3. Verify restoration only happens after toolbar is initialized
