# Implementation Tasks

## Task 1: Persist Filter State and Column Selection
**File**: `src/fm/gui/_shared_components/table_view_v2/fm_table_view.py` (parent widget)
**Method**: Widget config management, signal handlers
**Time**: 20 minutes
**Dependencies**: None

**Current Code**:
```python
# No persistence, no default column logic in parent widget
```

**New Code**:
```python
# On filter or column change, parent widget saves column/pattern to config or QSettings
# On widget init, parent widget restores last-used filter and column if enabled in config
# Default column is 'details' if no prior selection
# FilterGroup is a passive UI component, state set externally
```

**Testing**: Set filter and column, close/reopen module/app, confirm both are restored if enabled, not restored if disabled

---

## Task 2: AND/EXCLUDE Filter Logic
**File**: `src/fm/gui/_shared_components/table_view_v2/components/table_utils/enhanced_filter_proxy_model.py`
**Method**: `filterAcceptsRow`
**Time**: 20 minutes
**Dependencies**: Task 1

**Current Code** (excerpt):
```python
# Only matches if pattern is a substring (case-insensitive)
if pattern.lower() not in str(data).lower():
    return False
```

**New Code**:
```python
# Parse pattern: split by space, terms starting with '-' (surrounded by spaces) are exclude
# All AND terms (surrounded by spaces) must match, no EXCLUDE term may match
# If '-' is not surrounded by spaces, treat as part of search term
# Quotes or * prefix force literal search for rare cases
```

**Testing**: Try multiple terms, exclusions, edge cases (e.g. '-foo bar', 'foo -bar', '"-foo"', '*-foo')

---

## Task 3: UI Operator Hint
**File**: `src/fm/gui/_shared_components/table_view_v2/components/toolbar/groups/filter_group.py`
**Method**: `FilterInput` init
**Time**: 10 minutes
**Dependencies**: None

**Current Code**:
```python
self.setPlaceholderText("Enter filter text...")
```

**New Code**:
```python
self.setPlaceholderText("foo bar = AND, -foo = exclude. Operators must be spaced. Use quotes/* for literal.")
# Or add tooltip for more detail
```

**Testing**: Hover/click in filter, confirm hint is clear and matches operator logic

---

## Task 4: Config Option for Persistence
**File**: `src/fm/gui/_shared_components/table_view_v2/fm_table_view.py` (parent widget config)
**Method**: Config check on save/load
**Time**: 10 minutes
**Dependencies**: Task 1

**Current Code**:
```python
# No config option
```

**New Code**:
```python
# Add config key (e.g. 'table.filter.remember_last') to widget config
# Parent widget respects this in persistence logic
```

**Testing**: Toggle config, confirm persistence behaviour changes
