"""
Column ordering definitions for FM-standard display order.

This module defines the canonical column order that should be used throughout
the application for consistent, logical display of transaction data.
"""

from enum import Enum


class StandardColumnsOrder(Enum):
    """
    Canonical FM-standard column order for consistent display.
    
    This enum defines the logical order columns should appear in tables and dropdowns,
    based on user workflow and data importance. Lower values appear first.
    
    Order Logic:
    1. Core transaction data (Date, Details, Amount) - most frequently used
    2. Account information - context for the transaction  
    3. Balance information - financial context
    4. User categorization fields - workflow order
    5. Extended transaction details - less frequently needed
    6. Source/metadata - technical details
    7. System fields - internal use
    """
    
    # Core Transaction Data (1-10)
    DATE = 1
    DETAILS = 2
    AMOUNT = 3
    ACCOUNT = 4
    BALANCE = 5
    
    # User Workflow Fields (11-20)
    CATEGORY = 11
    TAGS = 12
    NOTES = 13
    
    # Extended Transaction Details (21-40)
    CREDIT_AMOUNT = 21
    DEBIT_AMOUNT = 22
    PAYMENT_TYPE = 23
    UNIQUE_ID = 24
    
    # This Party (TP) Details (41-50)
    TP_REF = 41
    TP_PART = 42
    TP_CODE = 43
    
    # Other Party (OP) Details (51-60)
    OP_REF = 51
    OP_PART = 52
    OP_CODE = 53
    OP_NAME = 54
    OP_ACCOUNT = 55
    
    # Source/Import Metadata (61-80)
    SOURCE_FILENAME = 61
    SOURCE_BANK = 62
    SOURCE_TYPE = 63
    STATEMENT_DATE = 64
    IMPORT_DATE = 65
    MODIFIED_DATE = 66
    
    # System Fields (81-99)
    ID = 81
    HASH = 82
    IMPORT_HASH = 83
    DB_UID = 84
    SOURCE_UID = 85
    
    # Special/Utility Columns (100+)
    EMPTY_COLUMN = 100
    
    @classmethod
    def get_order_value(cls, column_name: str) -> int:
        """
        Get the order value for a column name.
        
        Args:
            column_name: The column name (enum name, not value)
            
        Returns:
            Order value, or 999 if column not found (puts it at end)
        """
        try:
            return cls[column_name.upper()].value
        except KeyError:
            return 999  # Unknown columns go to the end
    
    @classmethod
    def get_ordered_column_names(cls) -> list[str]:
        """
        Get all column names in order.
        
        Returns:
            List of column names (enum names) in display order
        """
        return [col.name for col in sorted(cls, key=lambda x: x.value)]
    
    @classmethod
    def sort_columns_by_order(cls, column_names: list[str]) -> list[str]:
        """
        Sort a list of column names according to FM-standard order.
        
        Args:
            column_names: List of column names to sort
            
        Returns:
            Sorted list with known columns in FM order, unknown columns at end
        """
        def get_sort_key(col_name: str) -> int:
            return cls.get_order_value(col_name)
        
        return sorted(column_names, key=get_sort_key)
