"""Base class for bank statement type maps."""

import os.path
import re
from abc import ABC
from contextlib import contextmanager
from dataclasses import dataclass, field
from datetime import datetime

from typing import List, Optional,Tuple, Generator, Type, ClassVar

import pandas as pd

from fm.core.data_services.standards.columns import Columns
from fm.core.data_services.standards.column_definition import Column
from fm.core.services.logger import Logger

@dataclass
class StatementType:
    """Configuration for statement format identification."""
    bank_name: str  # e.g., "Kiwibank"
    variant: str    # e.g., "basic"
    file_type: str  # e.g., "csv"
    
    def validate(self):
        if not self.bank_name or not isinstance(self.bank_name, str):
            raise ValueError("bank_name must be a non-empty string")
        if not self.variant or not isinstance(self.variant, str):
            raise ValueError("variant must be a non-empty string")
        if not self.file_type or not isinstance(self.file_type, str):
            raise ValueError("file_type must be a non-empty string")


@dataclass
class ColumnAttributes:
    """Configuration for column mapping and formatting."""
    # Required fields
    has_col_names: bool
    colnames_in_header: bool
    n_source_cols: int
    date_format: str
    data_start_row: int = 0

    col_names_row: int = 0
    has_account_column: bool = False
    
    source_col_names: List[str] = field(default_factory=list)
    target_col_names: List[Column] = field(default_factory=list)
    concat_cols_for_details: Optional[List[Column]] = None
    file_encoding: str = 'utf-8'
    
    def validate(self):
        """Validate column attributes."""
        if not self.date_format or not isinstance(self.date_format, str):
            raise ValueError("date_format must be a non-empty string")
        if not isinstance(self.n_source_cols, int) or self.n_source_cols <= 0:
            raise ValueError("n_source_cols must be a positive integer")
        if not isinstance(self.data_start_row, int) or self.data_start_row < 0:
            raise ValueError("data_start_row must be a non-negative integer")
        if not isinstance(self.col_names_row, int) or self.col_names_row < 0:
            raise ValueError("col_names_row must be a non-negative integer")
        if not isinstance(self.colnames_in_header, bool):
            raise ValueError("colnames_in_header must be a boolean")
        if not isinstance(self.has_col_names, bool):
            raise ValueError("has_col_names must be a boolean")
        if self.has_col_names and not self.source_col_names:
            raise ValueError("source_col_names must be provided when has_col_names is True")
        if self.source_col_names and len(self.source_col_names) != self.n_source_cols:
            raise ValueError(f"source_col_names length ({len(self.source_col_names)}) must match n_source_cols ({self.n_source_cols})")
        if self.target_col_names and len(self.target_col_names) != self.n_source_cols:
            raise ValueError(f"target_col_names length ({len(self.target_col_names)}) must match n_source_cols ({self.n_source_cols})")
        if not isinstance(self.file_encoding, str) or not self.file_encoding:
            raise ValueError("file_encoding must be a non-empty string")
            


@dataclass
class AccountNumberAttributes:
    """Configuration for account number extraction."""
    pattern: str = ""
    in_data: bool = False
    location: Tuple[int, int] = field(default_factory=lambda: (0, 0))
    in_file_name: bool = False
    in_metadata: bool = False
    
    def validate(self):
        # Check for at least one location flag set
        location_flags = [self.in_data, self.in_file_name, self.in_metadata]
        if not any(location_flags):
            raise ValueError("At least one account location (in_data, in_file_name, in_metadata) must be True")
            
        if any(location_flags) and not self.pattern:
            raise ValueError("pattern is required when any account location is specified")
            
        # Validate location coordinates
        if not isinstance(self.location, tuple) or len(self.location) != 2:
            raise ValueError("location must be a tuple of two integers")
        row, col = self.location
        if not isinstance(row, int) or not isinstance(col, int) or row < 0 or col < 0:
            raise ValueError("location coordinates must be non-negative integers")


@dataclass
class SourceMetadataAttributes:
    """Configuration for source metadata handling."""
    has_metadata_rows: bool = False
    metadata_start: Tuple[int, int] = field(default_factory=lambda: (0, 0))
    metadata_end: Tuple[int, int] = field(default_factory=lambda: (0, 0))
    
    def validate(self):
        if not isinstance(self.has_metadata_rows, bool):
            raise ValueError("has_metadata_rows must be a boolean")
            
        # Validate coordinates
        for name, coord in [("metadata_start", self.metadata_start), 
                          ("metadata_end", self.metadata_end)]:
            if not isinstance(coord, tuple) or len(coord) != 2:
                raise ValueError(f"{name} must be a tuple of two integers")
            row, col = coord
            if not isinstance(row, int) or not isinstance(col, int) or row < 0 or col < 0:
                raise ValueError(f"{name} coordinates must be non-negative integers")
        
        if self.has_metadata_rows and self.metadata_start > self.metadata_end:
            raise ValueError("metadata_start must not be after metadata_end")

@dataclass
class StatementAttributes:
    """Unified configuration for statement handling.
    
    Groups all statement-related attributes into a single class for better organization
    and enforces validation rules across all attributes.
    """
    statement_type_attrs: StatementType = None
    columns_attrs: ColumnAttributes = None
    account_attrs: AccountNumberAttributes = None
    metadata_attrs: SourceMetadataAttributes = None
    
    
    def validate(self):
        """Validate all statement attributes and their relationships."""
        # Validate all components
        self.statement_type_attrs.validate()
        self.columns_attrs.validate()
        self.account_attrs.validate()
        self.metadata_attrs.validate()        
    


class StatementHandler(ABC):
    """Base class for all statement handlers.
    
    Subclasses must define:
    - statement_type: StatementType
    - columns: ColumnAttributes
    - account: AccountNumberAttributes
    - metadata: SourceMetadataAttributes
    
    These will be automatically validated on initialization.
    """
    
    def __init__(self):
        """Initializes the handler and validates its configuration."""
        self.attributes = StatementAttributes(
            statement_type_attrs=self.statement_type,
            columns_attrs=self.columns,
            account_attrs=self.account,
            metadata_attrs=self.metadata,
        )
        self.attributes.validate()
        Logger.debug(f"Initialized {self.__class__.__name__}", module=self.__class__.__name__)

    # Type hints for statement attributes
    StatementType: ClassVar[Type[StatementType]] = StatementType
    
    # Instance attributes
    attributes: StatementAttributes

    # --- Core Internal Helpers ---

    def _read_csv(self, filepath: str, nrows: Optional[int] = None) -> Optional[pd.DataFrame]:
        """Centralized method to read CSV files using handler-specific configuration."""
        col_attrs = self.columns
        Logger.debug(f"[{self.__class__.__name__}] Attempting to read file: {filepath}")

        try:
            # Use colnames_in_header to decide if the file has a header row.
            # If True, use the specified row index. If False, read without a header.
            header_arg = col_attrs.col_names_row if col_attrs.colnames_in_header else None

            df = pd.read_csv(
                filepath,
                header=header_arg,
                nrows=nrows,
                on_bad_lines='skip',
                engine='python',
                encoding=col_attrs.file_encoding,
                sep=','
            )
            Logger.debug(f"[{self.__class__.__name__}] Successfully read file. Shape: {df.shape}")
            Logger.debug(f"[{self.__class__.__name__}] First 5 lines:\n{df.head().to_string()}")
            return df
        except FileNotFoundError:
            Logger.error(f"File not found at path: {filepath}", module=self.__class__.__name__)
        except (pd.errors.EmptyDataError) as e:
            Logger.warning(f"File is empty: {filepath} ({e})")
            return None
        except Exception as e:
            Logger.error(f"Unexpected error reading {filepath}: {e}")
            return None

    def _read_file(self, filepath: str) -> Optional[pd.DataFrame]:
        """Reads the full statement file into a DataFrame using the centralized _read_csv method.

        NOTE: This method acts as a dispatcher. It can be extended in the future
        to handle different file types (e.g., PDF, XLSX) by checking the file
        extension and calling the appropriate reader (e.g., _read_pdf).
        """
        return self._read_csv(filepath, nrows=None)

    def _extract_account_number(self, df: pd.DataFrame) -> str:
        """Extract account number using handler configuration."""
        attrs = self.account

        # Method 1: From metadata
        if attrs.in_metadata:
            try:
                row, col = attrs.location
                account_line = str(df.iloc[row, col])
                if attrs.pattern:
                    match = re.search(attrs.pattern, account_line)
                    if match:
                        return match.group(1) if match.groups() else match.group(0)
                else: # No pattern, return the whole cell content
                    return account_line.strip()
            except (IndexError, AttributeError, KeyError) as e:
                Logger.warning(f"Could not find account number in metadata: {e}")

        # Method 2: From data column
        elif attrs.in_data:
            try:
                col_name = attrs.location[1]
                if col_name in df.columns:
                    # Find first non-null value that matches pattern
                    for item in df[col_name].dropna():
                        item_str = str(item)
                        if attrs.pattern:
                            match = re.search(attrs.pattern, item_str)
                            if match:
                                return match.group(1) if match.groups() else match.group(0)
            except (IndexError, KeyError) as e:
                Logger.warning(f"Could not find account number in data column '{attrs.location[1]}': {e}")

        # Method 3: From filename
        elif attrs.in_file_name:
            try:
                from pathlib import Path
                if hasattr(self, '_current_filepath'):
                    filename = Path(self._current_filepath).stem
                    if attrs.pattern:
                        match = re.search(attrs.pattern, filename)
                        if match:
                            return match.group(1) if match.groups() else match.group(0)
                    else: # No pattern, return the whole filename
                        return filename
            except Exception as e:
                Logger.warning(f"Could not extract account number from filename: {e}")
        
        return ""

    def _standardize_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Standardizes DataFrame columns based on handler configuration.

        If the source file has a header (`colnames_in_header` is True), it renames
        the columns based on the `source_col_names` to `target_col_names` mapping.

        If the file is headless, it assigns the `target_col_names` directly.
        """
        target_names = [str(col) for col in self.columns.target_col_names]

        if self.columns.colnames_in_header:
            # For files with headers, rename based on source->target mapping.
            df.columns = [str(c).strip() for c in df.columns]
            rename_map = dict(zip(self.columns.source_col_names, target_names))
            df.rename(columns=rename_map, inplace=True)
        else:
            # For headless files, assign target names directly, ensuring column counts match.
            num_target_cols = len(target_names)
            df = df.iloc[:, :num_target_cols]  # Ensure we don't have extra columns
            df.columns = target_names
        return df

    def _standardize_dates(self, df: pd.DataFrame) -> pd.DataFrame:
        """Standardize dates in the DataFrame to ISO format."""
        date_col_name = str(Columns.DATE)
        date_format = self.columns.date_format

        if date_col_name not in df.columns:
            # This can happen if a statement has no date column configured.
            return df

        # Ensure the date column is of string type before parsing
        df[date_col_name] = df[date_col_name].astype(str)
        
        # Normalize the date string by replacing common separators with forward slashes
        normalized_dates = df[date_col_name].str.replace(r'[-.]', '/', regex=True)
        
        # Parse the normalized date string using the specified format
        df[date_col_name] = pd.to_datetime(normalized_dates, format=date_format, errors='coerce')

        # Check for any parsing failures
        failed_mask = df[date_col_name].isna() & df[date_col_name].notna()
        if failed_mask.any():
            failed_count = failed_mask.sum()
            Logger.warning(
                f"{failed_count}/{len(df)} dates could not be parsed with format '{date_format}'.",
                module=self.__class__.__name__
            )

        return df

    def _create_details_column(self, df: pd.DataFrame, columns: list[Column]) -> None:
        """Create a details column by concatenating specified standard columns."""
        # Convert enums to their string values for DataFrame operations
        col_names = [str(col) for col in columns]
        existing_cols = [col for col in col_names if col in df.columns]

        if not existing_cols:
            Logger.warning("No valid columns provided for details creation")
            return

        df[str(Columns.DETAILS)] = df[existing_cols].apply(
            lambda x: ' '.join(str(val) for val in x if pd.notna(val) and str(val).strip()),
            axis=1
        )
        Logger.info(f"Created details column from: {', '.join(existing_cols)}")

    def _reorder_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Reorder columns based on StandardColumns enum order, if they exist"""
        standard_order = [str(c) for c in Columns.get('statement_handler')]
        ordered_cols = [col for col in standard_order if col in df.columns]
        remaining_cols = [col for col in df.columns if col not in ordered_cols]
        return df.reindex(columns=ordered_cols + remaining_cols)

    def _custom_format(self, df: pd.DataFrame) -> pd.DataFrame:
        """Hook for handlers to perform bank-specific formatting."""
        return df

    # --- Internal Orchestration ---

    def _format_df(self, df: pd.DataFrame) -> pd.DataFrame:
        """Standardizes the format of the input DataFrame based on handler configuration."""
        account_number = self._extract_account_number(df)

        # --- 1. Slice DataFrame to exclude metadata/header rows ---
        # Only apply data_start_row if we're not using column names from header
        if not self.columns.colnames_in_header and self.columns.data_start_row > 0:
            df = df.iloc[self.columns.data_start_row:].reset_index(drop=True)

        # --- 2. Standardize column headers --- 
        # This is the core unification step. After this, all DataFrames, regardless
        # of their original format, have the same standardized column headers.
        df = self._standardize_columns(df)

        # --- Clean the DataFrame ---
        df.dropna(how='all', inplace=True) # Drop fully empty rows

        # No need for additional header detection - we already have data_start_row and colnames_in_header
        df[str(Columns.SOURCE_FILENAME)] = os.path.basename(self._current_filepath)

        if not self.columns.has_account_column:
            df[str(Columns.ACCOUNT)] = account_number if account_number else ''

        df = self._standardize_dates(df)
        df = self._custom_format(df)

        if str(Columns.EMPTY_COLUMN) in df.columns:
            df = df.drop(columns=[str(Columns.EMPTY_COLUMN)])

        # If the handler has explicitly configured columns for concatenation,
        # run the creation logic using that specific list.
        if self.columns.concat_cols_for_details:
            self._create_details_column(df, self.columns.concat_cols_for_details)
        
        df = self._set_data_types(df)
        df = self._reorder_columns(df)

        return df

    def _set_data_types(self, df: pd.DataFrame) -> pd.DataFrame:
        amount_col = str(Columns.AMOUNT)
        balance_col = str(Columns.BALANCE)

        if amount_col in df.columns:
            # Ensure the column is of string type before trying string operations
            if df[amount_col].dtype == 'object':
                # Remove currency symbols, commas, and whitespace
                df[amount_col] = df[amount_col].str.replace(r'[$,]', '', regex=True).str.strip()

            # Convert to numeric, coercing errors to NaN (which can be caught in validation)
            df[amount_col] = pd.to_numeric(df[amount_col], errors='coerce')

        if balance_col in df.columns:
            if df[balance_col].dtype == 'object':
                df[balance_col] = df[balance_col].str.replace(r'[$,]', '', regex=True).str.strip()
            df[balance_col] = pd.to_numeric(df[balance_col], errors='coerce')

        return df

    def _validate_data(self, df: pd.DataFrame):
        """
        Validates the DataFrame for required columns and null values.

        A valid transaction row must contain non-null values for DATE, DETAILS, AMOUNT,
        and ACCOUNT. Additionally, it must have a valid entry in EITHER the BALANCE
        or the UNIQUE_ID column.

        Raises:
            ValueError: If validation fails.
        """
        # Core columns that must always be present and populated.
        core_required_cols = [
            Columns.DATE,
            Columns.DETAILS,
            Columns.AMOUNT,
            Columns.ACCOUNT,
        ]

        for col_def in core_required_cols:
            col_name = str(col_def)
            if col_name not in df.columns:
                raise ValueError(
                    f"Validation failed for {os.path.basename(self._current_filepath)}: "
                    f"Required column '{col_name}' is missing."
                )
            
            if df[col_name].isnull().any():
                null_rows = df[df[col_name].isnull()].index.tolist()
                raise ValueError(
                    f"Validation failed for {os.path.basename(self._current_filepath)}: "
                    f"Required column '{col_name}' contains null values at rows: {null_rows}"
                )

        # Each row must have either a balance or a unique_id.
        balance_col = str(Columns.BALANCE)
        unique_id_col = str(Columns.UNIQUE_ID)

        has_balance = balance_col in df.columns
        has_unique_id = unique_id_col in df.columns

        if not has_balance and not has_unique_id:
            raise ValueError(
                f"Validation failed for {os.path.basename(self._current_filepath)}: "
                f"DataFrame must contain at least one of '{balance_col}' or '{unique_id_col}'."
            )

        # Check row-level validity
        invalid_rows_mask = pd.Series([False] * len(df), index=df.index)
        if has_balance and has_unique_id:
            invalid_rows_mask = df[balance_col].isnull() & df[unique_id_col].isnull()
        elif has_balance:
            invalid_rows_mask = df[balance_col].isnull()
        elif has_unique_id:
            invalid_rows_mask = df[unique_id_col].isnull()

        if invalid_rows_mask.any():
            invalid_indices = invalid_rows_mask[invalid_rows_mask].index.tolist()
            raise ValueError(
                f"Validation failed for {os.path.basename(self._current_filepath)}: "
                f"Rows {invalid_indices} are missing both '{balance_col}' and '{unique_id_col}'."
            )

    # --- Public API ---

    def process_file(self, filepath: str) -> Optional[pd.DataFrame]:
        """Reads, formats, and validates a statement file."""
        self._current_filepath = filepath
        try:
            df = self._read_file(filepath)
            if df is None or df.empty:
                Logger.warning(f"File is empty or could not be read: {filepath}", module=self.__class__.__name__)
                return None
                
            formatted_df = self._format_df(df)
            self._validate_data(formatted_df)
            return formatted_df
        except Exception as e:
            context = f"processing file {os.path.basename(filepath)}"
            Logger.error(
                f"Error in {self.__class__.__name__} while {context}: {e}",
                exc_info=True
            )
            raise

    @classmethod
    def can_handle_file(
        cls,
        filepath: str,
        *,
        require_filename_match: bool = False,
        require_columns: bool = True,
        require_account_number: bool = True,
    ) -> bool:
        """Check if the handler can process the given file based on its configuration."""
        try:
            col_attrs = cls.columns
            acc_attrs = cls.account

            # --- Column Header Validation ---
            if require_columns and col_attrs.has_col_names and col_attrs.source_col_names:
                try:
                    # Ensure all expected headers are treated as strings
                    expected_headers = {str(h).strip().lower() for h in col_attrs.source_col_names}

                    if col_attrs.colnames_in_header:
                        df_preview = pd.read_csv(filepath, encoding=col_attrs.file_encoding, header=col_attrs.col_names_row, nrows=5)
                        actual_headers = {str(h).strip().lower() for h in df_preview.columns}
                    else: # Headless file, names are in a data row
                        df_preview = pd.read_csv(filepath, encoding=col_attrs.file_encoding, header=None, nrows=col_attrs.col_names_row + 5)
                        if len(df_preview) <= col_attrs.col_names_row:
                            return False # Not enough rows to check for headers
                        actual_headers = {str(h).strip().lower() for h in df_preview.iloc[col_attrs.col_names_row]}

                    if not expected_headers.issubset(actual_headers):
                        return False
                except Exception as e:
                    Logger.debug(f"[{cls.__name__}] Failed column check for {filepath}: {e}")
                    return False

            # --- Account Number Validation ---
            if require_account_number and acc_attrs.pattern: 
                # todo wtf is this?
                # This check requires reading the file again, let's keep it simple for now
                # and assume the main process_file will handle it. A full implementation
                # would require reading a preview and checking metadata/data/filename here.
                pass

            return True

        except (FileNotFoundError, pd.errors.EmptyDataError):
            return False
        except Exception as e:
            Logger.error(f"[{cls.__name__}] Unexpected error in can_handle_file for {filepath}: {e}", exc_info=True)
            return False