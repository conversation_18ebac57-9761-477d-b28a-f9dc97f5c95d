"""
Core Data Services Package

This package provides centralized data services for the application,
including column management, database I/O, and data transformation.
"""

from .standards.columns import Columns
from .standards.column_definition import Column
# #from .column_service.column_manager import (
#     get_column_display_mapping,
#     apply_display_names_to_dataframe,
#     get_default_visible_columns_for_module,
#     convert_transactions_to_dataframe
# )
from .db_io_service import DBIOService
from .date_format_service import DateFormatService


# For backward compatibility with the old functional interface.
# This allows other modules to call `convert_transactions_to_dataframe` directly
# from the `data_services` package without needing to instantiate a service.
convert_transactions_to_dataframe = DBIOService.convert_transactions_to_dataframe

__all__ = [
    'Columns',
    'Column',
    'DBIOService',
    'DateFormatService',
    # Convenience functions for backward compatibility
    'convert_transactions_to_dataframe',
]
