# Test Real CSVs - Notes

## Running Tests - CONFIRMED WORKING COMMAND

### From Git Bash (Recommended)
```bash
# Navigate to project root (Flatmate_Dev/)
cd /c/Users/<USER>/_DEV/__PROJECTS/Flatmate_Dev

# Run the test with the virtual environment's Python
flatmate/.venv_fm313/Scripts/python.exe tests/test_real_csvs.py
```

### From Command Prompt
```batch
REM Navigate to project root (Flatmate_Dev/)
cd /d C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev

REM Run the test with the virtual environment's Python
flatmate\.venv_fm313\Scripts\python.exe tests\test_real_csvs.py
```

### Why This Works
- Uses the Python interpreter from the virtual environment directly
- No need to activate the virtual environment first
- Works from any directory as long as you use the full path to the Python executable
- Bypasses any path/activation issues with the virtual environment

### Useful Options
```bash
# Show detailed output for failing tests
pytest -v

# Stop after first failure
pytest -x

# Run only failed tests
pytest --lf

# Run tests in parallel (auto-detects CPU cores)
pytest -n auto
```

## Debugging Tips

### Logging
- Check test output directly in console (configured in `pytest.ini`)
- Add `-s` flag to see print statements: `pytest -s`
- Set log level in `pytest.ini` (INFO/DEBUG)

### Common Issues
1. **Import Errors**
   - Ensure you're running from the `flatmate` directory
   - Check `sys.path` in `conftest.py`

2. **Test Failures**
   - Run with `-v` for verbose output
   - Use `--showlocals` to see variable values
   - Check coverage with `--cov=src --cov-report=term-missing`

3. **Performance**
   - Tests running slow? Check `--durations=10`
   - Enable parallel execution with `-n auto`

## Dev note : now that we've fspent half a day figuring out how to run a simple  test:

----------------------------------

# ACTUAL TEST NOTES: (from actual tests!)

-----------------------------------
after making numerous changes listed in statement_handler_refactoring.md
we are ready to test:

