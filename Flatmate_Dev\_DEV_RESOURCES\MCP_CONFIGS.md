```json
{
  "mcpServers": {
    "puppeteer": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-puppeteer"
      ]
    },
    "sequential-thinking": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-sequential-thinking"
      ],
      "env": {}
    },"TAV2": {
      "command": "npx",
      "args": [
        "-y",
        "tavily-mcp@0.1.3"
      ],
      "env": {
        "TAVILY_API_KEY": "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"
      }
    }, "mcp-installer": {
      "command": "npx",
      "args": [
        "@anaisbetts/mcp-installer"
      ]
    },
    "google-cloud-platform": {
      "command": "npx -y gcp-mcp"
    },
    "filesystem": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "C:\\Users\\<USER>\\Users\\Admin\\OneDrive\\Documents\\_DEV",
        "C:\\Users\\<USER>\\OneDrive\\Documents\\_DEV\\Receipt Parser"

      ]
    }
  }
}

```

# ## Not google cloud, mcp not working ...

### New config: note, remove file system, superceded by desktop commander.

```json

{
  "mcpServers": {
    "puppeteer": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-puppeteer"
      ]
    },
    "sequential-thinking": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-sequential-thinking"
      ],
      "env": {}
    },"TAV2": {
      "command": "npx",
      "args": [
        "-y",
        "tavily-mcp@0.1.3"
      ],
      "env": {
        "TAVILY_API_KEY": "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"
      }
    }, "mcp-installer": {
      "command": "npx",
      "args": [
        "@anaisbetts/mcp-installer"
      ]
    },
"desktop-commander": {
      "command": "npx",
      "args": [
        "-y",
        "@wonderwhy-er/desktop-commander"
      ]
    }
  }
}

```

# MCP Filesystem Configuration

## Allowed Directories

### Primary Workspace

- `C:\Users\<USER>\OneDrive\Documents\_DEV\Receipt Parser`

### Specific Subdirectories

1. `C:\Users\<USER>\OneDrive\Documents\_DEV\Receipt Parser\Receipts_Shoebox`
2. `C:\Users\<USER>\OneDrive\Documents\_DEV\Receipt Parser\Receipt_Processor`
3. `C:\Users\<USER>\OneDrive\Documents\_DEV\Receipt Parser\receipt_scanner`
4. `C:\Users\<USER>\OneDrive\Documents\_DEV\Receipt Parser\OutputTest1`
5. `C:\Users\<USER>\OneDrive\Documents\_DEV\Receipt Parser\docs`

### Configuration Directories

- `C:\Users\<USER>\OneDrive\Documents\_DEV\Receipt Parser\.cursor`
- `C:\Users\<USER>\OneDrive\Documents\_DEV\Receipt Parser\memory-bank`

## Access Strategy

- Full read/write access to all listed directories
- Prioritize security by explicitly listing paths
- Enable comprehensive project management

## Path Validation Rules

1. Always use full absolute paths
2. Verify directory existence before operations
3. Implement error handling for access restrictions

## Recommended Filesystem Tools

- Use `mcp1_search_files` for precise file location
- Prefer individual file reading methods
- Log and handle any access denial errors

## Notes

- Paths are case-sensitive
- Ensure consistent path formatting
- Regularly update this configuration as project evolve

  ## Serena config (not currently used)


  ```
  json

  {
    "mcpServers": {
      "serena": {
        "command": "uv",
        "args": [
          "run", 
          "--directory", 
          "C:\\Users\\<USER>\\OneDrive\\Documents\\_DEV\\MCP_REPOSITORY\\serena", 
          "serena-mcp-server", 
          "C:\\Users\\<USER>\\OneDrive\\Documents\\_DEV\\Receipt Parser\\.serena\\project.yml"
        ]
      }
    }
  }
  ```
