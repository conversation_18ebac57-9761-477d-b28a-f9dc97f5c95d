# Core Database Implementation

This module contains the **core database implementation** for the Flatmate application. It provides the foundational database layer using the repository pattern with SQLite implementation.

## Purpose

This module handles:
- **Database connections and transactions**
- **SQL operations and schema management**  
- **Core data models** (Transaction, ImportResult)
- **Repository pattern implementation**
- **Database migrations**

## Architecture

```
database/
├── sql_repository/           # Repository pattern implementation
│   ├── transaction_repository.py    # Abstract repository interface
│   ├── sqlite_repository.py         # SQLite implementation
│   └── __init__.py
├── migrations/              # Database schema management
│   └── update_schema_consistency.py
├── docs/                   # Core database documentation
└── z_archive/             # Superseded components
```

## Key Components

### Repository Pattern

**`transaction_repository.py`** - Abstract base class defining the repository interface
**`sqlite_repository.py`** - Concrete SQLite implementation

### Core Models

**`Transaction`** - Core transaction data model
**`ImportResult`** - Result of import operations

## Usage

### Direct Repository Usage

```python
from fm.core.database import SQLiteTransactionRepository

# Initialize repository
repo = SQLiteTransactionRepository()

# Import transactions
result = repo.add_transactions_from_df(dataframe)
print(f"Added {result.added_count} transactions")

# Query transactions
transactions = repo.get_transactions({
    'start_date': datetime(2023, 1, 1),
    'end_date': datetime(2023, 12, 31)
})
```

### Integration with Data Services

The core database is designed to be used through the enhanced data services layer:

```python
# Recommended approach
from fm.core.data_services import DBIOService

service = DBIOService()  # Uses SQLiteTransactionRepository internally
result = service.update_database(dataframe)
```

## Design Principles

1. **Repository Pattern** - Clean separation between data access and business logic
2. **Abstract Interfaces** - Easy to swap implementations (SQLite → PostgreSQL, etc.)
3. **Core Models** - Simple, focused data models without UI concerns
4. **Transaction Safety** - Proper transaction handling and rollback
5. **Schema Management** - Versioned migrations for database changes

## Relationship to Data Services

```
┌─────────────────────────────────────┐
│         DATA SERVICES               │  ← Enhanced layer with column mgmt
│  (DBIOService, ColumnManager, etc.) │
├─────────────────────────────────────┤
│         CORE DATABASE               │  ← This module
│  (Repository, Models, SQL)          │
└─────────────────────────────────────┘
```

- **Core Database** provides the foundation
- **Data Services** adds enhanced functionality
- **Clean separation** allows independent development

## Archived Components

The `z_archive/` directory contains superseded components:
- **`db_io_service_old.py`** - Old service implementation (superseded by data_services)
- **`README_old.md`** - Previous documentation

## See Also

- **`core/data_services/`** - Enhanced service layer (recommended for application use)
- **`core/standards/`** - Canonical column definitions
- **`docs/`** - Additional database documentation
