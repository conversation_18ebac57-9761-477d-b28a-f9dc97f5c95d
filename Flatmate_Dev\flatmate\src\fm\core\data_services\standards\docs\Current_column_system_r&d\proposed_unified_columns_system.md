# Proposed Unified Column System

## Overview

This document outlines the proposed unified approach to the column system in Flatmate, addressing the current overlapping systems and architectural issues.

## Current Issues

1. **Competing Standards**: Two different column definition systems:
   - `standard_columns.py` (Enhanced with rich metadata)
   - `fm_standard_columns.py` (Legacy/canonical version)

2. **Overlapping Services**:
   - `column_manager.py` (Current service)
   - `column_name_service.py` (Superseded)

3. **Architectural Flaw**: Column visibility menu only shows columns in the table model, preventing users from accessing hidden columns.

4. **Unclear Categorization**: Different types of columns (bank, system, user) mixed together without clear organization.

5. **Type Handling**: No consistent system for type validation between Python and SQLite.

## Proposed Solution: Category-Based Enum Groups

We will implement the **Category-Based Enum Groups** approach as recommended in the analysis documents. This approach:

1. Maintains strong typing and IDE support
2. Allows for gradual migration
3. Provides clear separation of concerns
4. Is backward compatible with existing code

### Key Components

#### 1. Enhanced Column Metadata

```python
class ColumnCategory(Enum):
    """Source/origin of the column"""
    BANK = "bank"          # From bank statements
    SYSTEM = "system"      # Created by the application
    USER = "user"          # Added by users
    INTERNAL = "internal"  # For internal use only

@dataclass
class ColumnMetadata:
    """Rich metadata for a column"""
    display_name: str
    category: ColumnCategory
    used_in: List[str]     # Values from ColumnUsage
    python_type: type      # Python type for validation
    editable: bool = False
    width: Optional[int] = None
    description: Optional[str] = None
    default_value: Any = None
    
    def validate(self, value: Any) -> bool:
        """Validate that a value matches this column's expected type"""
        if value is None:
            return True
        return isinstance(value, self.python_type)
```

#### 2. Unified StandardColumns Enum

```python
class StandardColumns(Enum):
    """Single source of truth for all column definitions"""
    
    # Bank columns (from statements)
    DATE = ColumnMetadata(
        display_name="Date",
        category=ColumnCategory.BANK,
        used_in=[ColumnUsage.SOURCE.value, ColumnUsage.DISPLAY.value],
        python_type=datetime.date,
        width=12
    )
    
    # System columns (created during import)
    IMPORT_HASH = ColumnMetadata(
        display_name="Import Hash",
        category=ColumnCategory.SYSTEM,
        used_in=[ColumnUsage.SYSTEM.value],
        python_type=str,
        editable=False
    )
    
    # User columns (added by users)
    CATEGORY = ColumnMetadata(
        display_name="Category",
        category=ColumnCategory.USER,
        used_in=[ColumnUsage.DISPLAY.value, ColumnUsage.CATEGORIZE.value],
        python_type=str,
        editable=True
    )
    
    # Internal columns (database IDs, etc.)
    DB_ID = ColumnMetadata(
        display_name="ID",
        category=ColumnCategory.INTERNAL,
        used_in=[ColumnUsage.SYSTEM.value],
        python_type=int,
        editable=False
    )
```

#### 3. Enhanced Column Manager

```python
class ColumnManager:
    """Central service for all column operations"""
    
    def get_columns_by_category(self, category: ColumnCategory) -> List[StandardColumns]:
        """Get columns by their category (BANK, SYSTEM, USER, INTERNAL)"""
        return [col for col in StandardColumns if col.value.category == category]
    
    def get_columns_by_usage(self, usage: str) -> List[StandardColumns]:
        """Get columns by their usage (SOURCE, DISPLAY, CATEGORIZE, SYSTEM)"""
        return StandardColumns.get_columns_by_usage(usage)
    
    def validate_value(self, column: StandardColumns, value: Any) -> bool:
        """Validate that a value is appropriate for a column"""
        return column.value.validate(value)
    
    def convert_to_sql_value(self, column: StandardColumns, value: Any) -> Any:
        """Convert a Python value to an SQLite-compatible value"""
        if value is None:
            return None
            
        if column.value.python_type == datetime.date:
            return value.isoformat() if isinstance(value, datetime.date) else value
        
        # Add other type conversions as needed
        return value
    
    def convert_from_sql_value(self, column: StandardColumns, value: Any) -> Any:
        """Convert an SQLite value to the appropriate Python type"""
        if value is None:
            return None
            
        if column.value.python_type == datetime.date:
            return datetime.date.fromisoformat(value) if isinstance(value, str) else value
            
        # Add other type conversions as needed
        return value
        
    def get_available_columns_for_module(self, module_name: str) -> List[str]:
        """Get all columns available for a module (not just visible ones)."""
        config_key = f"{module_name}.display.available_columns"
        return self.preferences.get_config_value(config_key, self._get_default_available_columns(module_name))
        
    def prepare_full_dataframe_for_module(self, df: pd.DataFrame, module_name: str) -> pd.DataFrame:
        """Ensure DataFrame has all available columns for a module."""
        available_columns = self.get_available_columns_for_module(module_name)
        for col in available_columns:
            if col not in df.columns:
                df[col] = ""  # Add missing columns
        return df[available_columns]  # Reorder to match available columns
```

#### 4. Fixed Column Visibility System

```python
def set_transactions(self, df: pd.DataFrame):
    # 1. Get all available columns
    column_manager = get_column_manager()
    available_columns = config.get_value('categorize.display.available_columns', [])
    
    # 2. Ensure DataFrame has all available columns
    for col in available_columns:
        if col not in df.columns:
            df[col] = ""  # Add missing columns with empty values
    
    # 3. Reorder DataFrame to match available columns order
    df_ordered = df[available_columns]
    
    # 4. Set ALL columns in table model
    self.transaction_table.set_dataframe(df_ordered)
    column_mapping = column_manager.get_column_display_mapping("categorize")
    self.transaction_table.set_display_columns(available_columns, column_mapping)
    
    # 5. Apply default visibility (hide non-default columns)
    default_visible = config.get_value('categorize.display.default_visible_columns', [])
    for i, col in enumerate(available_columns):
        should_hide = col not in default_visible
        self.transaction_table.table_view.setColumnHidden(i, should_hide)
```

## Migration Plan

### Phase 1: Enhance StandardColumns
1. Update `standard_columns.py` with:
   - Add `category` field to `ColumnMetadata`
   - Add `python_type` field for validation
   - Add validation methods
   - Categorize existing columns

### Phase 2: Update ColumnManager
1. Update `column_manager.py` with:
   - Add methods for working with categories
   - Add validation methods
   - Add SQL conversion methods
   - Keep backward compatibility

### Phase 3: Fix Column Visibility
1. Modify table model to include all available columns
2. Update visibility menu to show all columns
3. Apply default visibility settings

### Phase 4: Deprecate Legacy Code
1. Mark `fm_standard_columns.py` as deprecated
2. Create migration guide for modules still using it
3. Set timeline for removal

### Phase 5: Full Transition
1. Remove deprecated code
2. Update all modules to use the new system
3. Add comprehensive tests

## Benefits

1. **Clear Organization**: Columns are categorized by both source/origin and usage
2. **Type Safety**: Built-in validation for Python types
3. **SQL Compatibility**: Explicit conversion between Python and SQLite types
4. **Single Source of Truth**: One definitive column definition system
5. **Backward Compatibility**: Gradual migration path
6. **Fixed Visibility**: All columns available in visibility menu

## Next Steps

1. Review this proposal
2. Identify modules using the column system for assessment
3. Create detailed implementation plan
4. Begin with Phase 1 implementation
