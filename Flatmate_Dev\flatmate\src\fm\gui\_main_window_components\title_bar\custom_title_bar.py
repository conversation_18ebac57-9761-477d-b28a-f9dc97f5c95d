"""
Custom title bar implementation with window controls and title display.

Features:
- Custom minimize, maximize/restore, and close buttons
- Window dragging support
- Dark theme styling
- Responsive layout
- Uses WindowControlsGroup for window control buttons
"""

import os
from pathlib import Path
from PySide6.QtWidgets import (QWidget, QHBoxLayout, QVBoxLayout, QLabel,
                             QSizePolicy, QSpacerItem)
from PySide6.QtCore import Qt, QPoint, QSize
from PySide6.QtGui import QIcon, QPixmap
from PySide6.QtSvg import QSvgRenderer
from PySide6.QtGui import QPainter

# Import window controls
from .components.window_controls import WindowControlsGroup


class CustomTitleBar(QWidget):
    """Custom title bar widget for frameless windows.
    
    This provides a custom title bar with window controls (minimize, maximize/restore, close)
    and supports window dragging. It uses WindowControlsGroup for the window control buttons.
    """
    
    def __init__(self, parent=None):
        """Initialize the custom title bar.
        
        Args:
            parent: The parent widget (should be the main window)
        """
        super().__init__(parent)
        self.parent = parent
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the UI components and layout."""
        # Set fixed height and size policy
        self.setFixedHeight(30)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        
        # Set transparent background - let the main window handle the background
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # Main layout with proper spacing
        self.main_layout = QHBoxLayout(self)
        self.main_layout.setContentsMargins(10, 0, 5, 0)  # Left margin for icon spacing
        self.main_layout.setSpacing(8)
        
        # Left group (window icon and title)
        self.left_group = QWidget()
        self.left_layout = QHBoxLayout(self.left_group)
        self.left_layout.setContentsMargins(10, 0, 0, 0)
        self.left_layout.setSpacing(10)
        
        # Window icon
        self.icon = QLabel()
        self.set_icon()  # This will use the default icon from components folder
        
        # Window title
        self.title = QLabel("flatmate")
        self.title.setStyleSheet("""
            QLabel {
                color: #CCCCCC;
                font-size: 16px;
                font-weight: 600;
            }
        """)
        
        # Add widgets to left group
        self.left_layout.addWidget(self.icon)
        self.left_layout.addSpacing(5)  # Add some spacing between icon and title
        self.left_layout.addWidget(self.title)
        self.left_layout.addStretch()
        
        # Add window controls group
        self.window_controls = WindowControlsGroup(self.parent)

        # Add all widgets to main layout
        self.main_layout.addWidget(self.left_group)
        self.main_layout.addStretch()
        self.main_layout.addWidget(self.window_controls)
            
    def mousePressEvent(self, event):
        """Handle mouse press for window dragging.
        
        Args:
            event: The mouse event
        """
        if event.button() == Qt.LeftButton:
            self.drag_start = event.globalPosition().toPoint() - self.parent.frameGeometry().topLeft()
            event.accept()
            
    def mouseMoveEvent(self, event):
        """Handle window movement when dragging.
        
        Args:
            event: The mouse move event
        """
        if event.buttons() & Qt.LeftButton and hasattr(self, 'drag_start'):
            self.parent.move(event.globalPosition().toPoint() - self.drag_start)
            event.accept()
            
    def set_title(self, title):
        """Set the window title.
        
        Args:
            title: The title text to display
        """
        self.title.setText(title)
        
    def set_icon(self, icon=None):
        """Set the window icon.
        
        Args:
            icon: Optional QIcon or path to icon file. If None, uses the default fm icon.
        """
        if icon is None:
            icon_path = Path(__file__).parent / 'components' / 'fm_icon.svg'
            if icon_path.exists():
                self.icon.setPixmap(QPixmap(str(icon_path)))
                return
        elif isinstance(icon, str):
            icon = QIcon(icon)
        if icon is not None:
            self.icon.setPixmap(icon.pixmap(20, 20))