# Date Formatting Architecture Discussion

## Current State

### Legacy Implementation (`format_date_for_display` instance method)
- **Responsibility**: Handles both date formatting AND user preferences
- **Behavior**:
  - Uses `get_user_date_format()` to get user's preferred format
  - Falls back to `DEFAULT_DISPLAY_FORMAT` if no user preference
  - Simple interface for callers who just want "the right format"

### New Implementation (`format_date_for_display` class method)
- **Responsibility**: Pure date formatting only
- **Behavior**:
  - Requires explicit format specification
  - No knowledge of user preferences
  - More flexible but shifts responsibility to callers

## Architectural Considerations

### 1. Separation of Concerns
- **Option A (Current)**: Formatting service manages both formatting and preference resolution
- **Option B (New)**: Formatting service only handles formatting, something else manages preferences

### 2. Caller Responsibility
- **Option A**: Callers don't need to know about formats
- **Option B**: Callers must know which format to use

### 3. Flexibility vs. Ease of Use
- **Option A**: Easier for common cases but less flexible
- **Option B**: More flexible but requires more boilerplate in callers

## Proposed Solutions

### Option 1: Keep Both with Clear Separation
- Keep class method for low-level formatting
- Keep instance method as a convenience wrapper that handles preferences
- Clearly document when to use each

### Option 2: Single Method with Optional Format
```python
@classmethod
def format_date_for_display(
    cls,
    date_input: Any,
    display_format: Optional[Union[str, DateDisplayFormat]] = None,
    **parse_kwargs
) -> str:
    """
    Format a date for display.
    
    Args:
        date_input: Date to format
        display_format: Optional format. If None, uses user's preferred format
        **parse_kwargs: Additional arguments for parse_date()
    """
    if display_format is None:
        display_format = get_user_date_format() or cls.DEFAULT_DISPLAY_FORMAT
    # Rest of implementation...
```

### Option 3: Service Layer
- Create a new `UserPreferencesService` that wraps the formatting service
- Handles preference resolution in one place

## Questions for Discussion
1. Should the formatting service be aware of user preferences?
2. Where should the preference resolution happen?
3. Do we need to support both use cases (with and without preferences)?
4. What's the expected behavior when no format is specified?

## Recommendation

Based on the principle of single responsibility, I recommend **Option 2** with a single method that can handle both cases. This:
- Maintains a clean, single interface
- Handles the common case simply (no format specified)
- Still allows explicit format when needed
- Avoids duplicate code
