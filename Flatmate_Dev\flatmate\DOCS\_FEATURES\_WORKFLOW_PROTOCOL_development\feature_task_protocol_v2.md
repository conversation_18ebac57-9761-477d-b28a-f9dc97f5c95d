# Flatmate Feature Task Protocol v2 (Discussion-Centered)

A structured, collaborative protocol for implementing new features, combining stepwise execution with .md-mediated discussion and traceability.

---
# Collaborative Process
- You are working with the product manager and lead dev
- Their comments will be demarked by a '>>'
- This is a .md-mediated process involving discussion at each major step
- At each key step, update or create relevant .md documentation to ensure shared context and traceability.


# Protocol Overview

For each new feature, create a folder in `flatmate/DOCS/_FEATURES/<feature_name>/` containing:

- `requirements.md` — Clear, testable requirements
- `design.md` — Architecture and technical design
- `tasks.md` — Actionable implementation steps
- `discussion.md` — Stepwise commentary, decisions, and stakeholder input

---

## Stepwise Feature Implementation Flow

1. **Clarify the Feature**
    - What is the desired behaviour?
    - What is the motivation or problem being solved?
    - Who are the stakeholders?
    - Document this in `discussion.md` and update `requirements.md`.

2. **Collaborative Requirements Discussion**
    - Product manager, lead dev, and others comment in `discussion.md` (use `>>` to denote roles).
    - Finalise requirements in `requirements.md`.

3. **Design Discussion**
    - Discuss architecture, data flow, edge cases in `discussion.md`.
    - Update `design.md` as consensus emerges.

4. **Task Breakdown**
    - List actionable steps in `tasks.md`.
    - Discuss dependencies, blockers, and assignments in `discussion.md`.

5. **Implementation & Traceability**
    - At each key milestone, document decisions, blockers, and progress in `discussion.md`.
    - Link PRs, commits, and test results for traceability.

6. **Review & Close**
    - Summarise outcomes, lessons learned, and any follow-ups in `discussion.md`.
    - Ensure all .mds are up to date and linked in the main tracking doc.

---

## Key Principles
- Every major step is discussed and documented in `discussion.md`.
- All stakeholders can comment directly in the markdown for transparency.
- Requirements, design, and tasks are living documents, updated as the feature evolves.

---

**This protocol ensures clarity, accountability, and collaborative traceability for all new feature work.**

---

*updated 2025-07-18. Refine as needed for team use.*

---