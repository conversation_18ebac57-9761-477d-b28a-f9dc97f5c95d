# Flatmate Feature Task Protocol v3 (Bulletproof for AI Systems)

A structured, collaborative protocol for implementing new features, designed to be bulletproof for less capable AI systems through concrete technical guidance and actionable specifications.

---
# Collaborative Process
- You are working with the product manager and lead dev
- Their comments will be demarked by a '>>'
- This is a .md-mediated process involving discussion at each major step
- At each key step, update or create relevant .md documentation to ensure shared context and traceability.

# Critical Success Factors for AI Implementation
- **Always include specific file paths** - Never use generic references like "the table view"
- **Provide concrete code examples** - Show exact method signatures and implementation patterns
- **Reference existing architecture** - Link to actual classes, methods, and interfaces in the codebase
- **Make tasks atomic and specific** - Each task should reference specific files and functions to modify

# Protocol Overview

For each new feature, create a folder in `flatmate/DOCS/_FEATURES/<feature_name>/` containing:

- `requirements.md` — Clear, testable requirements with acceptance criteria
- `design.md` — Architecture and technical design with specific file paths and class references
- `tasks.md` — Actionable implementation steps with specific file paths, function signatures, and code examples
- `discussion.md` — Stepwise commentary, decisions, and stakeholder input
- `implementation_guide.md` — Detailed technical guide with code examples and architecture context

---

## Stepwise Feature Implementation Flow

### 1. **Clarify the Feature**
- What is the desired behaviour?
- What is the motivation or problem being solved?
- Who are the stakeholders?
- **NEW**: Identify all affected files and components in the codebase
- Document this in `discussion.md` and update `requirements.md`.

### 2. **Collaborative Requirements Discussion**
- Product manager, lead dev, and others comment in `discussion.md` (use `>>` to denote roles).
- **NEW**: Include specific acceptance criteria with testable outcomes
- Finalise requirements in `requirements.md`.

### 3. **Architecture Analysis & Design Discussion**
- **NEW**: Use codebase-retrieval to identify all relevant files and components
- **NEW**: Document current implementation patterns and interfaces
- **NEW**: Identify specific classes, methods, and files that need modification
- Discuss architecture, data flow, edge cases in `discussion.md`.
- Update `design.md` with specific file paths and class references.

### 4. **Detailed Task Breakdown**
- **NEW**: Each task must include:
  - Specific file path (e.g., `flatmate/src/fm/gui/_shared_components/table_view_v2/components/table_view_core.py`)
  - Exact method/class names to modify
  - Code examples showing before/after states
  - Dependencies on other tasks
- List actionable steps in `tasks.md`.
- Create `implementation_guide.md` with detailed technical specifications.

### 5. **Implementation & Traceability**
- **NEW**: Before making changes, always use codebase-retrieval to understand current implementation
- **NEW**: Reference specific line numbers and existing code patterns
- At each key milestone, document decisions, blockers, and progress in `discussion.md`.
- Link PRs, commits, and test results for traceability.

### 6. **Review & Close**
- **NEW**: Include specific test cases and validation steps
- Summarise outcomes, lessons learned, and any follow-ups in `discussion.md`.
- Ensure all .mds are up to date and linked in the main tracking doc.

---

## AI-Specific Guidelines

### For Less Capable AI Systems:
1. **Always start with codebase analysis** - Use codebase-retrieval before making any changes
2. **Reference specific files and line numbers** - Never use vague references
3. **Provide complete code examples** - Show full method implementations, not just snippets
4. **Include error handling patterns** - Show how to handle common failure cases
5. **Reference existing patterns** - Point to similar implementations in the codebase
6. **Make dependencies explicit** - Clearly state what needs to be done before each task

### Required Documentation Sections:

#### In `design.md`:
- **Current Architecture**: Specific file paths and class hierarchy
- **Affected Components**: List of all files that need modification
- **Interface Contracts**: Method signatures and expected behavior
- **Data Flow**: Specific classes and methods involved in data processing

#### In `tasks.md`:
- **File Path**: Full path to file being modified
- **Method Signature**: Exact function signature with parameters and return types
- **Code Example**: Before and after code snippets
- **Dependencies**: Other tasks that must be completed first
- **Testing**: Specific test cases to validate the change

#### In `implementation_guide.md`:
- **Step-by-step implementation** with code examples
- **Common pitfalls** and how to avoid them
- **Testing strategies** with specific test cases
- **Integration points** with existing systems

---

## Key Principles
- **Specificity over generality** - Always provide concrete, actionable guidance
- **Code examples over descriptions** - Show, don't just tell
- **Architecture awareness** - Reference actual codebase structure and patterns
- **Atomic tasks** - Each task should be completable in ~20 minutes by a competent developer
- **Testable outcomes** - Every requirement and task should have clear success criteria

---

**This protocol ensures bulletproof implementation guidance for AI systems of all capability levels.**

---

*updated 2025-07-18 v3. Optimized for AI implementation reliability.*

---