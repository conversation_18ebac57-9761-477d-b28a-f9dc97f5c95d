# Transaction Categorisation Integration Plan

* DOCUMENTATION IN PROGRESS: #comment for git highlight


_Last updated: 2025-06-15_

## 1  Purpose & Scope
Build the **MVP release** of Flatmate focused on:
1. Bank-statement ingestion & parsing
2. Local database interface (read/write transactions)
3. Transaction categorisation tooling (patterns now, ML later)

This plan documents how we will integrate categorisation into the existing application, re-using the current data-pipeline code (`dw_director.py`, `dw_pipeline.py`, `statement_handlers/`).

---
## 2  Current Workflow (Update-Data module)
```
update_data/utils/
├── dw_director.py       # Orchestrates the import pipeline
├── dw_pipeline.py       # Pure functions for each pipeline step
└── statement_handlers/  # Bank-specific CSV/XLSX parsers
```
Flow summary:
1. **`dw_director.process_files()`** (called from Update-Data presenter)  
   • backs-up originals, dispatches each file to a `statement_handler` via `dw_pipeline.process_with_handler`, merges into a master dataframe, saves, then updates DB via `update_database_records()`.
2. **Handlers** convert raw statements → standard column dataframe (`StandardColumns`).
3. **Database layer** (`DataService`, `transaction_repository`) persists new transactions.

👉 These pieces already cover _parsing_ and _storage_. What’s missing for categorisation is:
* category assignment logic
* UI to view/edit categories
* module wiring in `ModuleCoordinator`

---
## 3  Design-Doc vs Codebase Gaps
| Area | Design Doc (`fmbankprocessor/`) | Current Code |
|------|---------------------------------|--------------|
| Core processor | `BankProcessor` class orchestrating parse + categorise | Implicit orchestration by `dw_director` only for parsing |
| Categoriser | `TransactionCategorizer` w/ patterns & ML | **Not implemented** |
| Data models | `Transaction` dataclass | Pandas DF + DB model; mapping exists but not explicit dataclass |
| Packaging | Stand-alone reusable wheel | Integrated in-repo util scripts |

---
## 4  Target Architecture (Phase-1 MVP)
```
fm/modules/categorize/
├── core/
│   ├── processor.py          # Thin wrapper around existing dw_* flow + categoriser
│   ├── categorizer.py        # Pattern-based categorisation (JSON patterns)
│   └── models.py             # Transaction (dataclass → DB row helpers)
├── categorize_presenter.py   # Orchestrates core → DB → View
├── _view/
│   └── cat_view.py           # Table view + category editor UI
└── resources/
    └── patterns.json         # Default pattern rules
```
Key notes:
* **Reuse** `statement_handlers` and `dw_pipeline` by importing them from `update_data.utils` until a later refactor.
* `processor.process_file()` will delegate to `dw_director` logic, then pass resulting DF/list to `categorizer`.
* `categorizer` first applies static patterns; ML hook returns `"Uncategorised"` for now.
* Presenter pushes categorised data to DB and emits events for UI updates.

---
## 5  Implementation Steps
1. **Scaffold module** – copy `update_data` layout; create presenter, view, and core package.
2. **Factor out shared parsing logic** – move/alias `dw_*` utils into a shared namespace to avoid circular imports.
3. **Implement `categorizer.py`** – load pattern JSON, regex/substring matching.
4. **Wire into `ModuleCoordinator`** – add factory & NAV mapping.
5. **Build UI** – table with editable combo-box for category; save/undo.
6. **Persist categories** – extend `transaction_repository` with `category` field update.
7. **QA & smoke test** – parse sample statements, categorise, verify DB rows.

---
## 6  Open Questions / Decisions
1. Should we refactor `dw_director` into the new core package now or after MVP?
2. Where should patterns.json live for user editing (app data dir vs repo)?
3. Which DB table/column stores `category` (confirm current schema)?
4. Future ML model location & format.

---
_End of document_
