"""
Enhanced Filter Proxy Model

Filtering and sorting component for the Enhanced Table View System.
Provides per-column filtering and advanced search capabilities.
"""

from PySide6.QtCore import Qt, QSortFilterProxyModel


class EnhancedFilterProxyModel(QSortFilterProxyModel):
    """Enhanced filter proxy model with per-column filtering."""
    
    def __init__(self, parent=None):
        """Initialize the enhanced filter proxy model."""
        super().__init__(parent)
        self._column_filters = {}
    
    def set_column_filter(self, column, pattern: str):
        """Set filter for a specific column or all columns."""
        if not pattern:
            if column in self._column_filters:
                del self._column_filters[column]
        else:
            self._column_filters[column] = pattern
        self.invalidateFilter()
    
    def clear_filters(self):
        """Clear all filters."""
        self._column_filters.clear()
        self.invalidateFilter()
    
    def filterAcceptsRow(self, source_row, source_parent):
        """Check if row matches all column filters."""
        model = self.sourceModel()

        # If no filters, accept all rows
        if not self._column_filters:
            return True

        # Check each column filter
        for column, pattern in self._column_filters.items():

            # Handle "All Columns" search
            if column == "all_columns":
                # Search across all columns
                found_match = False
                for col_idx in range(model.columnCount()):
                    index = model.index(source_row, col_idx, source_parent)
                    if index.isValid():
                        data = model.data(index, Qt.DisplayRole)
                        if data is not None and pattern.lower() in str(data).lower():
                            found_match = True
                            break
                if not found_match:
                    return False
            else:
                # Single column search
                index = model.index(source_row, column, source_parent)
                if not index.isValid():
                    if source_row == 0:
                        print(f"DEBUG: Invalid index for column {column}")
                    continue

                data = model.data(index, Qt.DisplayRole)
                if data is None:
                    if source_row == 0:
                        print(f"DEBUG: No data for column {column}")
                    continue

                if pattern.lower() not in str(data).lower():
                    return False

        return True
