"""
Developer Settings Configuration

Provides configuration for developer mode settings in the Update Data module.
This module maintains a clean separation between UI and business logic.
"""

import os
from typing import Any, Dict, Optional

import yaml
from fm.core.services.logger import log


class DevSettings:
    """
    Manages developer mode settings for the Update Data module.

    This class provides a clean interface for accessing and modifying
    developer mode settings, maintaining separation between UI and configuration.
    """

    # Default settings
    DEFAULT_SETTINGS = {
        "enabled": False,
        "options": {
            "timestamped_csv": False,
            "tidy_originals": False,
            "db_output": False,
            "update_master": False,
        },
    }

    def __init__(self, config_dir: str = "~/.flatmate/config"):
        """
        Initialize the developer settings manager.

        Args:
            config_dir: Directory for configuration files
        """
        self.config_dir = os.path.expanduser(config_dir)
        self.config_file = os.path.join(self.config_dir, "dev_settings.yaml")
        self._settings = self._load_settings()

    def _load_settings(self) -> Dict[str, Any]:
        """
        Load settings from the configuration file.

        Returns:
            Dict containing the settings
        """
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, "r") as f:
                    settings = yaml.safe_load(f)

                # Validate and merge with defaults
                if not isinstance(settings, dict):
                    log("Invalid dev settings format, using defaults", level="warning")
                    return self.DEFAULT_SETTINGS.copy()

                # Ensure all required keys exist
                result = self.DEFAULT_SETTINGS.copy()
                if "enabled" in settings:
                    result["enabled"] = settings["enabled"]

                if "options" in settings and isinstance(settings["options"], dict):
                    for key, value in settings["options"].items():
                        if key in result["options"]:
                            result["options"][key] = value

                return result
            else:
                return self.DEFAULT_SETTINGS.copy()
        except Exception as e:
            log(f"Error loading dev settings: {e}", level="error")
            return self.DEFAULT_SETTINGS.copy()

    def _save_settings(self) -> bool:
        """
        Save settings to the configuration file.

        Returns:
            True if successful, False otherwise
        """
        try:
            # Create directory if it doesn't exist
            os.makedirs(self.config_dir, exist_ok=True)

            with open(self.config_file, "w") as f:
                yaml.dump(self._settings, f, default_flow_style=False)

            return True
        except Exception as e:
            log(f"Error saving dev settings: {e}", level="error")
            return False

    def is_enabled(self) -> bool:
        """
        Check if developer mode is enabled.

        Returns:
            True if enabled, False otherwise
        """
        return self._settings.get("enabled", False)

    def set_enabled(self, enabled: bool) -> bool:
        """
        Enable or disable developer mode.

        Args:
            enabled: Whether to enable developer mode

        Returns:
            True if successful, False otherwise
        """
        self._settings["enabled"] = enabled
        return self._save_settings()

    def get_option(self, option_id: str) -> bool:
        """
        Get the value of a developer option.

        Args:
            option_id: Identifier of the option

        Returns:
            Current value of the option
        """
        options = self._settings.get("options", {})
        return options.get(option_id, False)

    def set_option(self, option_id: str, value: bool) -> bool:
        """
        Set the value of a developer option.

        Args:
            option_id: Identifier of the option
            value: New value for the option

        Returns:
            True if successful, False otherwise
        """
        if "options" not in self._settings:
            self._settings["options"] = {}

        if option_id in self.DEFAULT_SETTINGS["options"]:
            self._settings["options"][option_id] = value
            return self._save_settings()

        return False

    def get_all_options(self) -> Dict[str, bool]:
        """
        Get all developer options.

        Returns:
            Dictionary of option identifiers to values
        """
        return self._settings.get("options", {}).copy()

    def reset_to_defaults(self) -> bool:
        """
        Reset all settings to defaults.

        Returns:
            True if successful, False otherwise
        """
        self._settings = self.DEFAULT_SETTINGS.copy()
        return self._save_settings()
