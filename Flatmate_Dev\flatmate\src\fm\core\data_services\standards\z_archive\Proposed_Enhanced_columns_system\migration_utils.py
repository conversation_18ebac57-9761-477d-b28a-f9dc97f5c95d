"""
Migration utilities for transitioning from legacy to enhanced column system.

This module provides utilities to help migrate existing code from the old
StandardColumns enum to the new enhanced column system.
"""

import pandas as pd
from typing import Dict, List, Any, Optional
from ..fm_standard_columns import StandardColumns as LegacyStandardColumns
from .enhanced_columns import EnhancedStandardColumns
from .column_manager import get_column_manager


class ColumnMigrationHelper:
    """Helper class for migrating from legacy to enhanced column system."""
    
    def __init__(self):
        self.column_manager = get_column_manager()
        self._legacy_to_enhanced_mapping = self._build_legacy_mapping()
    
    def _build_legacy_mapping(self) -> Dict[str, EnhancedStandardColumns]:
        """Build mapping from legacy column names to enhanced columns."""
        mapping = {}
        
        # Map legacy enum values to enhanced columns
        legacy_mappings = {
            'Date': EnhancedStandardColumns.DATE,
            'Details': EnhancedStandardColumns.DETAILS,
            'Amount': EnhancedStandardColumns.AMOUNT,
            'Balance': EnhancedStandardColumns.BALANCE,
            'Account': EnhancedStandardColumns.ACCOUNT,
            'Unique Id': EnhancedStandardColumns.UNIQUE_ID,
            'Credit': EnhancedStandardColumns.CREDIT_AMOUNT,
            'Debit': EnhancedStandardColumns.DEBIT_AMOUNT,
            'Source Filename': EnhancedStandardColumns.SOURCE_FILENAME,
            'Payment Type': EnhancedStandardColumns.PAYMENT_TYPE,
        }
        
        return legacy_mappings
    
    def migrate_legacy_column_reference(self, legacy_column) -> Optional[EnhancedStandardColumns]:
        """
        Migrate a legacy StandardColumns reference to enhanced system.
        
        Args:
            legacy_column: Legacy StandardColumns enum value or string
            
        Returns:
            Corresponding EnhancedStandardColumns value or None
        """
        if hasattr(legacy_column, 'value'):
            # It's a legacy enum value
            return self._legacy_to_enhanced_mapping.get(legacy_column.value)
        elif isinstance(legacy_column, str):
            # It's a string
            return self._legacy_to_enhanced_mapping.get(legacy_column)
        
        return None
    
    def convert_legacy_display_columns(self, legacy_columns: List[str]) -> List[str]:
        """
        Convert legacy display column list to database column names.
        
        Args:
            legacy_columns: List of legacy column references (db_names or display names)
            
        Returns:
            List of database column names for enhanced system
        """
        result = []
        
        for col_ref in legacy_columns:
            # Try to find in enhanced system by db_name first
            enhanced_col = EnhancedStandardColumns.find_by_db_name(col_ref)
            if enhanced_col:
                result.append(enhanced_col.db_name)
                continue
            
            # Try to find by display name
            enhanced_col = EnhancedStandardColumns.find_by_display_name(col_ref)
            if enhanced_col:
                result.append(enhanced_col.db_name)
                continue
            
            # Try legacy mapping
            enhanced_col = self.migrate_legacy_column_reference(col_ref)
            if enhanced_col:
                result.append(enhanced_col.db_name)
                continue
            
            # Keep as-is if not found (might be custom column like 'tags', 'category')
            result.append(col_ref)
        
        return result
    
    def convert_legacy_column_mapping(self, legacy_mapping: Dict[str, str]) -> Dict[str, str]:
        """
        Convert legacy column name mapping to enhanced system.
        
        Args:
            legacy_mapping: Dictionary mapping old names to display names
            
        Returns:
            Dictionary mapping database names to display names
        """
        result = {}
        
        for old_name, display_name in legacy_mapping.items():
            # Convert old name to database name
            db_names = self.convert_legacy_display_columns([old_name])
            if db_names:
                result[db_names[0]] = display_name
        
        return result
    
    def migrate_dataframe_creation_code(self, transactions: List[Any]) -> pd.DataFrame:
        """
        Create DataFrame using enhanced column system (replacement for legacy code).
        
        This replaces code like:
        df = pd.DataFrame([t.__dict__ for t in txns])
        
        Args:
            transactions: List of Transaction objects
            
        Returns:
            DataFrame with proper database column names
        """
        return self.column_manager.convert_transactions_to_dataframe(transactions)
    
    def get_migration_suggestions(self, module_name: str) -> Dict[str, Any]:
        """
        Get suggestions for migrating a module to the enhanced column system.
        
        Args:
            module_name: Name of the module to migrate
            
        Returns:
            Dictionary with migration suggestions and code examples
        """
        suggestions = {
            'module_name': module_name,
            'recommended_columns': [],
            'code_examples': {},
            'breaking_changes': []
        }
        
        # Get recommended columns for this module
        display_info = self.column_manager.get_column_display_info(module_name)
        suggestions['recommended_columns'] = [
            {
                'db_name': info.db_name,
                'display_name': info.display_name,
                'is_editable': info.is_editable,
                'width': info.width
            }
            for info in display_info
        ]
        
        # Code examples
        suggestions['code_examples'] = {
            'get_display_columns': f"""
# Old way:
display_columns = [
    StandardColumns.DATE.db_name,
    StandardColumns.DETAILS.db_name,
    # ...
]

# New way:
column_manager = get_column_manager()
display_columns = column_manager.get_display_columns_for_module('{module_name}')
""",
            
            'get_column_mapping': f"""
# Old way:
column_names = {{
    StandardColumns.DATE.db_name: StandardColumns.DATE.value,
    StandardColumns.DETAILS.db_name: StandardColumns.DETAILS.value,
    # ...
}}

# New way:
column_manager = get_column_manager()
column_names = column_manager.get_column_display_mapping('{module_name}')
""",
            
            'create_dataframe': """
# Old way:
df = pd.DataFrame([t.__dict__ for t in transactions])

# New way:
column_manager = get_column_manager()
df = column_manager.convert_transactions_to_dataframe(transactions)
""",
            
            'prepare_for_display': f"""
# Old way:
# Manual column filtering and mapping

# New way:
column_manager = get_column_manager()
filtered_df, column_mapping, column_widths = column_manager.prepare_dataframe_for_display(df, '{module_name}')
"""
        }
        
        # Breaking changes to watch out for
        suggestions['breaking_changes'] = [
            "StandardColumns.DETAILS.value changed from 'Details' to 'Description'",
            "Column widths now use display names as keys instead of db names",
            "DataFrame creation now uses database column names consistently",
            "Custom columns (tags, category) are now part of the standard system"
        ]
        
        return suggestions


# Convenience functions for common migration tasks
def quick_migrate_display_columns(legacy_columns: List[str]) -> List[str]:
    """Quick migration of display columns list."""
    helper = ColumnMigrationHelper()
    return helper.convert_legacy_display_columns(legacy_columns)


def quick_migrate_column_mapping(legacy_mapping: Dict[str, str]) -> Dict[str, str]:
    """Quick migration of column name mapping."""
    helper = ColumnMigrationHelper()
    return helper.convert_legacy_column_mapping(legacy_mapping)


def quick_create_dataframe(transactions: List[Any]) -> pd.DataFrame:
    """Quick DataFrame creation with proper column names."""
    helper = ColumnMigrationHelper()
    return helper.migrate_dataframe_creation_code(transactions)


# Global helper instance
_migration_helper: Optional[ColumnMigrationHelper] = None

def get_migration_helper() -> ColumnMigrationHelper:
    """Get the global migration helper instance."""
    global _migration_helper
    if _migration_helper is None:
        _migration_helper = ColumnMigrationHelper()
    return _migration_helper
