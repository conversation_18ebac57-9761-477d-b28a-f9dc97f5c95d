# Info Bar

The InfoBar is a status bar widget that displays messages and loading statistics across the application. It's positioned at the bottom of the center panel and integrates with the global event bus for easy updates from any module.

## Features

- **Status Messages**: Display simple text messages
- **Loading Statistics**: Show progress during data loading operations
  - Record count (e.g., "1,234/5,000 records")
  - Elapsed time
  - Records per second
- **Event Bus Integration**: Automatically updates when events are published

## Usage

### Basic Message
```python
info_bar.set_message("Operation completed successfully")
```

### Loading Operation
```python
# Start loading
info_bar.start_loading("Loading transactions...")

try:
    # Process records in batches
    for i, record in enumerate(records):
        # Process record...
        
        # Update progress periodically
        if i % 100 == 0:  # Update every 100 records
            info_bar.update_progress(i, len(records))
    
    # Finish with success message
    info_bar.finish_loading(f"Loaded {len(records)} records")
    
except Exception as e:
    # Handle error
    info_bar.finish_loading(f"Error: {str(e)}")
```

### Clearing the Bar
```python
info_bar.clear()
```

## Implementation

The InfoBar is a `QWidget` that can be added to any window. It's typically placed at the bottom of the main application window.

### Adding to Main Window
```python
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        
        # Create and add info bar
        self.info_bar = InfoBar()
        self.setStatusBar(self.info_bar)
        
        # Or add to a layout
        # layout.addWidget(self.info_bar)
```

## Event Bus Integration

The InfoBar automatically subscribes to these events from the global event bus:

### Events
- `Events.INFO_MESSAGE`: Show a message or update progress
- `Events.INFO_CLEAR`: Clear the current message

### Message Formats

#### Simple Message
```python
from fm.core.services.event_bus import global_event_bus, Events

global_event_bus.publish(Events.INFO_MESSAGE, "Operation started")
```

#### Loading with Progress
```python
# Start loading
global_event_bus.publish(Events.INFO_MESSAGE, {
    "text": "Loading transactions...",
    "is_loading": True
})

# Update progress
global_event_bus.publish(Events.INFO_MESSAGE, {
    "progress": 42,
    "total": 100
})

# Complete loading
global_event_bus.publish(Events.INFO_MESSAGE, "Loaded 100 records")
```

#### Error Message
```python
try:
    # Code that might fail
    process_data()
except Exception as e:
    global_event_bus.publish(Events.INFO_MESSAGE, f"Error: {str(e)}")
```

## Styling

The InfoBar uses the following styles:
- Background: Light gray (`#f8f8f8`)
- Border: Light gray top border
- Text: Dark gray (`#444`), italic for regular messages
- Stats: Slightly lighter gray (`#666`), normal style

(service always on? started in main window at init?)

Notes ai is updating the loading code in categorize to use the "events system" it also has access to the info bar cvia its view.. so not sure what tf is going on her 