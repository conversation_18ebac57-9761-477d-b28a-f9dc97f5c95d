# Column Management System

This document outlines the centralized column management system, designed to provide a single source of truth for all column definitions, names, and properties throughout the application.

## Core Philosophy

The `Columns` class, located in `fm.core.data_services.standards.columns`, is the heart of this system. It replaces legacy helpers like `ColumnNameService` and `ColumnManager`, consolidating all column-related logic into one place. This approach simplifies maintenance, ensures consistency, and reduces the risk of errors caused by fragmented or duplicated column definitions.

## Naming Conventions: `db_name` vs. `display_name`

Every column is defined with two key name properties:

*   **`db_name`**: This is the official, internal name of the column as it exists in the database (e.g., `transaction_date`, `op_name`). It is used for all backend logic, database queries, and data processing. It should follow a consistent `snake_case` format.

*   **`display_name`**: This is the user-facing name that appears in the UI (e.g., "Date", "Transaction"). This allows the backend representation to remain stable while giving flexibility to present data in a more readable format.

**Example:**
```python
Column(db_name="transaction_date", display_name="Date", ...)
```

## Column Groups

To manage different sets of columns for various application contexts, columns are organized into groups. This allows different parts of the application to request only the columns they need, preventing errors like the duplicate `modified_date` issue.

### Key Groups

*   **`core_transaction`**: Represents the essential fields of a financial transaction that are either imported from a bank statement or derived during processing. These are the columns that typically form the basis of the `Transaction` dataclass.

*   **`user_editable`**: A subset of `core_transaction` columns that the user is allowed to modify directly in the UI (e.g., `category`, `notes`).

*   **`db_system`**: Columns that are managed exclusively by the database or backend services for internal tracking (e.g., `id`, `created_date`, `modified_date`). These columns should **never** be included in data models used for creating new transactions to avoid conflicts with the database's own management of these fields.

*   **`ui_helper`**: Columns that are used for display or interaction logic within the UI but are not part of the core transaction data (e.g., a selection checkbox).

### How to Use Groups

The `Columns` class provides methods to retrieve columns by their group:

```python
from fm.core.data_services import Columns

# Get all columns needed for a new transaction
transaction_cols = Columns.get_transaction_columns()

# Get all columns that can be edited by the user
editable_cols = Columns.get_columns_by_group('user_editable')

# Get display names for a list of db_names
display_names = Columns.get_display_names(['transaction_date', 'amount'])
```

## Best Practices

1.  **Always use the `Columns` class** for any column-related logic. Do not hardcode column names or properties elsewhere.
2.  When creating data models for new records (like a `Transaction`), use a specific helper method like `get_transaction_columns()` to ensure system-managed columns are excluded.
3.  The database repository is the only layer that should be directly aware of or interact with `db_system` columns.
4.  To add or modify a column, update its definition in the `Columns` registry. All dependent parts of the application will automatically inherit the change.
