# FlatMate Logger Module

## Overview
The logger module provides a centralized logging system for the FlatMate application, supporting both console and file output with configurable log levels.

## Features
- Multiple log levels (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- Shorthand level notation (e.g., 'e' for ERROR, 'd' for DEBUG)
- Automatic module detection
- Exception logging with traceback
- Function decorator for automatic entry/exit logging
- Event-based architecture using global event bus
- Configurable log directory and levels

## Installation & Setup
```python
# The logger is automatically set up when importing from the core package
from fm.core.services.logger import log, Logger, log_this
```

## Log Levels
| Level | Shorthand | Description |
|-------|-----------|-------------|
| DEBUG | 'd' | Detailed debug information |
| INFO | 'i' | General information about application progress |
| WARNING | 'w' | Indication of potential issues |
| ERROR | 'e' | Serious problems that prevented normal execution |
| CRITICAL | 'c' | Severe errors that may cause application failure |

## Basic Usage

### Using the log function
```python
from fm.core.services.logger import log, LogLevel

# Basic usage
log("Starting application")

# With explicit level
log("Debug information", "debug")  # Shorthand
log("Error occurred", LogLevel.ERROR)  # Using enum

# With exception logging
try:
    # Some code that might fail
    pass
except Exception as e:
    log("Operation failed", "error", exc_info=True)
```

### Using the Logger class
```python
from fm.core.services.logger import Logger

Logger.debug("Debug message")
Logger.info("Information message")
Logger.warning("Warning message")
Logger.error("Error message")
Logger.critical("Critical error")

# With custom module name
Logger.info("Custom module log", module="CUSTOM_MODULE")
```

### Function Decorator
```python
from fm.core.services.logger import log_this

@log_this('debug')
def process_data():
    # Function entry/exit will be logged at DEBUG level
    return "result"

@log_this(level='info')
def another_function():
    # Entry/exit logged at INFO level
    pass
```

## Configuration
Logging is configured through the global config manager. The following settings are available:

- `Paths.LOGS`: Directory where log files will be stored
- Log level can be set in `preferences.yaml` with `console_level`

## Log File Format
Log files are automatically created with the following format:
```
flatmate-YYYY-MM-DD_HH-MM-SS.log
```

Each log entry includes:
- Timestamp
- Log level
- Module name
- Message
- Exception traceback (if any)

## Best Practices
1. Use appropriate log levels
2. Include relevant context in log messages
3. Use the `@log_this` decorator for function tracing
4. Set `exc_info=True` when logging exceptions
5. Use module-specific logging when appropriate

## Example Output
```
[INFO] [main] Application started
[DEBUG] [data_processor] Processing data chunk
[ERROR] [api] Failed to connect to database
    Traceback (most recent call last):
      File "api.py", line 42, in connect
        db.connect()
    ConnectionError: Connection refused
```

## Current Issues

1. **Multiple Entry Points**
   - `log()` function with level as parameter
   - `Logger` class with static methods
   - Shorthand level notation (e.g., 'e' for error)
   - `log_this` decorator with multiple ways to specify level

2. **Inconsistencies**
   - `log()` vs `Logger` class methods
   - Shorthand vs full level names
   - Module parameter sometimes required
   - Inconsistent exception handling

## Proposed Simplification

### 1. Standardize on `log` object with methods
```python
# Current (multiple ways)
log("message", "info")
Logger.info("message")
log_this('debug')(func)

# Proposed (only this way)
log.info("message")
log.error("Something failed", exc_info=True)
log_this(level='info')(func)  # Only if decorator is really needed
```

### 2. Remove Redundancies
- Deprecate `Logger` class in favor of `log` object methods
- Remove shorthand level notation (just use full level names)
- Make module parameter optional with better auto-detection
- Standardize exception handling

### 3. Implementation Plan

1. **Phase 1: Add New Methods**
   - Add method-based interface to `log` object
   - Keep backward compatibility with deprecation warnings
   - Update documentation

2. **Phase 2: Update Codebase**
   - Update all internal code to use new style
   - Add deprecation warnings to old patterns
   - Update tests

3. **Phase 3: Cleanup (Next Major Version)**
   - Remove deprecated code paths
   - Simplify implementation

## Recommended Usage

```python
# Basic logging
log.info("Starting process")
log.debug("Debug information")
log.warning("This might be a problem")
log.error("Something went wrong")
log.critical("Application is unstable")

# With exception

try:
    # Some operation
    pass
except Exception as e:
    log.error("Operation failed", exc_info=True)

# With module context
log.info("Module-specific message", module="module_name")
```

## Benefits
- **Simplicity**: One obvious way to log
- **Discoverability**: IDE autocomplete works better with methods
- **Maintainability**: Less code to maintain
- **Consistency**: All logging follows the same pattern

## Next Steps
1. Review and approve the proposed changes
2. Implement Phase 1 changes
3. Update documentation
4. Schedule codebase updates

## Dependencies
- Python's built-in `logging` module
- `global_event_bus` for event handling
- `fm.core.config` for configuration management
