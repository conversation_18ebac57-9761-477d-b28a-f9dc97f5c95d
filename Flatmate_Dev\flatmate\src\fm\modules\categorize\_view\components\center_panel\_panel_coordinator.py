"""Center panel coordinator for the Categorize module."""

import pandas as pd
from PySide6.QtCore import Signal
from PySide6.QtWidgets import QVBoxLayout
import logging

from fm.gui._shared_components import BasePanelComponent

# Set up logger
logger = logging.getLogger(__name__)

class CenterPanelCoordinator(BasePanelComponent):
    """Coordinator for the center panel components.

    This class coordinates layout and signals between different components
    that can be shown in the center area of the Categorize module.
    """

    # Signals
    transaction_selected = Signal(int)
    tags_updated = Signal(int, str)

    def __init__(self, parent=None):
        """Initialize the center panel coordinator."""
        super().__init__(parent)
        logger.debug("Initializing CenterPanelCoordinator")
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        # Create main layout
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # Initialize components
        self._init_components()
        
        # Add center panel to layout
        self.main_layout.addWidget(self.center_panel)
    
    def _init_components(self):
        """Initialize the panel components."""
        # Import here to avoid circular imports
        from .transaction_view_panel import TransactionViewPanel

        logger.debug("Creating TransactionViewPanel with EnhancedTableWidget")
        self.center_panel = TransactionViewPanel()
    
    def _connect_signals(self):
        """Connect internal signals."""
        logger.debug("Connecting CenterPanelCoordinator signals")
        self.center_panel.transaction_selected.connect(self.transaction_selected)
        self.center_panel.tags_updated.connect(self.tags_updated)
    
    def set_transactions(self, df: pd.DataFrame):
        """Set the transactions dataframe to display."""
        logger.debug(f"CenterPanelCoordinator setting transactions: {len(df) if df is not None else 0} rows")
        if df is not None and not df.empty:
            self.center_panel.set_transactions(df)
    
    def show_component(self):
        """Show this component."""
        self.show()
        
    def hide_component(self):
        """Hide this component."""
        self.hide()
    
    def display_dataframe(self, df, file_info: str = ""):
        """Display a DataFrame in this component.
        
        Args:
            df: DataFrame to display
            file_info: Information about the file being displayed
        """
        self.set_transactions(df)
    
    def disconnect_signals(self):
        """Clean up signal connections."""
        logger.debug("Disconnecting CenterPanelCoordinator signals")
        try:
            self.center_panel.transaction_selected.disconnect(self.transaction_selected)
            self.center_panel.tags_updated.disconnect(self.tags_updated)
            self.center_panel.disconnect_signals()
        except (TypeError, RuntimeError) as e:
            # Signal might not be connected
            logger.debug(f"Error disconnecting signals: {e}")


