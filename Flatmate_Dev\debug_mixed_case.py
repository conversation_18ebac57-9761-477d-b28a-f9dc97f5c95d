#!/usr/bin/env python3
"""
Debug the mixed OR/AND case that's failing.
"""

import sys
import os

# Add the flatmate source to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'flatmate', 'src'))

def debug_mixed_case():
    """Debug the specific failing case."""
    print("=== Debugging Mixed OR/AND Case ===")
    
    try:
        from fm.gui._shared_components.table_view_v2.components.table_utils.enhanced_filter_proxy_model import EnhancedFilterProxyModel
        
        proxy = EnhancedFilterProxyModel()
        
        # The failing case
        data = "Coffee Tea Shop"
        pattern = "coffee|tea hot"
        
        print(f"Data: '{data}'")
        print(f"Pattern: '{pattern}'")
        print()
        
        # Parse the pattern
        parsed = proxy._parse_filter_pattern_v2(pattern)
        print(f"Parsed result: {parsed}")
        print()
        
        # Check the matching logic step by step
        data_lower = data.lower()
        print(f"Data (lowercase): '{data_lower}'")
        
        if parsed["type"] == "or_expression":
            or_groups = parsed["or_groups"]
            exclude_terms = parsed["exclude_terms"]
            
            print(f"OR groups: {or_groups}")
            print(f"Exclude terms: {exclude_terms}")
            print()
            
            # Check exclude terms first
            for term in exclude_terms:
                if term in data_lower:
                    print(f"❌ Exclude term '{term}' found in data - REJECT")
                    return False
                else:
                    print(f"✅ Exclude term '{term}' not found in data")
            
            # Check OR groups
            for i, or_group in enumerate(or_groups):
                print(f"\nChecking OR group {i+1}: {or_group}")
                group_matched = False
                for term in or_group:
                    if term in data_lower:
                        print(f"  ✅ Term '{term}' found in data")
                        group_matched = True
                        break
                    else:
                        print(f"  ❌ Term '{term}' not found in data")
                
                if not group_matched:
                    print(f"❌ OR group {i+1} didn't match - REJECT")
                    return False
                else:
                    print(f"✅ OR group {i+1} matched")
            
            print("\n✅ All conditions met - ACCEPT")
            return True
        
        return False
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Debug the mixed case."""
    result = debug_mixed_case()
    
    print(f"\nFinal result: {result}")
    print("Expected: True")
    
    if result:
        print("✅ Case is working correctly!")
    else:
        print("❌ Case needs fixing")
        print("\nThe issue is likely in how we parse mixed OR/AND patterns.")
        print("Pattern 'coffee|tea hot' should be interpreted as:")
        print("  - OR group 1: ['coffee', 'tea']")
        print("  - OR group 2: ['hot']")
        print("  - Both groups must match (AND between groups)")

if __name__ == "__main__":
    main()
