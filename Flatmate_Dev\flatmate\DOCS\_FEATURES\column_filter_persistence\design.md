# Technical Design

## Current Architecture
- **File:** `src/fm/gui/_shared_components/table_view_v2/components/toolbar/groups/filter_group.py`
  - `FilterInput`, `FilterGroup`: UI for filter entry and pattern emission.
- **File:** `src/fm/gui/_shared_components/table_view_v2/components/table_utils/enhanced_filter_proxy_model.py`
  - `EnhancedFilterProxyModel.filterAcceptsRow`: Applies filter pattern to table rows.
- **File:** `src/fm/gui/_shared_components/table_view_v2/components/table_view_core.py`
  - `TableViewCore.set_column_filter`: Passes pattern to proxy model.
- **File:** `src/fm/gui/_shared_components/table_view_v2/fm_table_view.py`
  - `CustomTableView_v2._on_filter_applied`: Handles filter_applied signal.

## Required Changes
- **Persistence:**
  - The filter pattern and column selection should be set and restored by the parent/calling widget (e.g., CustomTableView_v2), not by FilterGroup directly.
  - Persistence and default values (including details column selection) must be handled/configured via the widget's config, in line with the app-wide widget pattern (see flatmate/DOCS/_ARCHITECTURE/_UI_system/APP_WIDE_WIDGET_PATTERN.md).
  - FilterGroup remains a passive UI component, with state set externally.
  - Store and restore last-used filter column and pattern (per user/device, via config or local storage).
  - Add config key to enable/disable persistence (default: enabled).
  - Restore filter state on widget init if enabled.
  - Save filter state on change.
- **Filtering Logic:**
  - Parse pattern into positive (AND) and negative (exclude) terms.
  - Operator tokens (such as `-` for exclude) are only treated as operators if surrounded by spaces; otherwise, they are part of the search term. For rare cases where a search term starts with `-`, use quotes or a * prefix to force literal matching.
  - Update `filterAcceptsRow` to require all AND terms and reject any EXCLUDE term.
  - UI: Ensure `-` is visually distinct as an operator (hint/tooltip, not part of search term).

## Integration Points
- **Persistence:**
  - Likely in `FilterGroup` or parent TableView/toolbar; use config or QSettings for storage.
- **Logic:**
  - Pattern parsing in `EnhancedFilterProxyModel` or just before calling `set_column_filter`.
- **UI:**
  - Add tooltip or small label near filter input explaining `-` for exclusion.
