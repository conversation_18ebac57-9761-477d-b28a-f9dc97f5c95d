"""Enhanced Base Local Configuration Manager V2.

This is the next generation config system that combines:
- Override hierarchy from the original BaseLocalConfig
- Usage-based config definition (no speculation)
- Source tracking for debugging
- Simple string keys (no complex enums)
- Auto-generated documentation from actual usage

Philosophy:
- Config keys are defined WHERE they're used, not in separate files
- Only keys that are actually used exist in the system
- Discoverability through actual usage patterns
- Self-documenting code with source tracking
"""

import inspect
from abc import ABC
from pathlib import Path
from typing import Dict, Any, Optional

import yaml

from ..services.logger import log
from .config import config as core_config


class BaseLocalConfigV2(ABC):
    """Enhanced base configuration manager for components.
    
    Key improvements over V1:
    1. No speculative defaults - only keys that are ensure_defaults() exist
    2. Source tracking for debugging and documentation
    3. Simple string keys instead of complex enums
    4. Usage-based discoverability
    5. Maintains override hierarchy from V1
    
    Subclasses MUST define MODULE_NAME class attribute.
    
    Override Hierarchy (highest to lowest priority):
    1. User preferences (~/.flatmate/preferences.yaml)
    2. Component defaults (defaults.yaml)
    3. Runtime ensure_defaults() calls
    """
    
    # Subclasses MUST define this
    MODULE_NAME = None  # e.g., "categorize", "reports", "update_data"

    def __init__(self):
        """Initialize the enhanced config system."""
        # Enforce explicit module naming
        if not hasattr(self.__class__, 'MODULE_NAME') or self.__class__.MODULE_NAME is None:
            raise RuntimeError(
                f"{self.__class__.__name__} must define MODULE_NAME class attribute. "
                f"Example: MODULE_NAME = 'categorize'"
            )
        
        # Link to the V1 core config manager
        self.core_config = core_config

        # Initialize storage for V2 features (source tracking, etc.)
        self._key_origins = {}
        self._user_preferences = {}
        self._component_defaults = {}
        
        # Load override hierarchy
        self._load_override_hierarchy()

        # Log initialization
        log.debug(f"{self.__class__.__name__} for {self.MODULE_NAME} initialized.")

    # --------------------------------------------------------------------------
    # Public API
    # --------------------------------------------------------------------------

    def ensure_defaults(self, defaults_dict: Dict[str, Any], source: str = None):
        """Enhanced ensure_defaults that writes to the central V1 config store.

        Args:
            defaults_dict: Dictionary of default key-value pairs to ensure
            source: Optional source description (auto-detected if None)
        """
        # Auto-detect source if not provided
        if source is None:
            try:
                stack = inspect.stack()
                caller_frame = stack[1]
                filename = caller_frame.filename
                func_name = caller_frame.function
                source = f"{Path(filename).name}:{func_name}"
            except Exception:
                source = "unknown"

        for key, default_value in defaults_dict.items():
            # Check if the key already exists in the V1 config
            existing_value = self.core_config.get_value(key)

            if existing_value is None:
                # Key does not exist, so we set it.
                # First, determine the correct value based on the V2 override hierarchy
                # (e.g., component_defaults.yaml might have a value)
                final_value, is_overridden = self._apply_override_hierarchy(
                    key, default_value
                )

                # Set the value in the central V1 config store
                self.set_value(key, final_value)

                # Track key origin for V2's documentation features
                self._key_origins[key] = {
                    "source": source,
                    "default_value": default_value,
                    "final_value": final_value,
                    "is_overridden": is_overridden,
                }

                log.debug(
                    f"Set new config key in core config: "
                    f"{key} = {final_value} (Source: {source})"
                )
            else:
                # Key already exists, do nothing to the value.
                # We can still log its origin for documentation purposes if this is the first time V2 has seen it.
                if key not in self._key_origins:
                    self._key_origins[key] = {
                        "source": "Existing in core_config",
                        "default_value": None,
                        "final_value": existing_value,
                        "is_overridden": True,
                    }

    def get_value(self, key: str, default: Any = None) -> Any:
        """Get a config value by key from the central V1 config store."""
        return self.core_config.get_value(key, default)
    
    def set_value(self, key: str, value: Any):
        """Set a config value by key in the central V1 config store."""
        self.core_config.set_value(key, value)

    # --------------------------------------------------------------------------
    # Documentation & Debugging
    # --------------------------------------------------------------------------

    def get_key_origins(self) -> Dict[str, Dict[str, Any]]:
        """Get information about where each config key was set."""
        return self._key_origins.copy()

    def list_available_keys(self) -> Dict[str, Any]:
        """List all config keys that have been defined through actual usage in this module.

        Returns:
            Dictionary mapping config keys to their current values
        """
        return {key: info['final_value'] for key, info in self._key_origins.items()}

    def set_user_preference(self, key: str, value: Any):
        """Set a user preference that overrides defaults.

        This saves the preference to ~/.flatmate/preferences.yaml and immediately
        updates the core config so the change takes effect.

        Args:
            key: Config key to set (e.g., 'categorize.display.table_margin')
            value: Value to set
        """
        # Validate that this key exists (was defined through usage)
        if key not in self._key_origins:
            available_keys = list(self._key_origins.keys())
            raise ValueError(
                f"Config key '{key}' not found. Available keys: {available_keys}. "
                f"Keys must be defined through ensure_defaults() before they can be overridden."
            )

        # Update user preferences file
        self._save_user_preference(key, value)

        # Update core config immediately so change takes effect
        self.core_config.set_value(key, value)

        # Update our local cache
        self._user_preferences[key] = value

        log.info(f"Set user preference: {key} = {value}")

    def get_user_preference(self, key: str) -> Any:
        """Get current user preference for a key.

        Args:
            key: Config key to get

        Returns:
            User preference value, or None if no user preference is set
        """
        return self._user_preferences.get(key)

    def reset_to_default(self, key: str):
        """Reset a key back to its original default value.

        This removes any user preference override and restores the original
        default value that was set through ensure_defaults().

        Args:
            key: Config key to reset
        """
        if key not in self._key_origins:
            raise ValueError(f"Config key '{key}' not found")

        # Remove from user preferences
        if key in self._user_preferences:
            del self._user_preferences[key]
            self._remove_user_preference(key)

        # Reset to original default value
        original_default = self._key_origins[key]['default_value']
        self.core_config.set_value(key, original_default)

        log.info(f"Reset config key to default: {key} = {original_default}")

    def export_user_preferences(self) -> str:
        """Export current user preferences for this module as YAML.

        Returns:
            YAML string containing only the user preferences for this module
        """
        if not self._user_preferences:
            return f"# No user preferences set for {self.MODULE_NAME} module\n"

        lines = [
            f"# User Preferences for {self.MODULE_NAME.title()} Module",
            f"# These override the default values",
            ""
        ]

        # Group preferences by namespace
        namespaces = {}
        for key, value in self._user_preferences.items():
            # Remove module prefix for cleaner YAML
            clean_key = key
            if key.startswith(f'{self.MODULE_NAME}.'):
                clean_key = key[len(self.MODULE_NAME) + 1:]

            # Split into namespace parts
            parts = clean_key.split('.')
            if len(parts) > 1:
                namespace = parts[0]
                leaf_key = '.'.join(parts[1:])
            else:
                namespace = 'general'
                leaf_key = clean_key

            if namespace not in namespaces:
                namespaces[namespace] = {}

            # Handle nested keys
            if '.' in leaf_key:
                leaf_parts = leaf_key.split('.')
                current = namespaces[namespace]
                for part in leaf_parts[:-1]:
                    if part not in current:
                        current[part] = {}
                    current = current[part]
                current[leaf_parts[-1]] = value
            else:
                namespaces[namespace][leaf_key] = value

        # Write the nested structure
        for namespace in sorted(namespaces.keys()):
            lines.append(f"{namespace}:")
            self._write_yaml_dict(namespaces[namespace], lines, indent=1)
            lines.append("")

        return "\n".join(lines)

    def generate_documented_yaml(self) -> str:
        """Generate YAML organized by source file and function/class."""
        lines = [
            f"# {self.MODULE_NAME.title()} Module Configuration", 
            "# Auto-generated from actual usage", 
            "# Organized by source file and function/class", 
            ""
        ]

        # Group keys by source file and function
        sources = {}
        
        for key, origin in self._key_origins.items():
            source = origin['source']
            value = origin['final_value']
            default_value = origin['default_value']
            is_overridden = origin['is_overridden']

            if ':' in source:
                filename, func_name = source.split(':', 1)
            else:
                filename = source
                func_name = 'unknown_function'

            if filename not in sources:
                sources[filename] = {}
            
            if func_name not in sources[filename]:
                sources[filename][func_name] = []
            
            sources[filename][func_name].append({
                'key': key,
                'value': value,
                'default_value': default_value,
                'overridden': is_overridden
            })

        # Generate YAML content from grouped sources
        for filename in sorted(sources.keys()):
            lines.append(f"# Source: {filename}")
            for func_name in sorted(sources[filename].keys()):
                lines.append(f"#  - Function/Class: {func_name}")
                # Group keys by namespace within this function
                func_keys = sources[filename][func_name]
                namespaces = {}
                
                for item in func_keys:
                    key = item['key']
                    value = item['value']
                    default_value = item['default_value']
                    is_overridden = item['overridden']

                    # Remove module prefix for cleaner YAML
                    clean_key = key
                    if key.startswith(f'{self.MODULE_NAME}.'):
                        clean_key = key[len(self.MODULE_NAME) + 1:]
                    
                    # Split into namespace parts
                    parts = clean_key.split('.')
                    if len(parts) > 1:
                        namespace = parts[0]
                        leaf_key = '.'.join(parts[1:])
                    else:
                        namespace = 'general'
                        leaf_key = clean_key
                    
                    if namespace not in namespaces:
                        namespaces[namespace] = {}
                    
                    # Handle nested keys
                    if '.' in leaf_key:
                        leaf_parts = leaf_key.split('.')
                        current = namespaces[namespace]
                        for part in leaf_parts[:-1]:
                            if part not in current:
                                current[part] = {}
                            current = current[part]
                        
                        # Add override info as comment
                        if is_overridden:
                            current[f"#{leaf_parts[-1]}_comment"] = f"Overridden from default: {default_value}"
                        current[leaf_parts[-1]] = value
                    else:
                        if is_overridden:
                            namespaces[namespace][f"#{leaf_key}_comment"] = f"Overridden from default: {default_value}"
                        namespaces[namespace][leaf_key] = value
                
                # Write the nested structure
                for namespace in sorted(namespaces.keys()):
                    lines.append(f"{namespace}:")
                    self._write_yaml_dict(namespaces[namespace], lines, indent=1)
                
                lines.append("")
            
            lines.append("")
        
        return "\n".join(lines)

    def save_defaults_yaml(self, filename: str = None) -> Path:
        """Save the generated YAML to a defaults file.

        The file is saved next to the inheriting config class, not the base class.
        Default filename is defaults.yaml (consistent with config.py)
        """
        if filename is None:
            filename = "defaults.yaml"

        yaml_content = self.generate_documented_yaml()

        # Save next to the defaults.yaml file (where the inheriting config lives)
        defaults_path = self.get_defaults_file_path()
        if defaults_path:
            test_file = defaults_path.parent / filename
        else:
            # Fallback to current directory if no defaults path
            test_file = Path.cwd() / filename

        with open(test_file, 'w') as f:
            f.write(yaml_content)

        log.info(f"Defaults YAML for {self.MODULE_NAME} saved to: {test_file}")
        return test_file

    # --------------------------------------------------------------------------
    # Internal Methods
    # --------------------------------------------------------------------------

    def get_defaults_file_path(self) -> Optional[Path]:
        """Get the path to the component's defaults.yaml file.
        
        Override this in subclasses to specify the correct path.
        Default assumes defaults.yaml is in the same directory as the config file.
        """
        return Path(__file__).parent / "defaults.yaml"

    def _load_override_hierarchy(self):
        """Load the config override hierarchy."""
        # Load component defaults from defaults.yaml (if exists)
        self._component_defaults = self._load_component_defaults()
        
        # Load user preferences (if exists)
        self._user_preferences = self._load_user_preferences()
        
        log.debug(
            f"Loaded config hierarchy for {self.MODULE_NAME}: "
            f"{len(self._component_defaults)} component defaults, "
            f"{len(self._user_preferences)} user preferences"
        )

    def _load_component_defaults(self) -> Dict[str, Any]:
        """Load component-specific defaults.yaml file."""
        defaults_path = self.get_defaults_file_path()
        if not defaults_path or not defaults_path.exists():
            return {}
        
        try:
            with open(defaults_path, "r", encoding="utf-8") as f:
                component_defaults = yaml.safe_load(f) or {}
            
            # Filter for this module's keys only
            module_defaults = {}
            prefix = f"{self.MODULE_NAME}."
            
            for key, value in component_defaults.items():
                if key.startswith(prefix):
                    module_defaults[key] = value
            
            log.debug(f"Loaded {len(module_defaults)} component defaults for {self.MODULE_NAME} from {defaults_path}")
            return module_defaults

        except Exception as e:
            log.warning(f"Error loading component defaults from {defaults_path}: {e}")
            return {}

    def _load_user_preferences(self) -> Dict[str, Any]:
        """Load user preferences from ~/.flatmate/preferences.yaml."""
        prefs_path = Path("~/.flatmate/preferences.yaml").expanduser()
        if not prefs_path.exists():
            return {}
        
        try:
            with open(prefs_path, "r", encoding="utf-8") as f:
                user_prefs = yaml.safe_load(f) or {}
            
            # Filter for this module's keys only
            module_prefs = {}
            prefix = f"{self.MODULE_NAME}."

            for key, value in user_prefs.items():
                if key.startswith(prefix):
                    module_prefs[key] = value

            return module_prefs
            
        except Exception as e:
            log.warning(f"Error loading user preferences from {prefs_path}: {e}")
            return {}

    def _apply_override_hierarchy(self, key: str, default_value: Any) -> tuple[Any, bool]:
        """Apply config override hierarchy for a key.
        
        Priority (highest to lowest):
        1. User preferences
        2. Component defaults
        3. Provided default value
        """
        # 1. Check user preferences
        if key in self._user_preferences:
            return self._user_preferences[key], True
        
        # 2. Check component defaults
        if key in self._component_defaults:
            return self._component_defaults[key], True
        
        # 3. Use provided default (lowest priority)
        return default_value, False
    
    def _get_user_preferences_path(self) -> Path:
        """Get the path to the user preferences file.

        Uses core_config paths if available, otherwise falls back to standard location.
        """
        # Try to use core_config path management if available
        if hasattr(self.core_config, 'get_user_preferences_path'):
            return self.core_config.get_user_preferences_path()
        elif hasattr(self.core_config, 'get_user_data_dir'):
            return self.core_config.get_user_data_dir() / "preferences.yaml"
        else:
            # Fallback to standard location
            return Path("~/.flatmate/preferences.yaml").expanduser()

    def _save_user_preference(self, key: str, value: Any):
        """Save a user preference to the preferences file."""
        prefs_path = self._get_user_preferences_path()

        # Ensure directory exists
        prefs_path.parent.mkdir(parents=True, exist_ok=True)

        # Load existing preferences
        existing_prefs = {}
        if prefs_path.exists():
            try:
                with open(prefs_path, "r", encoding="utf-8") as f:
                    existing_prefs = yaml.safe_load(f) or {}
            except Exception as e:
                log.warning(f"Error loading existing preferences from {prefs_path}: {e}")

        # Update with new preference
        existing_prefs[key] = value

        # Save back to file
        try:
            with open(prefs_path, "w", encoding="utf-8") as f:
                yaml.safe_dump(existing_prefs, f, default_flow_style=False, sort_keys=True)
            log.debug(f"Saved user preference {key} = {value} to {prefs_path}")
        except Exception as e:
            log.error(f"Error saving user preference to {prefs_path}: {e}")
            raise

    def _remove_user_preference(self, key: str):
        """Remove a user preference from the preferences file."""
        prefs_path = self._get_user_preferences_path()

        if not prefs_path.exists():
            return  # Nothing to remove

        try:
            # Load existing preferences
            with open(prefs_path, "r", encoding="utf-8") as f:
                existing_prefs = yaml.safe_load(f) or {}

            # Remove the key if it exists
            if key in existing_prefs:
                del existing_prefs[key]

                # Save back to file
                with open(prefs_path, "w", encoding="utf-8") as f:
                    yaml.safe_dump(existing_prefs, f, default_flow_style=False, sort_keys=True)

                log.debug(f"Removed user preference {key} from {prefs_path}")

        except Exception as e:
            log.error(f"Error removing user preference from {prefs_path}: {e}")
            raise

    def _write_yaml_dict(self, data, lines, indent=0):
        """Helper to write nested dictionary as YAML."""
        indent_str = "  " * indent

        for key, value in sorted(data.items()):
            if key.startswith('#') and key.endswith('_comment'):
                # Skip comment keys in main output
                continue
            elif isinstance(value, dict):
                lines.append(f"{indent_str}{key}:")
                self._write_yaml_dict(value, lines, indent + 1)
            else:
                if isinstance(value, str):
                    lines.append(f"{indent_str}{key}: '{value}'")
                else:
                    lines.append(f"{indent_str}{key}: {value}")
