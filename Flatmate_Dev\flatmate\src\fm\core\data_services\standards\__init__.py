# fm/core/standards
"""
Core standards module for Flatmate application.

This module provides the canonical column system and related utilities.
The Columns registry is the primary/canonical system.
"""

# New centralized column registry
from .column_definition import Column
from .columns import Columns


# NEW: Elegant date formatting service
from fm.core.data_services.date_format_service import DateFormatService

# Public API
__all__ = [
    'Column',                 # Column dataclass definition
    'Columns',                # Centralized column registry
    'DateFormatService',      # Elegant date formatting service
    'DateDisplayFormat',      # Date format options
]