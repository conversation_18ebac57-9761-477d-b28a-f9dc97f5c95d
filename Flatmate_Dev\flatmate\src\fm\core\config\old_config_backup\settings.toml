[default]
# Application Configuration
app_name = "FlatMate"
debug_mode = false
version = "0.1.0"

# UI Preferences
[default.ui]
theme = "light"
font_size = 12
color_scheme = "default"
show_confirmations = true
remember_last_directory = true
window_width = 1200
window_height = 800

# Data Import Settings
[default.data_import]
default_directory = "~/Documents/Flatmate"
supported_banks = ["kiwibank", "anz", "westpac"]
auto_backup_enabled = true
backup_location = "utils/fm_backup"
preserve_original_files = true
backup_on_import = true
backup_on_process = true

# Logging Configuration
[default.logging]
level = "INFO"
file_logging_enabled = true
log_directory = "logs"
max_log_files = 5
log_retention_days = 30

# User Profile Settings
[default.user_profile]
first_time_launch = true
auto_update_check = true

# Experimental Features
[default.experimental]
data_analysis_enabled = false
machine_learning_insights = false

# Testing Configuration
[default.testing]
use_test_data = false
test_data_location = "tests/test_data"
mock_file_operations = false

# Development Environment
[development]
debug_mode = true
logging.level = "DEBUG"
testing.use_test_data = true
testing.mock_file_operations = true

# Production Environment
[production]
debug_mode = false
logging.level = "WARNING"
testing.use_test_data = false
testing.mock_file_operations = false
