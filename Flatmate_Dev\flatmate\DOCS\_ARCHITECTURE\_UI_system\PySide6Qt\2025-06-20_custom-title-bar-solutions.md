# Custom Title Bar Solutions for PySide6

## Overview

This document covers custom title bar implementations for PySide6 applications, focusing on frameless windows and the challenges of window state management.

## The Problem

Custom title bars in Qt applications face several challenges:

1. **Window State Synchronization** - Maximize/restore button states getting out of sync
2. **Platform Differences** - Inconsistent behavior across Windows, Linux, macOS
3. **Event Timing Issues** - `WindowStateChange` events firing at inconsistent times
4. **Complex State Management** - Multiple events for single state changes

## Current Implementation Issues

Our custom implementation in `window_controls.py` experiences:

- **3-click problem**: Takes multiple clicks to properly maximize/restore
- **State desynchronization**: Button icons don't match actual window state
- **Event timing**: `WindowStateChange` events fire before actual state changes
- **Platform inconsistencies**: Different behavior on different operating systems

### Debug Output Analysis

```
DEBUG: Window is normal, calling showMaximized()
DEBUG: WindowStateChange event - isMaximized=False  # ❌ Still False!
DEBUG: WindowStateChange event - isMaximized=False  # ❌ Still False!
# Later...
DEBUG: WindowStateChange event - isMaximized=True   # ✅ Finally True
```

The issue: Qt fires multiple `WindowStateChange` events, and the first ones report incorrect states.

## Recommended Solution: PyQt-Frameless-Window

### Why This Library?

1. **Battle-tested** - Handles all the edge cases we're experiencing
2. **Cross-platform** - Consistent behavior across all operating systems
3. **Mature** - 570+ stars, actively maintained
4. **PySide6 support** - Dedicated PySide6 branch available
5. **Customizable** - Can preserve our existing title bar design

### Installation

```bash
pip install PySide6-Frameless-Window
```

### Basic Integration

```python
from qframelesswindow import FramelessWindow

class MainWindow(FramelessWindow):  # Change from QMainWindow
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
        # Optional: Replace default title bar with custom one
        self.setTitleBar(self.title_bar)
```

### Advanced Customization

```python
from qframelesswindow import FramelessWindow, StandardTitleBar

class CustomTitleBar(StandardTitleBar):
    def __init__(self, parent):
        super().__init__(parent)
        
        # Customize button colors
        self.minBtn.setHoverColor(Qt.white)
        self.minBtn.setHoverBackgroundColor(QColor(0, 100, 182))
        
        # Use QSS for advanced styling
        self.maxBtn.setStyleSheet("""
            TitleBarButton {
                qproperty-hoverColor: white;
                qproperty-hoverBackgroundColor: rgb(0, 100, 182);
            }
        """)

class MainWindow(FramelessWindow):
    def __init__(self):
        super().__init__()
        self.setTitleBar(CustomTitleBar(self))
```

## Alternative: QT-PyQt-PySide-Custom-Widgets

### JSON-Based Configuration

```bash
pip install QT-PyQt-PySide-Custom-Widgets
```

```json
{
    "QMainWindow": [{
        "tittle": "Flatmate",
        "icon": ":/icons/app_icon.svg",
        "frameless": true,
        "navigation": [{
            "minimize": "minimize_button",
            "close": "close_button",
            "restore": [{
                "buttonName": "restore_button",
                "normalIcon": ":/icons/maximize.svg",
                "maximizedIcon": ":/icons/restore.svg"
            }]
        }]
    }]
}
```

## Migration Strategy

### Phase 1: Library Integration
1. Install PyQt-Frameless-Window
2. Change base class from `QMainWindow` to `FramelessWindow`
3. Test basic functionality

### Phase 2: Custom Title Bar Preservation
1. Adapt existing `CustomTitleBar` to work with the library
2. Remove custom window state management code
3. Let library handle maximize/restore logic

### Phase 3: Cleanup
1. Remove debugging code
2. Remove custom event handlers
3. Simplify window controls implementation

## Benefits of Migration

1. **Reliability** - No more state synchronization issues
2. **Maintainability** - Less custom code to maintain
3. **Cross-platform** - Consistent behavior everywhere
4. **Professional** - Handles edge cases we haven't encountered yet
5. **Future-proof** - Library is actively maintained

## Implementation Notes

### Preserving Current Design
- Keep existing button styling and layout
- Maintain current color scheme and icons
- Preserve window dragging and resizing behavior

### Code Removal
After migration, we can remove:
- Custom `changeEvent` handling
- Window state synchronization logic
- Timeout mechanisms and debugging code
- Complex event handling in `window_controls.py`

## Next Steps

1. **Install library**: `pip install PySide6-Frameless-Window`
2. **Create test branch**: Test integration without breaking current functionality
3. **Minimal integration**: Change base class and test
4. **Preserve styling**: Adapt current title bar to work with library
5. **Remove custom logic**: Clean up state management code
6. **Testing**: Verify maximize/restore works with single clicks
7. **Documentation**: Update code documentation

## Comparison: PyQt-Frameless-Window vs QT-PyQt-PySide-Custom-Widgets

| Feature | PyQt-Frameless-Window | QT-PyQt-PySide-Custom-Widgets |
|---------|----------------------|-------------------------------|
| **Customization** | High - Complete title bar replacement | Medium - JSON configuration |
| **Learning Curve** | Medium - Python code | Easy - JSON config |
| **Flexibility** | Very High - Any design possible | Limited - Predefined options |
| **Maintenance** | Active - Regular updates | Active - Regular updates |
| **Integration** | Replace base class | JSON + widget mapping |
| **Performance** | Excellent | Good |
| **Documentation** | Good with examples | Excellent with tutorials |

### Recommendation: PyQt-Frameless-Window

For Flatmate, we recommend **PyQt-Frameless-Window** because:
- We already have custom title bar components
- We need maximum flexibility for future enhancements
- It provides the most reliable window state management
- It allows us to preserve our existing design

## Resources

- [PyQt-Frameless-Window GitHub](https://github.com/zhiyiYo/PyQt-Frameless-Window/tree/PySide6)
- [QT-PyQt-PySide-Custom-Widgets Docs](https://khamisikibet.github.io/Docs-QT-PyQt-PySide-Custom-Widgets/)
- [Qt Forum Discussion](https://forum.qt.io/topic/151702/title-bar-customization)
- [Python GUIs Custom Title Bar Tutorial](https://www.pythonguis.com/tutorials/custom-title-bar-pyqt6/)
