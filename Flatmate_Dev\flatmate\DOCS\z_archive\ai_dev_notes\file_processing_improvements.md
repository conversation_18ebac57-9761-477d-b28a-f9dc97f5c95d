# File Processing System Improvements

## Overview

We improved the file processing system to ensure that only successfully processed files are backed up and that all files are properly managed after processing. The system now follows a complete workflow with proper tracking and cleanup.

## Initial Problems

1. **Backup Logic**: All files from the job sheet were being backed up, regardless of whether they were successfully processed
2. **Redundant Tracking**: Files were tracked in both the global `processed_files_tracker` and a local list
3. **File Management**: Processed files remained in their original location after processing
4. **Unrecognised Files**: Files that couldn't be processed needed proper handling

## Changes Implemented

### 1. Using the Processed Files Tracker as Source of Truth

We modified the system to use the `processed_files_tracker` as the single source of truth for processed files:

```python
# In dw_director.py
# Get the list of processed files from the tracker
processed_files_list = processed_files_tracker.get()

# Only back up files that were successfully processed
backup_status, backup_result = back_up_originals(processed_files_list, save_folder)
```

This ensures that only files that were actually processed are backed up.

### 2. Reducing Redundancy in File Tracking

We eliminated the redundant tracking of processed files:

```python
# Removed from process_files in dw_pipeline.py
# processed_files = []  # This line was removed

# Removed redundant append operation
if formatted_df is not None:
    formatted_dfs.append(formatted_df)
    # processed_files.append(df.filepath)  # This line was removed
    # Note: processed_files_tracker.add() is already called in process_with_handler

# Get processed files directly from the tracker
stats["processed_files"] = processed_files_tracker.get()
```

### 3. Enhanced Backup Function

We enhanced the `back_up_originals` function to track which files were backed up and which were skipped:

```python
# Track statistics and file lists
backed_up = 0
skipped = 0
backed_up_files = []
skipped_files = []

# When backing up a file
backed_up_files.append(filepath)

# When skipping a file (already exists)
skipped_files.append(filepath)

# Return structured stats including file lists
backup_stats = {
    "message": f"Backed up {backed_up} files to {backup_dir}, skipped {skipped} identical files",
    "backed_up_count": backed_up,
    "skipped_count": skipped,
    "backup_dir": backup_dir,
    "backed_up_files": backed_up_files,
    "skipped_files": skipped_files
}
```

### 4. Automatic Cleanup of Processed Files

We added logic to delete original files after they have been successfully backed up:

```python
# In dw_director.py
# Handle processed files after backup
if isinstance(backup_result, dict):
    # Get lists of backed up and skipped files
    backed_up_files = backup_stats.get("backed_up_files", [])
    skipped_files = backup_stats.get("skipped_files", [])
    
    # Delete original files that have been backed up or skipped
    deleted_count = 0
    for filepath in backed_up_files + skipped_files:
        try:
            os.remove(filepath)
            deleted_count += 1
        except Exception as e:
            log(f"Failed to delete original file {filepath}: {str(e)}", level="warning")
    
    # Add deletion count to stats
    backup_stats["deleted_count"] = deleted_count
    backup_msg += f", deleted {deleted_count} original files"
```

### 5. Handling of Unrecognised Files

We ensured that unrecognised files are properly tracked and moved to a designated folder:

```python
# In dw_director.py
# Handle unrecognized files
unrecognized_count = 0
for unrecognized in unrecognized_files:
    filepath = unrecognized["filepath"]
    reason = unrecognized["reason"]
    
    # Add to tracker for global accessibility
    unrecognised_files_tracker.add(filepath)
    
    # Move file to unrecognized folder
    if move_to_unrecognised(filepath):
        unrecognized_count += 1
```

## Benefits of the Changes

1. **Cleaner Workflow**: Files are properly sorted - processed files are backed up and removed, unrecognized files are moved to their own folder
2. **Reduced Redundancy**: The system uses a single source of truth for processed files
3. **Automatic Cleanup**: Original files are automatically cleaned up after processing
4. **Better Tracking**: The system keeps track of which files were backed up, skipped, or moved to unrecognized
5. **Improved Reliability**: Only files that were actually processed are backed up

## Current System Workflow

The file processing system now follows this workflow:

1. **Process Files**: Files are processed and tracked in the `processed_files_tracker`
2. **Back Up Processed Files**: Only successfully processed files are backed up
3. **Clean Up Originals**: Original files are deleted after successful backup
4. **Handle Unrecognised Files**: Files that couldn't be processed are moved to the unrecognised folder

## Next Steps

1. **Analysis of Unprocessed Files**: Examine what files are not being processed and why
2. **Architectural Review**: Further examine the redundancy between trackers and lists
3. **Error Handling**: Improve error handling and reporting for file processing failures
4. **User Interface**: Enhance the UI to provide better feedback on file processing status

## Conclusion

The improvements we've made create a more robust and efficient file processing system. Files are now properly tracked, backed up, and cleaned up, with a clear workflow from start to finish.
