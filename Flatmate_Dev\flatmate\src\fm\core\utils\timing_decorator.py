"""
Timing Decorator for Performance Analysis

Simple decorator to measure function execution time for debugging performance bottlenecks.
"""

import time
import functools
from fm.core.services.logger import log


def timing_decorator(func):
    """
    Decorator to measure and log function execution time.
    
    Usage:
        @timing_decorator
        def my_function():
            # ... function code
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        
        # Get function name and class if it's a method
        func_name = func.__name__
        if args and hasattr(args[0], '__class__'):
            class_name = args[0].__class__.__name__
            full_name = f"{class_name}.{func_name}"
        else:
            full_name = func_name
        
        log(f"⏱️  TIMING START: {full_name}", level="info")
        
        try:
            result = func(*args, **kwargs)
            elapsed = time.time() - start_time
            log(f"⏱️  TIMING END: {full_name} took {elapsed:.3f}s", level="info")
            return result
            
        except Exception as e:
            elapsed = time.time() - start_time
            log(f"⏱️  TIMING ERROR: {full_name} failed after {elapsed:.3f}s: {e}", level="error")
            raise
    
    return wrapper


def timing_context(name: str):
    """
    Context manager for timing code blocks.
    
    Usage:
        with timing_context("my_operation"):
            # ... code to time
    """
    class TimingContext:
        def __init__(self, operation_name):
            self.name = operation_name
            self.start_time = None
        
        def __enter__(self):
            self.start_time = time.time()
            log(f"⏱️  TIMING START: {self.name}", level="info")
            return self
        
        def __exit__(self, exc_type, exc_val, exc_tb):
            elapsed = time.time() - self.start_time
            if exc_type is None:
                log(f"⏱️  TIMING END: {self.name} took {elapsed:.3f}s", level="info")
            else:
                log(f"⏱️  TIMING ERROR: {self.name} failed after {elapsed:.3f}s", level="error")
    
    return TimingContext(name)
