"""Base class for bank statement type maps."""

import os.path
import re
from abc import ABC, abstractmethod
from contextlib import contextmanager
from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Optional, Tuple, Union, Generator, Any

import pandas as pd
from fm.core.data_services.standards.fm_standard_columns import StandardColumns
from fm.core.utils.date_utils import convert_df_dates, standardize_date
from fm.core.services.logger import Logger


class StatementError(Exception):
    """Base exception for statement handling errors.
    
    Args:
        message: Human-readable error message
        is_validation: If True, logs as warning; if False, logs as error
    """
    def __init__(self, message: str, *, is_validation: bool = False):
        self.is_validation = is_validation
        super().__init__(message)


class StatementHandler(ABC):
    """Base class for all statement handlers.
    
    Provides common functionality including error handling and logging.
    
    Error Handling:
        Use the handle_errors context manager for consistent error handling:
        
        with self.handle_errors("context message"):
            # Your code here
            if something_wrong:
                raise StatementError("Description of issue", is_validation=True)
    """
    
    def __init__(self):
        """Initialize the statement handler with a logger.
        
        Note: Subclasses should call super().__init__() to ensure proper logging setup.
        """
        self._logger = Logger()
        self._logger.debug(f"Initialized {self.__class__.__name__}")
    
    def log_debug(self, message: str, *args) -> None:
        """Log a debug message.
        
        Args:
            message: The message to log
            *args: Additional arguments to format into the message
        """
        formatted_message = f"[{self.__class__.__name__}] {message}" if not args else f"[{self.__class__.__name__}] {message}".format(*args)
        self._logger.debug(formatted_message)
    
    def log_info(self, message: str, *args) -> None:
        """Log an info message.
        
        Args:
            message: The message to log
            *args: Additional arguments to format into the message
        """
        formatted_message = f"[{self.__class__.__name__}] {message}" if not args else f"[{self.__class__.__name__}] {message}".format(*args)
        self._logger.info(formatted_message)
    
    def log_warning(self, message: str, *args) -> None:
        """Log a warning message.
        
        Args:
            message: The message to log
            *args: Additional arguments to format into the message
        """
        formatted_message = f"[{self.__class__.__name__}] {message}" if not args else f"[{self.__class__.__name__}] {message}".format(*args)
        self._logger.warning(formatted_message)
    
    def log_error(self, message: str, *args) -> None:
        """Log an error message.
        
        Args:
            message: The message to log
            *args: Additional arguments to format into the message
        """
        formatted_message = f"[{self.__class__.__name__}] {message}" if not args else f"[{self.__class__.__name__}] {message}".format(*args)
        self._logger.error(formatted_message)
    
    @contextmanager
    def handle_errors(self, context: str = "") -> Generator[None, None, None]:
        """Context manager for consistent error handling.
        
        Args:
            context: Additional context for error messages
            
        Example:
            with self.handle_errors("processing file"):
                # Code that might raise exceptions
                if invalid_condition:
                    raise StatementError("Invalid format", is_validation=True)
        """
        try:
            yield
        except StatementError as e:
            if e.is_validation:
                self.log_warning(f"{context}: {str(e)}")
            else:
                self.log_error(f"{context}: {str(e)}")
            raise
        except Exception as e:
            # Log the error with traceback
            self.log_error(f"Unexpected error {context}: {str(e)}")
            raise StatementError(f"Unexpected error: {str(e)}") from e

    @dataclass
    class StatementFormat:
        bank_name: str  # Bank name e.g., "Kiwibank"
        variant: str  # Format variant e.g., "basic"
        file_type: str  # File extension e.g., "csv"

    @dataclass
    class ColumnAttributes:
        """Attributes related to column mapping and formatting.

        Args:
            has_headers: Whether the file has a header row with column names
            has_col_names: Whether column names are present in the file at all
            n_source_cols: Number of source columns expected (must be > 0)
            col_names_in_data_row: Whether column names are in a data row
            has_account_column: Whether the file has a dedicated account column
            col_names_row: Row index (0-based) containing column names (must be >= 0)
            source_col_names: List of source column names
            target_col_names: List of target column names (StandardColumns enum values)
            date_format: Format string for parsing dates (e.g., '%d/%m/%Y')
        """
        # Required fields (no default)
        has_headers: bool
        has_col_names: bool
        n_source_cols: int  # Must be > 0
        date_format: str    # Must be non-empty
        
        # Optional fields with defaults
        col_names_in_data_row: bool = False
        has_account_column: bool = False     # !? shouldnt be optional ?
        col_names_row: int = 0
        source_col_names: list[str] = field(default_factory=list)
        target_col_names: list[StandardColumns] = field(default_factory=list)
        
        # Nested dataclasses
        @dataclass
        class ColumnHeaders:
            start_column: int = 0
            in_row: int = 0
            
        # Methods
        def __post_init__(self):
            # Validate required fields
            if not self.date_format:
                raise ValueError("date_format is required")
                
            if not isinstance(self.n_source_cols, int) or self.n_source_cols <= 0:
                raise ValueError("n_source_cols must be a positive integer")
                
            if self.col_names_row < 0:
                raise ValueError("col_names_row cannot be negative")

        @dataclass
        class ColumnHeaders:
            start_column: int = 0
            in_row: int = 0

    @dataclass
    class AccountNumberAttributes:
        pattern: str = ""

        in_data: bool = False  # Whether to check for account number in data
        location: Tuple[int, int] = field(default_factory=lambda: (0, 0))

        in_header: bool = False  # Whether to check the column name itself
        in_file_name: bool = False
        in_metadata: bool = False

    @dataclass
    class SourceMetadataAttributes:
        has_metadata_rows: bool = False
        metadata_start: Tuple[int, int] = field(default_factory=lambda: (0, 0))
        metadata_end: Tuple[int, int] = field(default_factory=lambda: (0, 0))

    @dataclass
    class FileInfo:
        """Information about a statement file"""

        bank_name: str
        account_number: str
        start_date: Optional[datetime]
        end_date: Optional[datetime]
        n_transactions: int
        source_filename: str

    statement_format: StatementFormat
    column_attrs: ColumnAttributes
    account_num_attrs: AccountNumberAttributes
    source_metadata_attrs: SourceMetadataAttributes

    """The header parameter in pd.read_csv() controls how column names are interpreted:
    header=None: No header row, uses default numeric column names
    header=0: First row is header (default)
    header=n: Use nth row as header"""

    def _check_account_number(self, df: pd.DataFrame, filepath: str) -> bool:
        """Check account number in order of reliability: metadata/data first, filename last"""
        # Import logging here to avoid circular imports
        from fm.core.services.logger import Logger

        logger = Logger()
        handler_name = self.__class__.__name__
        filename = os.path.basename(filepath)

        if not self.account_num_attrs.pattern:
            logger.debug(
                f"[{handler_name}] No account number pattern specified, skipping check"
            )
            return True

        logger.debug(
            f"[{handler_name}] Checking account number pattern '{self.account_num_attrs.pattern}' in file: {filename}"
        )

        # Check metadata first (most reliable)
        if self.account_num_attrs.in_metadata:
            row, col = self.account_num_attrs.location
            try:
                cell = df.iloc[row, col]
                logger.debug(
                    f"[{handler_name}] Checking metadata at [{row},{col}]: '{str(cell)}'"
                )
                if re.search(self.account_num_attrs.pattern, str(cell)):
                    logger.debug(f"[{handler_name}] Found account number in metadata")
                    return True
                logger.debug(f"[{handler_name}] Account number not found in metadata")
            except IndexError:
                logger.debug(
                    f"[{handler_name}] Metadata location [{row},{col}] out of bounds for DataFrame with shape {df.shape}"
                )

        # Then check data
        if self.account_num_attrs.in_data:
            row, col = self.account_num_attrs.location
            try:
                cell = df.iloc[row, col]
                logger.debug(
                    f"[{handler_name}] Checking data at [{row},{col}]: '{str(cell)}'"
                )
                if re.search(self.account_num_attrs.pattern, str(cell)):
                    logger.debug(f"[{handler_name}] Found account number in data")
                    return True
                logger.debug(f"[{handler_name}] Account number not found in data")
            except IndexError:
                logger.debug(
                    f"[{handler_name}] Data location [{row},{col}] out of bounds for DataFrame with shape {df.shape}"
                )

        # Then check header
        if self.account_num_attrs.in_header:
            logger.debug(
                f"[{handler_name}] Checking column headers: {[str(col) for col in df.columns]}"
            )
            if any(
                re.search(self.account_num_attrs.pattern, str(col))
                for col in df.columns
            ):
                logger.debug(f"[{handler_name}] Found account number in column headers")
                return True
            logger.debug(f"[{handler_name}] Account number not found in column headers")

        # Finally check filename (least reliable)
        if self.account_num_attrs.in_file_name:
            logger.debug(f"[{handler_name}] Checking filename: '{filename}'")
            # Use re.search instead of 'in' operator for regex patterns
            if re.search(self.account_num_attrs.pattern, filename):
                logger.debug(f"[{handler_name}] Found account number in filename")
                return True
            logger.debug(f"[{handler_name}] Account number not found in filename")

        # No valid account number found in any location
        logger.debug(f"[{handler_name}] No valid account number found in any location")
        return False

    @classmethod
    def can_handle_file(
        cls, 
        filepath: str, 
        *,
        require_filename_match: bool = False,
        require_columns: bool = True,
        require_account_number: bool = True,
    ) -> bool:
        """
        Lightweight, configuration-driven check to see if this handler can process the file.

        This method reads a small preview of the file and checks it against the
        handler's configuration to determine if it's a match.
        """
        import pandas as pd
        import re
        import os

        try:
            # Instantiate the handler to access its configuration
            handler_instance = cls()
            handler_name = handler_instance.__class__.__name__
            
            # Get handler attributes
            col_attrs = handler_instance.column_attrs
            acc_attrs = handler_instance.account_num_attrs
            
            # Skip column count check for Co-op handler (handles it in its own can_handle_file)
            if handler_name == 'CoopStandardCSVHandler':
                require_columns = False
            
            # Use the instance's logger
            log = handler_instance._logger.debug
            
            # Color codes for different log levels
            COLORS = {
                'DEBUG': '\033[36m',    # Cyan
                'INFO': '\033[32m',     # Green
                'WARNING': '\033[33m',  # Yellow
                'ERROR': '\033[31m',    # Red
                'RESET': '\033[0m',     # Reset to default
                'HANDLER': '\033[1;35m' # Bright Magenta
            }
            
            def colorize(level, message):
                return f"{COLORS.get(level, '')}[{level}] {message}{COLORS['RESET']}"
            
            def log_debug(message):
                log(colorize('DEBUG', f"{COLORS['HANDLER']}[{handler_name}]{COLORS['RESET']} {message}"))
            
            def log_info(message):
                handler_instance._logger.info(colorize('INFO', f"{COLORS['HANDLER']}[{handler_name}]{COLORS['RESET']} {message}"))
            
            def log_warning(message):
                handler_instance._logger.warning(colorize('WARNING', f"{COLORS['HANDLER']}[{handler_name}]{COLORS['RESET']} {message}"))
            
            def log_error(message):
                handler_instance._logger.error(colorize('ERROR', f"{COLORS['HANDLER']}[{handler_name}]{COLORS['RESET']} {message}"))
            
            # --- Perform checks based on configuration ---
            
            # # 1. Filename check (cheap and fast)
            filename_matches = True
            if acc_attrs.in_file_name:
                filename_matches = bool(re.search(acc_attrs.pattern, os.path.basename(filepath)))
                if filename_matches:
                    log_debug(f"✅ Filename pattern check passed")
                elif require_filename_match:
                    log_warning(f"❌ Filename pattern check failed and is required")
                    return False
                else:
                    log_debug(f"⚠️  Filename pattern check failed, but continuing with other validations")

            # For content checks, read a preview of the file.
            # Start with a reasonable default of 20 rows for most cases
            rows_to_read = 20
            
            # If file has column names, ensure we read enough rows to include:
            # - The row containing column names (col_names_row)
            # - Plus at least one row of data (hence +2: +1 for 0-indexing, +1 for data)
            if col_attrs.has_col_names:
                rows_to_read = max(rows_to_read, col_attrs.col_names_row + 2)
            
            # If there's metadata in the file, ensure we read enough rows to include:
            # - The row containing the metadata (location[0] is the row index)
            # - Plus at least one row of data
            if acc_attrs.in_metadata:
                rows_to_read = max(rows_to_read, acc_attrs.location[0] + 2)
            
            # # Read CSV with or without headers based on handler configuration
            df_preview = pd.read_csv(
                filepath,
                header=col_attrs.col_names_row if col_attrs.has_headers else None,
                nrows=rows_to_read,
                on_bad_lines='skip',
                engine='python',
                delimiter=','
            )



            # # 3. Header content check
            if col_attrs.has_col_names and col_attrs.source_col_names:
                try:
                    # If has_headers is True, the headers are already in df_preview.columns
                    if col_attrs.has_headers:
                        actual_headers = [str(h).strip() for h in df_preview.columns]
                    else:
                        actual_headers = [str(h).strip() for h in df_preview.iloc[col_attrs.col_names_row]]
                    
                    expected_headers = [str(h).strip() for h in col_attrs.source_col_names if h is not None]
                    
                    if actual_headers != expected_headers:
                        log_warning(f"❌ Header check failed. Expected: {expected_headers}, Actual: {actual_headers}")
                        return False
                    log_debug(f"✅ Header check passed. Found expected headers")
                except Exception as e:
                    log_warning(f"❌ Header check failed with error: {str(e)}")
                    return False

            # 4. Account number validation if required
            if require_account_number and acc_attrs.pattern and (acc_attrs.in_header or acc_attrs.in_metadata or acc_attrs.in_data):
                found = False
                # `in_header` means check the designated header row
                if acc_attrs.in_header:
                    header_row_index = col_attrs.col_names_row if col_attrs.has_col_names else 0
                    try:
                        if re.search(acc_attrs.pattern, df_preview.iloc[header_row_index].to_string()):
                             found = True
                             log_debug(f"✅ Account number found in header row {header_row_index}")
                    except IndexError:
                        pass
                
                # `in_metadata` or `in_data` means check a specific cell
                if not found and (acc_attrs.in_metadata or acc_attrs.in_data):
                    row, col = acc_attrs.location
                    try:
                        cell_content = str(df_preview.iloc[row, col])
                        if re.search(acc_attrs.pattern, cell_content):
                            found = True
                            log_debug(f"✅ Account number found at location ({row},{col})")
                    except IndexError:
                        pass

                if not found:
                    log_warning(f"❌ Account number pattern check failed for content")
                    return False

            log_info(f"✅ All configured checks passed. Match found")
            return True

        except (FileNotFoundError, pd.errors.EmptyDataError):
            log_warning(f"❌ File is empty or not found: {filepath}")
            return False
        except Exception as e:
            log_error(f"❌ An unexpected error occurred during check for {filepath}: {e}")
            return False

    def matches_statement_type(self, df: pd.DataFrame) -> bool:
        handler_name = self.__class__.__name__
        filename = df.filename

        # Import logging here to avoid circular imports
        from fm.core.services.logger import Logger

        logger = Logger()

        logger.debug(f"[{handler_name}] Checking if file matches: {filename}")
        logger.debug(
            f"[{handler_name}] Expected columns: {self.column_attrs.n_source_cols}, Actual: {len(df.columns)}"
        )

        # Check number of columns
        if len(df.columns) != self.column_attrs.n_source_cols:
            logger.debug(
                f"[{handler_name}] Column count mismatch: expected {self.column_attrs.n_source_cols}, got {len(df.columns)}"
            )
            return False

        # Check column names if specified and file should have column names
        if self.column_attrs.has_col_names:
            if self.column_attrs.col_names_in_header:
                # Names should be in column headers in order
                expected = [str(name) for name in self.column_attrs.source_col_names]
                actual = [str(col) for col in df.columns]
                logger.debug(
                    f"[{handler_name}] Checking column headers - Expected: {expected}"
                )
                logger.debug(
                    f"[{handler_name}] Checking column headers - Actual: {actual}"
                )
                if expected != actual:
                    logger.debug(f"[{handler_name}] Column headers don't match")
                    return False

            elif self.column_attrs.col_names_in_data_row:
                # Names should be in specified row in order
                row = df.iloc[self.column_attrs.col_names_row]
                expected = [str(name) for name in self.column_attrs.source_col_names]
                actual = [str(cell) for cell in row]
                logger.debug(
                    f"[{handler_name}] Checking data row {self.column_attrs.col_names_row} - Expected: {expected}"
                )
                logger.debug(
                    f"[{handler_name}] Checking data row {self.column_attrs.col_names_row} - Actual: {actual}"
                )
                if expected != actual:
                    logger.debug(f"[{handler_name}] Data row column names don't match")
                    return False

        # Check account number in any valid location
        logger.debug(
            f"[{handler_name}] Checking account number pattern: {self.account_num_attrs.pattern}"
        )
        if not self._check_account_number(df, df.filepath):
            logger.debug(f"[{handler_name}] Account number check failed")
            return False

        logger.debug(f"[{handler_name}] File matches handler criteria: {filename}")
        return True

    def get_file_info(self, df: pd.DataFrame) -> FileInfo:
        """Extract key information about the statement file"""
        # Find which source column maps to DATE
        date_index = None
        for i, target_col in enumerate(self.column_attrs.target_col_names):
            if target_col == StandardColumns.DATE:
                date_index = i
                break

        # Get date range from raw data
        if date_index is not None:
            # Skip metadata if present
            start_row = (
                self.source_metadata_attrs.metadata_end[0] + 1
                if self.source_metadata_attrs.has_metadata_rows
                else 0
            )
            # Skip header row if column names are in data
            if self.column_attrs.col_names_in_data_row:
                start_row = max(start_row, self.column_attrs.col_names_row + 1)

            dates = pd.to_datetime(df.iloc[start_row:, date_index])
            start_date = dates.min() if not dates.empty else None
            end_date = dates.max() if not dates.empty else None
        else:
            start_date = None
            end_date = None

        # Extract account number from the DataFrame
        account_number = self._extract_account_number(df)

        return self.FileInfo(
            bank_name=self.statement_format.bank_name,
            account_number=account_number,
            start_date=start_date,
            end_date=end_date,
            n_transactions=(len(df) - start_row if date_index is not None else 0),
            source_filename=df.filename,
        )

    def _reorder_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Reorder columns based on StandardColumns enum order, if they exist"""
        # Import logging here to avoid circular imports
        from fm.core.services.logger import Logger

        logger = Logger()
        handler_name = self.__class__.__name__

        # Get the standard order from enum
        standard_order = [fmt.value for fmt in StandardColumns]

        logger.debug(f"[{handler_name}] Standard column order: {standard_order}")
        logger.debug(f"[{handler_name}] Current columns: {list(df.columns)}")

        # Filter to only columns that exist in df
        ordered_cols = [col for col in standard_order if col in df.columns]

        # Add any remaining columns that aren't in the standard order
        remaining_cols = [col for col in df.columns if col not in ordered_cols]
        ordered_cols.extend(remaining_cols)

        logger.debug(f"[{handler_name}] Reordered columns: {ordered_cols}")

        # Use reindex to ensure we return a DataFrame
        return df.reindex(columns=ordered_cols)

    def _create_details_from_columns(self, df: pd.DataFrame, columns: list[str]) -> None:
        """Create a details column by concatenating specified columns.
        
        Args:
            df: Input DataFrame (modified in place)
            columns: List of column names to concatenate (in order)
        """
        existing_cols = [col for col in columns if col in df.columns]
        if not existing_cols:
            self.log_warning("No valid columns provided for details creation")
            return
            
        # Filter out empty strings and join with spaces
        df[StandardColumns.DETAILS] = df[existing_cols].apply(
            lambda x: ' '.join(str(val) for val in x if pd.notna(val) and str(val).strip()),
            axis=1
        )
        self.log_info(f"Created details column from: {', '.join(existing_cols)}")

    def _custom_format(self, df: pd.DataFrame) -> pd.DataFrame:
        """Hook for handlers to perform bank-specific formatting.
        
        Override this in bank handlers that need custom formatting.
        No need to call super() - just implement your custom logic here.
        
        Note: Date standardization is handled automatically before this is called.
        """
        return df

    def _standardize_dates(self, df: pd.DataFrame) -> None:
        """Standardize dates in the DataFrame to ISO format.

        Uses the date_format specified in column_attrs to parse dates
        and convert them to ISO 8601 format (YYYY-MM-DD).
        
        Note: column_attrs.date_format is required to be set.

        Args:
            df: DataFrame with dates to standardize
            
        Raises:
            ValueError: If date_format is not set in column_attrs
        """
        # Import logging here to avoid circular imports
        from fm.core.services.logger import Logger

        logger = Logger()
        handler_name = self.__class__.__name__
        date_col = StandardColumns.DATE.value
        date_format = self.column_attrs.date_format
        
        if not date_format:
            raise ValueError(f"{handler_name} must specify date_format in ColumnAttributes")

        if date_col not in df.columns:
            logger.debug(f"[{handler_name}] No date column found for standardization")
            return

        # Get sample of dates before standardization
        sample_dates = df[date_col].head(3).tolist()
        logger.debug(
            f"[{handler_name}] Sample dates before standardization: {sample_dates}"
        )

        # Track original null count
        null_before = df[date_col].isna().sum()

        # Apply standardization to each date in the column
        if self.column_attrs.date_format:
            logger.debug(
                f"[{handler_name}] Standardizing dates using format: {self.column_attrs.date_format}"
            )
            # Use the date_utils function with the handler's date format
            # First try with the handler's specific format
            original_df = df.copy()

            # Create a custom DATE_FORMATS list with the handler's format first
            from fm.core.utils.date_utils import DATE_FORMATS

            custom_formats = [self.column_attrs.date_format] + [
                fmt for fmt in DATE_FORMATS if fmt != self.column_attrs.date_format
            ]

            # Try each date in the column with the handler's format first
            for idx, date_val in enumerate(df[date_col]):
                if pd.isna(date_val):
                    continue

                # Try the handler's format first
                try:
                    date_obj = datetime.strptime(
                        str(date_val).strip(), self.column_attrs.date_format
                    )
                    df.at[idx, date_col] = date_obj.strftime("%Y-%m-%d")  # ISO format
                except ValueError:
                    # If that fails, let the standard function try all formats
                    df.at[idx, date_col] = standardize_date(date_val)

            # Log success rate
            success_count = (df[date_col].notna() & original_df[date_col].notna()).sum()
            total_dates = original_df[date_col].notna().sum()
            if total_dates > 0:
                success_rate = (success_count / total_dates) * 100
                logger.debug(
                    f"[{handler_name}] Date conversion success rate: {success_rate:.1f}% ({success_count}/{total_dates})"
                )

            # Check if any dates couldn't be converted (became null)
            null_after = df[date_col].isna().sum()
            if null_after > null_before:
                logger.debug(
                    f"[{handler_name}] WARNING: {null_after - null_before} dates could not be converted to ISO format"
                )

    def format_df(self, df: pd.DataFrame, filepath: str) -> pd.DataFrame:
        """Format the DataFrame to standard format"""
        # Import logging here to avoid circular imports
        from fm.core.services.logger import Logger

        logger = Logger()
        handler_name = self.__class__.__name__
        logger.debug(f"[{handler_name}] Starting format_df")
        self._current_filepath = filepath  # Store for potential use in _extract_account_number
        account_number = None
        # skip_first_row = False #todo purpose of this line?
        if not self.column_attrs.has_account_column:
            # Extract account number and check if we found it in the first row
            account_number = self._extract_account_number(df)
            logger.debug(f"[{handler_name}] Extracted account number: {account_number}")

        # 2. Strip metadata if present
        if self.source_metadata_attrs.has_metadata_rows:
            start = self.source_metadata_attrs.metadata_end[0] + 1
            logger.debug(f"[{handler_name}] Stripping metadata rows 0-{start-1}")
            df = df.iloc[start:]
        """# Skip first row if we found account number there and it's not already handled as metadata
        elif skip_first_row:
            logger.debug(
                f"[{handler_name}] Skipping first row with account number"
            )
            df = df.iloc[1:]"""

        # 3. Handle column names in data
        if self.column_attrs.col_names_in_data_row:
            df.columns = df.iloc[self.column_attrs.col_names_row]
            df = df.iloc[self.column_attrs.col_names_row + 1 :]

        # 4. Map source columns to target format
        target_col_values = [
            name.value
            for name in self.column_attrs.target_col_names
            if name is not None
        ]

        # Log column mapping for debugging
        logger.debug(
            f"[{handler_name}] Mapping columns: {list(df.columns)} -> {target_col_values}"
        )
        logger.debug(f"[{handler_name}] DataFrame shape: {df.shape}")

        # 5. Assign column names - no need for column count checks
        df.columns = target_col_values

        # 6. Add source filename
        df[StandardColumns.SOURCE_FILENAME.value] = os.path.basename(filepath)

        # 7. Add account number if it was extracted earlier
        if not self.column_attrs.has_account_column and account_number is not None:
            df[StandardColumns.ACCOUNT.value] = account_number

        # 8. Standardize dates (date_format is required in ColumnAttributes)
        self._standardize_dates(df)

        # 9. Allow handler to perform custom formatting
        df = self._custom_format(df)

        # 10. Automatically remove empty columns
        if StandardColumns.EMPTY_COLUMN.value in df.columns:
            df = df.drop(columns=[StandardColumns.EMPTY_COLUMN.value])
            logger.debug(f"[{handler_name}] Removed empty column")

        # 10. Auto-create details column if missing and we have potential source columns
        if StandardColumns.DETAILS not in df.columns:
            common_detail_cols = ['Description', 'Narrative', 'Particulars', 'Payee', 'Memo']
            self._create_details_from_columns(df, common_detail_cols)

        # 11. Reorder columns to standard format
        df = self._reorder_columns(df)

        return df

    def _extract_account_number(self, df: pd.DataFrame) -> str:
        """Extract account number using handler configuration.
        
        Tries to extract account number in this order:
        1. From metadata location if configured
        2. From filename if configured
        3. Returns empty string if not found
        """
        # Try to extract from metadata if configured
        if hasattr(self, 'account_num_attrs'):
            attrs = self.account_num_attrs
            
            # Check metadata location
            if hasattr(attrs, 'in_metadata') and attrs.in_metadata:
                try:
                    row, col = attrs.location
                    account_line = str(df.iloc[row, col])
                    if hasattr(attrs, 'pattern'):
                        import re
                        match = re.search(attrs.pattern, account_line)
                        if match:
                            return match.group(1) if match.groups() else match.group(0)
                    return account_line.strip()
                except (IndexError, AttributeError, KeyError) as e:
                    self.log_warning(f"Failed to extract account number from metadata: {e}")
            
            # Check filename if configured
            if hasattr(attrs, 'in_file_name') and attrs.in_file_name:
                try:
                    import re
                    from pathlib import Path
                    if hasattr(self, '_current_filepath'):
                        filename = Path(self._current_filepath).stem
                        if hasattr(attrs, 'pattern'):
                            match = re.search(attrs.pattern, filename)
                            if match:
                                return match.group(1) if match.groups() else match.group(0)
                        return filename
                except Exception as e:
                    self.log_warning(f"Failed to extract account number from filename: {e}")
        
        return ""  # Return empty string if no account number found
