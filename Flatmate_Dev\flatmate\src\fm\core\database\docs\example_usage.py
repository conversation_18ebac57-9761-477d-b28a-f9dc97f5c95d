"""
Example usage of the data module.
This script demonstrates how to use the database system with clean architectural boundaries.
"""
import os
from datetime import datetime, timed<PERSON><PERSON>
from typing import List

from fm.database_service.repository.transaction_repository import Transaction
from fm.database_service.service import DataService
from fm.database_service.events import event_bus, DataEvents
from fm.database_service.converters import CSVToTransactionConverter

# Initialize data service
data_service = DataService()


def on_transactions_imported(event):
    """Example subscriber function for transaction import events."""
    print(f"Event received: {event.event_type}")
    print(f"Imported {event.data['count']} transactions")
    print(f"Found {event.data['duplicate_count']} duplicates")
    if event.data['error_count'] > 0:
        print(f"Encountered {event.data['error_count']} errors")


def example_import_from_csv():
    """Example of importing transactions from a CSV file."""
    # Subscribe to import events
    event_bus.subscribe(DataEvents.TRANSACTIONS_IMPORTED, on_transactions_imported)
    
    # Create a converter
    converter = CSVToTransactionConverter()
    
    # Path to your CSV file
    csv_path = input("Enter path to CSV file: ")
    
    if not os.path.exists(csv_path):
        print(f"File not found: {csv_path}")
        return
    
    # Convert CSV to transactions
    print("Converting CSV to transactions...")
    source_bank = input("Enter source bank name: ")
    account_number = input("Enter account number: ")
    
    transactions = converter.convert_csv_file(
        csv_path, 
        source_bank=source_bank,
        account_number=account_number
    )
    
    print(f"Converted {len(transactions)} transactions")
    
    # Import transactions
    print("Importing transactions...")
    result = data_service.import_transactions(transactions)
    
    print(f"Import complete. Added: {result.added_count}, "
          f"Duplicates: {result.duplicate_count}, Errors: {result.error_count}")
    
    # Get statistics
    stats = data_service.get_statistics()
    print("\nDatabase Statistics:")
    print(f"Total transactions: {stats['total_count']}")
    print(f"Date range: {stats['earliest_date']} to {stats['latest_date']}")
    print(f"Total amount: {stats['total_amount']}")


def example_create_manual_transactions():
    """Example of creating and importing transactions manually."""
    # Create some sample transactions
    transactions = [
        Transaction(
            date=datetime.now() - timedelta(days=5),
            description="Grocery shopping",
            amount=-85.75,
            account_number="12-3456-7890123-00",
            transaction_type="EFTPOS",
            category="Groceries"
        ),
        Transaction(
            date=datetime.now() - timedelta(days=3),
            description="Salary payment",
            amount=2000.00,
            account_number="12-3456-7890123-00",
            transaction_type="Direct Credit",
            category="Income"
        ),
        Transaction(
            date=datetime.now() - timedelta(days=1),
            description="Internet bill",
            amount=-89.99,
            account_number="12-3456-7890123-00",
            transaction_type="Automatic Payment",
            category="Utilities"
        )
    ]
    
    # Import the transactions
    result = data_service.import_transactions(transactions)
    print(f"Added {result.added_count} transactions")


def example_query_transactions():
    """Example of querying transactions."""
    # Get all transactions
    all_transactions = data_service.get_transactions()
    print(f"Total transactions: {len(all_transactions)}")
    
    # Get transactions by date range
    start_date = datetime.now() - timedelta(days=7)
    end_date = datetime.now()
    date_transactions = data_service.get_transactions_by_date_range(start_date, end_date)
    print(f"Transactions in the last 7 days: {len(date_transactions)}")
    
    # Get transactions by category
    category = "Groceries"
    category_transactions = data_service.get_transactions_by_category(category)
    print(f"Transactions in category '{category}': {len(category_transactions)}")
    
    # Search transactions
    search_text = "bill"
    search_transactions = data_service.search_transactions(search_text)
    print(f"Transactions matching '{search_text}': {len(search_transactions)}")
    
    # Print some details
    if search_transactions:
        print("\nSearch results:")
        for t in search_transactions:
            print(f"{t.date.strftime('%Y-%m-%d')} - {t.description}: ${t.amount:.2f}")


def example_update_transaction():
    """Example of updating a transaction."""
    # Get a transaction to update
    transactions = data_service.get_transactions({"limit": 1})
    if not transactions:
        print("No transactions to update")
        return
    
    transaction = transactions[0]
    print(f"Updating transaction: {transaction.description}")
    
    # Update the transaction
    data_service.update_transaction(
        transaction.transaction_id,
        {
            "category": "Updated Category",
            "notes": "This transaction was updated by the example script"
        }
    )
    
    # Verify the update
    updated = data_service.get_transactions({"transaction_id": transaction.transaction_id})[0]
    print(f"Updated category: {updated.category}")
    print(f"Updated notes: {updated.notes}")


def main():
    """Main example function."""
    print("Data Module Example Usage")
    print("-----------------------")
    
    while True:
        print("\nChoose an example:")
        print("1. Import from CSV")
        print("2. Create manual transactions")
        print("3. Query transactions")
        print("4. Update a transaction")
        print("5. Exit")
        
        choice = input("\nEnter your choice (1-5): ")
        
        if choice == "1":
            example_import_from_csv()
        elif choice == "2":
            example_create_manual_transactions()
        elif choice == "3":
            example_query_transactions()
        elif choice == "4":
            example_update_transaction()
        elif choice == "5":
            break
        else:
            print("Invalid choice. Please try again.")


if __name__ == "__main__":
    main()
