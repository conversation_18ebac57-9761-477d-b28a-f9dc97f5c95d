# Categorise Transactions Module – Architecture & GUI Layout

_Last updated: 2025-06-15_

---
## 1  Purpose
Provide users with a quick way to categorise imported bank-statement transactions, review/edit categories, and persist results to the Flatmate database.

---
## 2  High-Level Component Diagram
```
MainWindow (vessel)
   └── CategorizePresenter
        ├── CatView (BaseModuleView)
        │     ├── LeftPanelButtons    # Select files / Back
        │     └── CenterPanelTable    # QTableView → Transaction grid
        └── core.processor
              ├── fm.statement_processing.process_files  # existing pipeline
              └── core.categorizer.TransactionCategorizer
```

---
## 3  Package / File Layout
```
fm/modules/categorize/
├── cat_presenter.py            # Presenter (handles UI ↔ core)
├── _view/
│   ├── cat_view.py             # Main Qt view
│   └── center_panel/
│       └── table_widget.py     # Thin wrapper around QTableView (optional)
├── core/
│   ├── processor.py            # Orchestrates parse + categorise
│   ├── categorizer.py          # Pattern matcher (regex/ML stub)
│   └── models.py               # Optional dataclasses/helpers
└── resources/
    └── patterns.json           # Category patterns registry
```

---
## 4  Presenter Responsibilities
1. **initialize()** – embed `CatView` into `MainWindow`, ensure left-panel visible.  
2. **_handle_files_selected()** – receive file list from view, call `categorize_files()` (core.processor), then pass DataFrame to view.  
3. **request_transition()** – used by `ModuleCoordinator` to handle navigation.  
4. **cleanup()** – disconnect signals, hide view.

---
## 5  View Layout & Signals
### 5.1  Left Panel Buttons
| Button | Signal | Purpose |
|--------|--------|---------|
| Select Files | `files_select_requested` | Opens multi-file dialog (CSV/XLSX). |
| Back | `cancel_clicked` | Emits transition request to `home`. |

### 5.2  Center Panel Table
`QTableView` bound to a `PandasModel` or `QStandardItemModel` with columns:
1. `date` (read-only)  
2. `amount` (read-only, right-aligned)  
3. `description` (read-only, stretch)  
4. `category` (editable combo-box delegate listing known categories + free-text)

Additional toolbar actions (future):
• Quick-filter by text / uncategorised  
• Save to DB  
• Export CSV

Signals emitted:
* `files_selected(List[str])` – after successful file dialog.  
* `data_saved()` – after DB commit.

---
## 6  Data Flow (MVP)
1. User clicks **Select Files** → file dialog returns list.  
2. Presenter calls `categorize_files(file_paths)`  
   • `process_files` parses & merges ↠ DataFrame.  
   • `TransactionCategorizer` adds `category` column.  
3. DataFrame handed to view → displayed in table.  
4. User edits categories inline.  
5. User presses **Save** (future) → presenter maps edited rows to DB update via `DataService`.

---
## 7  Extensibility Notes
* **Pattern editing UI** – load JSON → editable table → save.  
* **ML Model** – slot into `TransactionCategorizer` as tie-breaker.  
* **Undo/Redo** – track original vs edited category per row.

---
## 8  Open Questions
1. Where to store user-defined pattern JSON? (AppData vs repo)  
2. Which DB schema/table stores the `category` field?  
3. Do we need multi-file batch editing across different accounts?  
4. Required nav-pane button/icon design.

---
_End of document_
