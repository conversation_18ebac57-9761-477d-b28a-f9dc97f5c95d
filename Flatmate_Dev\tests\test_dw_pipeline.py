#!/usr/bin/env python3
"""
Test the end-to-end data processing pipeline via dw_director.
"""

import os
import sys
import shutil

import pandas as pd
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from fm.modules.update_data.utils.dw_director import dw_director


class PipelineTest: 
    def __init__(self):
        self.test_root = Path(__file__).parent
        self.base_test_data_dir = self.test_root / "test_CSVs" / "test_csvs_ALL_TYPES_~50rows_data"
        self.input_dir = self.test_root / "temp_test_input" # Temporary folder for test run
        self.output_dir = self.test_root / "output" / "pipeline_test_output"
        self.source_data_dir = None # To be determined in setup

    def setup(self):
        """Create clean input/output directories and copy test files for the run."""
        # 1. Determine the source data directory
        # Check root directory first, then 'originals' subdirectory
        root_csvs = list(self.base_test_data_dir.glob('*.csv')) + list(self.base_test_data_dir.glob('*.CSV'))
        originals_dir = self.base_test_data_dir / "originals"

        if root_csvs:
            self.source_data_dir = self.base_test_data_dir
            print(f"Found test files in root directory: {self.source_data_dir}")
        elif originals_dir.is_dir():
            self.source_data_dir = originals_dir
            print(f"Found test files in 'originals' subdirectory: {self.source_data_dir}")
        else:
            raise FileNotFoundError(
                f"Could not find test CSV files in {self.base_test_data_dir} or its 'originals' subdirectory."
            )

        # 2. Clean and create temporary input and output directories
        for dir_path in [self.output_dir, self.input_dir]:
            if dir_path.exists():
                shutil.rmtree(dir_path)
            dir_path.mkdir(parents=True, exist_ok=True)
        
        (self.output_dir / "backup").mkdir()
        print(f"Created clean test output directory: {self.output_dir}")
        print(f"Created clean temporary input directory: {self.input_dir}")

        # 4. Clean up old database file to ensure a fresh schema
        db_path = Path.home() / ".flatmate" / "data" / "transactions.db"
        if db_path.exists():
            db_path.unlink()
            print(f"Removed existing database file: {db_path}")

        # 3. Copy source files to the temporary input directory
        for src_file in self.source_data_dir.glob("*"):
            if src_file.suffix.lower() == '.csv':
                shutil.copy(src_file, self.input_dir)
        print(f"Copied test files to temporary input directory.")

    def _create_test_file(self, filename, content):
        path = os.path.join(self.input_dir, filename)
        with open(path, 'w') as f:
            f.write(content)
        return path

    def run_tests(self):
        self.setup()
        import atexit
        atexit.register(self.cleanup)
        print("\n--- Running Pipeline Tests ---")
        self.test_successful_run()
        # self.test_validation_failure() # Disabled until a real failing file is created
        print("\n--- Tests Finished ---")

    def cleanup(self):
        """Remove the temporary input directory after the test run."""
        if self.input_dir.exists():
            shutil.rmtree(self.input_dir)
            print(f"\nCleaned up temporary input directory: {self.input_dir}")

    def test_successful_run(self):
        print("\nScenario 1: Successful end-to-end run with real files")
        
        # Get all CSV files from the directory, excluding previous master files
        filepaths = [
            str(p) for p in self.input_dir.glob("*.csv") 
            if not p.name.startswith('fmMaster_')
        ]
        print(f"Found {len(filepaths)} CSV files to test.")

        job_sheet = {
            "filepaths": filepaths,
            "save_folder": str(self.output_dir),
            "update_database": True,  # Enable database update for this test
            "cleanup_source_folder": False # IMPORTANT: Do not modify source test files
        }

        result = dw_director(job_sheet)

        # Assertions
        if result['status'] != 'success':
            import json
            print(f"Test failed. Director response:\n{json.dumps(result, indent=2)}")
        # Print the director's response for debugging
        import json
        print(json.dumps(result, indent=2))

        assert result['status'] == 'success', f"Expected status 'success', got '{result['status']}'"
        
        # Get the number of input files that should be processed (excluding any master files)
        expected_file_count = len(filepaths)
        assert result['details']['processed_files'] == expected_file_count, \
            f"Expected {expected_file_count} processed files, got {result['details']['processed_files']}"
        assert result['details']['unrecognized_files'] == 0, \
            f"Expected 0 unrecognized files, got {result['details']['unrecognized_files']}"

        # Check for master file
        output_files = os.listdir(self.output_dir)
        master_files = [f for f in output_files if f.startswith('fmMaster_') and f.endswith('.csv')]
        assert len(master_files) == 1, f"Expected 1 master file, found {len(master_files)}"

        # Check database update results
        print(f"--- Database Update Summary ---")
        print(f"Added: {result['details']['added_count']}, Duplicates: {result['details']['duplicate_count']}, Errors: {result['details']['error_count']}")
        assert result['details']['database_updated'] is True, "Expected database_updated flag to be true"
        assert result['details']['added_count'] >= 0, "Added count should be 0 or more"
        assert result['details']['duplicate_count'] >= 0, "Duplicates count should be 0 or more"
        assert result['details']['error_count'] == 0, "Expected no database import errors"

        # Check that original files were NOT deleted
        assert os.path.exists(filepaths[0]), "Original test file was incorrectly deleted"

        print("  -> PASS")



if __name__ == "__main__":
    test_runner = PipelineTest()
    test_runner.run_tests()
