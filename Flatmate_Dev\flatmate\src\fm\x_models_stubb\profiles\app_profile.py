"""
Application profile implementation for the FlatMate application.
Handles application-wide settings and preferences.
"""

from typing import Dict, <PERSON>, <PERSON><PERSON>, Optional
from dataclasses import dataclass
from .base_profile import BaseProfile


@dataclass
class GUIPreferences:
    """GUI-specific preferences."""
    theme: str = "dark_teal"
    default_module: str = "home"
    window_size: Tuple[int, int] = (1200, 800)
    show_splash: bool = True
    column_preferences: Dict[str, bool] = None
    
    def __post_init__(self):
        if self.column_preferences is None:
            self.column_preferences = {}


class AppProfile(BaseProfile):
    """Application profile for storing app-wide settings."""
    
    def __init__(self):
        super().__init__()
        self.gui_preferences = GUIPreferences()
        self.default_property_id: Optional[str] = None
        self.last_used_module: str = "home"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert profile to dictionary format."""
        data = super().to_dict()
        data.update({
            "gui_preferences": {
                "theme": self.gui_preferences.theme,
                "default_module": self.gui_preferences.default_module,
                "window_size": self.gui_preferences.window_size,
                "show_splash": self.gui_preferences.show_splash,
                "column_preferences": self.gui_preferences.column_preferences
            },
            "default_property_id": self.default_property_id,
            "last_used_module": self.last_used_module
        })
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AppProfile':
        """Create profile instance from dictionary data."""
        profile = cls()
        gui_prefs = data.get("gui_preferences", {})
        
        profile.gui_preferences = GUIPreferences(
            theme=gui_prefs.get("theme", "dark_teal"),
            default_module=gui_prefs.get("default_module", "home"),
            window_size=tuple(gui_prefs.get("window_size", (1200, 800))),
            show_splash=gui_prefs.get("show_splash", True),
            column_preferences=gui_prefs.get("column_preferences", {})
        )
        
        profile.default_property_id = data.get("default_property_id")
        profile.last_used_module = data.get("last_used_module", "home")
        
        return profile
