"""Local configuration manager for Home module."""

from typing import Any
from pathlib import Path

from ....core.config.base_local_config import BaseLocalConfig
from ....core.services.event_bus import global_event_bus
from .home_keys import HomeKeys


class HomeConfig(BaseLocalConfig[HomeKeys]):
    """Configuration manager for home module.

    Provides:
    1. Component-specific config access through BaseLocalConfig
    2. Type-safe key management through HomeKeys
    3. Default value initialization from home_defaults.yaml
    4. Access to global event bus
    """

    def __init__(self):
        super().__init__()
        self.events = global_event_bus

        # Log initialization
        self.events.publish(
            Events.LOG_EVENT,
            {
                "level": "DEBUG",
                "module": "home.config",
                "message": "Home Configuration initialized with default settings",
            },
        )

    def get_defaults(self) -> dict:
        """Get default values for Home module."""
        return HomeKeys.get_defaults()

    def get_defaults_file_path(self) -> Path:
        """Get the path to the Home defaults.yaml file."""
        return Path(__file__).parent / "defaults.yaml"

    def get_recent_files(self) -> list:
        """Get list of recent files."""
        return self.get_value(HomeKeys.Recent.FILES, default=[])

    def update_recent_file(self, file_path: str):
        """Update recent files list."""
        recent_files = self.get_recent_files()
        if str(file_path) not in recent_files:
            recent_files.append(str(file_path))
            if len(recent_files) > 10:  # Keep only last 10
                recent_files.pop(0)
            self.set_value(HomeKeys.Recent.FILES, recent_files)


# Global instance
home_config = HomeConfig()
