"""
Toolbar Components for Table Views

This package provides a complete toolbar system for table widgets,
organized into functional groups with embedded components.

Architecture:
- Groups: Functional combinations containing their own component classes
- Toolbars: Complete toolbar solutions combining groups

Main Components:
- TableViewToolbar: Complete toolbar with filter, column, and export groups
- FilterGroup: Filter controls with embedded component classes
- ColumnGroup: Column visibility and management controls
- ExportGroup: Export functionality with embedded component classes

Usage:
    # Complete toolbar (most common)
    from fm.gui.shared.table_view.components.toolbar import TableViewToolbar

    # Individual groups for custom combinations
    from fm.gui.shared.table_view.components.toolbar import FilterGroup, ColumnGroup, ExportGroup
"""

from .table_view_toolbar import TableViewToolbar
from .groups import FilterGroup, ColumnGroup, ExportGroup

__all__ = [
    # Complete toolbar
    'TableViewToolbar',

    # Groups
    'FilterGroup',
    'ColumnGroup',
    'ExportGroup'
]
