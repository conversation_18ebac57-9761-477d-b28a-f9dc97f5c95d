# Export Functionality Design

## Current Architecture Analysis

### Affected Files and Components:
- **Export UI**: `flatmate/src/fm/gui/_shared_components/table_view_v2/components/toolbar/groups/export_group.py`
  - `ExportButton` class (lines 13-40)
  - `ExportGroup` class (lines 42-75)
- **Table View**: `flatmate/src/fm/gui/_shared_components/table_view_v2/fm_table_view.py`
  - `CustomTableView_v2` class with export signal connections (lines 345-346, 524-530)
- **Core Table**: `flatmate/src/fm/gui/_shared_components/table_view_v2/components/table_view_core.py`
  - `TableViewCore._export_data()` method (lines 309-325)
  - `TableViewCore.get_dataframe()` method (lines 365-367)
- **Data Model**: `flatmate/src/fm/gui/_shared_components/table_view_v2/components/table_utils/enhanced_table_model.py`
  - `EnhancedTableModel.get_dataframe()` method (lines 79-95)
- **Proxy Model**: `flatmate/src/fm/gui/_shared_components/table_view_v2/components/table_utils/enhanced_filter_proxy_model.py`
  - `EnhancedFilterProxyModel` class (lines 11-50)

### Current Signal Flow:
```
ExportButton.csv_export_requested
→ ExportGroup.csv_export_requested
→ TableViewToolbar.csv_export_requested
→ CustomTableView_v2._export_csv()
→ TableViewCore._export_data("csv")
→ TableViewCore.get_dataframe()
→ EnhancedTableModel.get_dataframe() [PROBLEM: Returns original data, not filtered]
```

### Root Cause Analysis:
The current `TableViewCore.get_dataframe()` method calls `self._model.get_dataframe()` which returns the original DataFrame from `EnhancedTableModel._original_data`, completely bypassing the `EnhancedFilterProxyModel` that handles filtering and sorting.

**Critical Issue**: The table uses a proxy model (`EnhancedFilterProxyModel`) for filtering/sorting, but export bypasses it and returns raw data from the source model.

## Required Architecture Changes

### 1. New Method: `get_visible_dataframe()`
**File**: `flatmate/src/fm/gui/_shared_components/table_view_v2/components/table_view_core.py`
**Location**: Add after line 367 (after existing `get_dataframe()` method)

```python
def get_visible_dataframe(self) -> pd.DataFrame:
    """Get the currently visible data as a pandas DataFrame.

    This method respects all active filters and sorting applied through
    the proxy model, returning exactly what the user sees in the table.

    Returns:
        pd.DataFrame: The filtered and sorted data as displayed
    """
    # Implementation details in tasks.md
```

### 2. Modified Method: `_export_data()`
**File**: `flatmate/src/fm/gui/_shared_components/table_view_v2/components/table_view_core.py`
**Location**: Lines 309-325 (existing method)

**Change**: Replace `df = self.get_dataframe()` with `df = self.get_visible_dataframe()`

### 3. Column Visibility Handling
The export must respect hidden columns and export only visible columns in their display order.

## Data Extraction Strategy

### Current Implementation (Broken):
```python
def _export_data(self, format_type):
    df = self.get_dataframe()  # Returns original data, ignoring filters
```

### New Implementation (Fixed):
```python
def _export_data(self, format_type):
    df = self.get_visible_dataframe()  # Returns filtered/sorted data as displayed
```

### Proxy Model Data Extraction:
The `get_visible_dataframe()` method must:
1. Iterate through the proxy model's visible rows
2. Extract data in the order displayed (respecting sorting)
3. Include only visible columns
4. Preserve any user edits made in the table

## Export Process Flow

1. User clicks export button in toolbar
2. `ExportButton._show_export_menu()` displays CSV/Excel options
3. User selects format → signal emitted
4. Signal chain: `ExportGroup` → `TableViewToolbar` → `CustomTableView_v2`
5. `CustomTableView_v2._export_csv()` or `_export_excel()` called
6. `TableViewCore._export_data(format_type)` called
7. **NEW**: `get_visible_dataframe()` extracts filtered/sorted data
8. File dialog prompts user for save location
9. Data written to file using pandas
10. Success/error message displayed

## Error Handling Strategy

### File I/O Errors:
- Catch `PermissionError`, `FileNotFoundError`, `OSError`
- Display user-friendly error messages via QMessageBox
- Log technical details for debugging

### Data Processing Errors:
- Handle empty result sets (no visible rows after filtering)
- Validate DataFrame before export
- Handle special characters and encoding issues

### Example Error Handling:
```python
try:
    df = self.get_visible_dataframe()
    if df.empty:
        QMessageBox.information(self, "Export", "No data to export. Please check your filters.")
        return
    # ... export logic
except Exception as e:
    QMessageBox.critical(self, "Export Error", f"Failed to export data: {str(e)}")
    log.error(f"Export failed: {e}")
```

## Integration Points

### With Column Manager:
- Export must respect column visibility settings
- Hidden columns should not appear in export
- Column order in export should match display order

### With Filter System:
- Export must respect all active filters
- Date range filters, text filters, account filters all apply
- Empty filter results should be handled gracefully

### With Categorize Module:
- Export is triggered from categorize module's transaction table
- Module path: `flatmate/src/fm/modules/categorize/_view/components/center_panel/transaction_view_panel.py`
- Table instance: `self.transaction_table` (CustomTableView_v2)

## Extensibility Design

### Future Format Support:
- New formats can be added to `ExportButton._show_export_menu()`
- `_export_data()` method can be extended with new format types
- File dialog filters can be updated for new extensions

### Advanced Export Features (Phase 2+):
- Export configuration panel
- Column selection for export
- Data transformation options
- Batch export capabilities
