# Export Functionality Design

## Architecture Overview

- The export functionality is initiated from the existing export button in the Categorise table view toolbar.
- Signal flow: `ExportButton` → `ExportGroup` → `FMTableView` → `TableViewCore._export_data()`.
- All export logic will be encapsulated in a single function: `export_current_view()`.

## Data Extraction

- The function must extract the data as currently displayed in the table, including all active filters and sorts.
- The exported data must match the visible columns, order, and row state.
- Data is converted to a `pandas.DataFrame` (or equivalent) for export.

## Export Process

1. User clicks the export button.
2. The system calls `export_current_view()`.
3. The function:
    - Extracts the visible table data (filters/sorts applied)
    - Prompts the user for file destination/format (CSV required; Excel optional)
    - Writes the data to the chosen file using pandas or built-in methods
    - Handles and reports errors to the user

## Error Handling

- All errors (e.g., file write issues) must be surfaced with clear, user-facing messages.
- No silent failures.

## Extensibility

- Design should allow for easy addition of new formats, export options, or advanced export modules in future phases.
