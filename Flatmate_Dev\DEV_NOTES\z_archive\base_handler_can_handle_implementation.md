# Base Handler `can_handle_file` Implementation

## Implementation Plan

### 1. Add to Base Class
Add the following method to `_base_statement_handler.py`:

```python
@classmethod
def can_handle_file(cls, filepath: str) -> bool:
    """Check if this handler can process the given file.
    
    Args:
        filepath: Path to the file to check
        
    Returns:
        bool: True if this handler can process the file, False otherwise
    """
    instance = cls()
    
    # Basic file checks
    if not filepath.lower().endswith('.csv'):
        return False
        
    try:
        # Read first few rows for validation
        df = pd.read_csv(
            filepath,
            header=0 if instance.column_attrs.has_headers else None,
            nrows=5
        )
        
        # Check column count
        if len(df.columns) != instance.column_attrs.n_source_cols:
            return False
            
        # Check account number in first row if specified
        if instance.account_num_attrs.in_header:
            first_row = pd.read_csv(filepath, header=None, nrows=1)
            if not re.search(instance.account_num_attrs.pattern, str(first_row.iloc[0, 0])):
                return False
                
        # Check date format in first data row
        if instance.column_attrs.date_format:
            first_date = str(df.iloc[0, 0])
            try:
                datetime.strptime(first_date, instance.column_attrs.date_format)
            except ValueError:
                return False
                
        return True
        
    except Exception:
        return False
```

### 2. Required Imports
Ensure these imports are at the top of `_base_statement_handler.py`:

```python
import re
from datetime import datetime
import pandas as pd
```

### 3. Handler Configuration
Handlers only need to define their attributes:

```python
class KiwibankBasicCSVHandler(StatementHandler):
    def __init__(self):
        super().__init__()
        self.statement_format = self.StatementFormat(
            bank_name="Kiwibank",
            variant="basic",
            file_type="csv"
        )
        
        self.column_attrs = self.ColumnAttributes(
            n_source_cols=5,
            has_headers=False,
            has_account_column=False,
            source_col_names=[],
            target_col_names=[...],
            date_format="%d-%b-%y"
        )
        
        self.account_num_attrs = self.AccountNumberAttributes(
            pattern=r"\d{2}-?\d{4}-?\d{7}-?\d{2,3}",
            in_file_name=True,
            in_header=True
        )
        
        self.source_metadata_attrs = self.SourceMetadataAttributes(
            has_metadata_rows=False
        )
```

### 4. Testing
Test with:

```python
# Should return True for matching files
assert KiwibankBasicCSVHandler.can_handle_file("kiwibank_statement.csv")

# Should return False for non-matching files
assert not KiwibankBasicCSVHandler.can_handle_file("other_bank.csv")
```

### 5. Error Handling
- Invalid files return `False`
- Exceptions during validation return `False`
- Handlers define their own validation rules through attributes
