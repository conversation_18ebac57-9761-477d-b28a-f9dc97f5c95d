"""
Base GUI components for the Flatmate application.

This module provides foundational abstract base classes that can be used
across different modules for consistent behavior and architecture.

Contains only abstract base classes and interfaces - concrete widgets
are in the widgets package.
"""

from .base_panel_component import BasePanelComponent
from .base_pane import BasePane

__all__ = [
    'BasePanelComponent',
    'BasePane'
]
