"""Left panel manager for the Categorize module."""

from PySide6.QtCore import Signal
from PySide6.QtWidgets import QVBoxLayout

from fm.gui._shared_components import BasePanelComponent
from .left_panel import LeftPanelWidget


class LeftPanelManager(BasePanelComponent):
    """Main left panel manager for the Categorize module."""
    
    # Signals
    files_select_requested = Signal()
    load_db_requested = Signal()
    apply_filters_clicked = Signal(dict)
    cancel_clicked = Signal()
    
    def __init__(self, parent=None):
        """Initialize the left panel manager."""
        super().__init__(parent)
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        # Main layout
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        
        # Create left panel
        self.left_panel = LeftPanelWidget(self)
        self.main_layout.addWidget(self.left_panel)
    
    def _connect_signals(self):
        """Connect signals between components."""
        self.left_panel.files_select_requested.connect(self.files_select_requested)
        self.left_panel.load_db_requested.connect(self.load_db_requested)
        self.left_panel.apply_filters_clicked.connect(self.apply_filters_clicked)
        self.left_panel.cancel_clicked.connect(self.cancel_clicked)
    
    def set_accounts(self, accounts):
        """Set the available accounts in the combo box."""
        self.left_panel.set_accounts(accounts)
    
    def get_filters(self):
        """Get the current filter settings."""
        return self.left_panel.get_filters()
    
    def show_component(self):
        """Show this component."""
        self.show()
        self.left_panel.show_component()
    
    def hide_component(self):
        """Hide this component."""
        self.hide()
        self.left_panel.hide_component()
    
    def disconnect_signals(self):
        """Clean up signal connections."""
        try:
            self.left_panel.files_select_requested.disconnect()
            self.left_panel.load_db_requested.disconnect()
            self.left_panel.apply_filters_clicked.disconnect()
            self.left_panel.cancel_clicked.disconnect()
        except (TypeError, RuntimeError):
            # Signal might not be connected
            pass