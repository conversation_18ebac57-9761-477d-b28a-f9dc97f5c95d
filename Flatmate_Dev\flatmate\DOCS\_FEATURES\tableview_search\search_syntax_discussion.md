# TableView Search Syntax Discussion

## Objective
Define an intuitive, flexible search/filter syntax for TableView that balances ease of use for basic users with power for advanced users, while keeping code complexity manageable.

---

## Options for Search Syntax

### 1. Simple Space-Separated Terms (Basic)
- **Syntax:** `term1 term2`
- **Interpretation:** All terms must be present (implicit AND).
- **Example:**  
  `rent utilities` → matches rows containing both "rent" and "utilities".
- **Pros:**  
  - Easiest for basic users.  
  - Minimal code complexity.
- **Cons:**  
  - No support for OR, grouping, or advanced queries.

---

### 2. Explicit Boolean Operators (AND, OR, NOT)
- **Syntax:** `term1 AND term2 OR term3`
- **Interpretation:** Standard boolean logic.
- **Example:**  
  `rent AND (utilities OR water)`
- **Pros:**  
  - Familiar to advanced users.  
  - Industry standard (Google, Jira, etc.).
- **Cons:**  
  - Requires parsing logic (tokenisation, precedence, grouping).  
  - Can confuse basic users if not documented.

---

### 3. Minimal Parentheses Grouping
- **Syntax:** `term1 (term2 OR term3)`
- **Interpretation:**  
  - First term is always ANDed with the group.
- **Example:**  
  `rent (utilities OR water)`
  → matches rows with "rent" and either "utilities" or "water".
- **Pros:**  
  - Intuitive for most users.  
  - Simpler parser than full boolean logic.
- **Cons:**  
  - Not as flexible as full boolean logic.  
  - Slightly nonstandard (but close).

---

### 4. Logical Operator Alternatives
- **Syntax Options:**
  - `OR`, `/`, `|` (all are treated as logical OR, always supported)
  - `AND` (always supported, equivalent to space between terms)
- **Usage:**
  - Operators should be written in capitals for clarity: `AND`, `OR`, `NOT`
  - Users can use any of these forms interchangeably:
    - `rent OR utilities`, `rent/utilities`, `rent | utilities` all mean the same thing (logical OR)
    - `rent utilities`, `rent AND utilities` both mean the same thing (logical AND)
- **Industry/Database Standard:**
  - SQL, Elasticsearch, MongoDB, Jira, Google: all use `AND`, `OR`, `NOT`
  - Pipe (`|`) is rarely used in database search, but is common in command-line tools and regex.
  - Slash (`/`) is used in a few UIs (e.g., Slack quick filters), but not standard in databases.
- **Pros/Cons:**
  - `OR`, `AND`: Clear, easy to type, discoverable, and standard.
  - `|`: Advanced/technical, hard to find on keyboard, not discoverable.
  - `/`: Intuitive for some, but can be confused with literal slashes in data.
- **Recommendation:**
  - All logical operators (`AND`, `OR`, `/`, `|`) are always supported and synonymous with their respective logic. Use capitals for operator words for clarity.

---

### 4. Quoted Phrases
- **Syntax:**  
  - `"exact phrase"` for exact matches.
- **Example:**  
  `"late fee"`
- **Pros:**  
  - Standard in many search UIs.  
  - Useful for power users.
- **Cons:**  
  - Adds parser complexity.  
  - May be overkill for most users.
---

### 5. Exclude Logic (Negation)
- **Syntax:**
  - `-term` (minus sign before term)
  - `NOT term` (optional, more explicit)
- **How it works:**
  - Matches rows that do NOT contain the excluded term.
  - Can be combined with AND/OR/parentheses and phrases.
- **Examples:**
  - `rent -utilities` (rows with "rent" but not "utilities")
  - `rent (utilities OR water) -"late fee"`
  - `rent NOT utilities`
- **Industry Standard:**
  - Google, Gmail, GitHub: `-term`
  - Jira: `NOT term`
- **Pros:**
  - Widely used and intuitive (`-term`)
  - Powerful for advanced filtering
- **Cons:**
  - Slight parser complexity increase
  - Users may not discover unless shown in UI

---

## Table: Syntax Permutations

| User Input           | Meaning            | Supported? |
|----------------------|--------------------|------------|
| `term1 term2`        | AND                | Yes        |
| `term1 AND term2`    | AND                | Yes        |
| `term1 OR term2`     | OR                 | Yes        |
| `term1/term2`        | OR                 | Yes        |
| `term1 | term2`      | OR                 | Yes        |
| `term1 NOT term2`    | AND NOT            | Yes        |
| `term1 -term2`       | AND NOT            | Yes        |
| `"exact phrase"`     | exact match        | Yes        |

---

## Recommendations

- **Default:**  
  Space-separated terms = AND (basic users).
- **Support:**  
  All logical operators are always available and synonymous: `AND` (or space), `OR`, `/`, `|` for OR; `NOT`, `-` for NOT. Operators should be in capitals for clarity.
  Parentheses for grouping.
- **Quoted phrases:**  
  Use quotes for exact matches or to include operator symbols as literals.
- **Negation:**
  Use either `-term` or `NOT term` for exclusion.
- **Case-insensitive:**
  All search and match logic should be case-insensitive by default.
- **Examples to Support:**  
  `rent utilities` (AND)  
  `rent AND utilities`  
  `rent OR utilities`  
  `rent/utilities`  
  `rent | utilities`  
  `rent (utilities OR water)`  
  `"late fee" -utilities`  
  `rent -utilities`  
  `rent NOT utilities`
  `"rent/utilities"`
- **Industry Standard:**  
  Google: `term1 term2 OR term3`, `-term`  
  Jira: `AND`, `OR`, `NOT`, parentheses
- **Parser Complexity:**  
  Start with simple AND/OR/parentheses and `-term` negation.  
  Use a recursive descent parser or adapt an existing boolean search parser if needed.

---

## Summary
- Support space-separated AND, explicit AND (`AND`), explicit OR (with `OR`, `/`, `|`), and grouping with parentheses.
- All operators are always available and synonymous.
- Show users examples in the UI.
- Keep parser logic as simple as possible at first.

---

## Implementation: Using a Parsing Package

Instead of writing a custom parser, consider using an existing Python package to handle boolean search syntax. This can reduce complexity and improve maintainability.

**Popular options:**

- **boolean.py**
  - Simple boolean expression parser; supports AND, OR, NOT, parentheses.
  - Extendable via pre-processing for synonyms (`/`, `|`, etc.).
- **pyparsing**
  - General parser library; can define your own grammar to support all required operators and synonyms.
- **lucene-query-parser**
  - Parses Lucene/Google-style boolean queries; supports AND, OR, NOT, parentheses, quotes.
- **Whoosh**
  - Full-text search engine with a robust query parser (heavier, but powerful if search indexing is needed).

**Recommendation:**
- For simple needs: `boolean.py` (with pre-processing for operator synonyms).
- For maximum flexibility: `pyparsing` or `lucene-query-parser`.

If using a package, ensure:
- Operator synonyms (`AND`, `OR`, `/`, `|`, `NOT`, `-`) are mapped and normalised in pre-processing.
- All matching remains case-insensitive.

---
# future dev note : advanced options could allow choosing a search parsing engine (e.g. boolean.py, pyparsing, lucene-query-parser, whoosh) 

- denoted as search syntax style::
- referenceing popular apps - google, jira, github, slack, etc.
implement different packages behind the scenes

