"""
Shared widget components for the Flatmate application.

This package provides reusable concrete UI widgets that can be used
across different modules for consistent styling and behavior.

Organized by widget type:
- buttons: ActionButton, SecondaryButton, ExitButton
- option_menus: OptionMenuWithLabel, OptionMenuWithLabelAndButton
- checkboxes: LabeledCheckBox
"""

from .buttons import ActionButton, SecondaryButton, ExitButton
from .option_menus import OptionMenuWithLabel, OptionMenuWithLabelAndButton
from .checkboxes import LabeledCheckBox

__all__ = [
    # Buttons
    'ActionButton',
    'SecondaryButton',
    'ExitButton',

    # Option Menus
    'OptionMenuWithLabel',
    'OptionMenuWithLabelAndButton',

    # Checkboxes
    'LabeledCheckBox'
]
