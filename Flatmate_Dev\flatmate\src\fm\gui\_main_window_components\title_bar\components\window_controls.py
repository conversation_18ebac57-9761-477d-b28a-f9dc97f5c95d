"""
Window controls component for the custom title bar.

This module provides a group of window control buttons (minimize, maximize/restore, close)
that can be used in a custom title bar.
"""

from PySide6.QtWidgets import QWidget, QHBoxLayout, QPushButton
from PySide6.QtCore import Qt
from ....config.gui_keys import G<PERSON><PERSON><PERSON><PERSON>
from ....config.gui_config import gui_config


class WindowControlsGroup(QWidget):
    """Group of window control buttons (minimize, maximize/restore, close)."""
    
    def __init__(self, parent=None):
        """Initialize the window controls group.

        Args:
            parent: The parent widget (should be the main window)
        """
        super().__init__(parent)
        self.parent = parent
        self.setup_ui()
        self._connect_signals()
        
    def setup_ui(self):
        """Set up the window control buttons."""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Create buttons
        self.minimize_btn = self._create_button("—", "minimize", "Minimize window")
        self.maximize_btn = self._create_button("□", "maximize", "Maximize window")
        self.close_btn = self._create_button("×", "close", "Close window")
        
        # Add buttons to layout
        layout.addWidget(self.minimize_btn)
        layout.addWidget(self.maximize_btn)
        layout.addWidget(self.close_btn)
        
    def _create_button(self, text, name, tooltip):
        """Create a styled window control button.
        
        Args:
            text: The button text (icon or symbol)
            name: The object name for styling
            tooltip: The tooltip text to display on hover
            
        Returns:
            QPushButton: The configured button
        """
        btn = QPushButton(text)
        btn.setObjectName(name)
        btn.setFixedSize(46, 32)
        btn.setToolTip(tooltip)
        btn.setStyleSheet("""
            QPushButton {
                background: transparent;
                color: #CCCCCC;
                border: none;
                font-size: 16px;
                font-weight: 300;
            }
            QPushButton:hover {
                background: #2D2D2D;
            }
            QPushButton#close:hover {
                background: #E81123;
                color: white;
            }
            QPushButton:pressed {
                background: #1E1E1E;
            }
            QPushButton#close:pressed {
                background: #F1707A;
            }
        """)
        return btn
        
    def _connect_signals(self):
        """Connect button signals to window actions."""
        self.minimize_btn.clicked.connect(lambda: self.parent.showMinimized())
        self.maximize_btn.clicked.connect(self._toggle_maximize)
        self.close_btn.clicked.connect(lambda: self.parent.close())

    def _toggle_maximize(self):
        """Toggle between maximized and normal window state."""
        if self.parent.isMaximized():
            self.parent.showNormal()
        else:
            self.parent.showMaximized()
        # FramelessWindow library handles button state updates automatically


