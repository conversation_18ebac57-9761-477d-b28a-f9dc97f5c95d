# Data Services Layer

The **Data Services** layer provides enhanced, high-level services for working with transaction data. This layer sits above the core database implementation and adds sophisticated features like column management, date formatting, and user preferences.

## Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    DATA SERVICES LAYER                      │
│  Enhanced services with column management & formatting      │
├─────────────────────────────────────────────────────────────┤
│                    CORE DATABASE LAYER                      │
│     Repository pattern, SQL implementation, models         │
├─────────────────────────────────────────────────────────────┤
│                    STANDARDS LAYER                          │
│          Canonical column definitions (fm_standard)        │
└─────────────────────────────────────────────────────────────┘
```

## Key Components

### Service Interfaces (Top Level)

- **`db_io_service.py`** - Enhanced database operations with column management
- **`column_manager.py`** - Column handling with user preferences and metadata
- **`date_format_service.py`** - Date formatting between database and display formats

### Utility Components (`utils/`)

- **`standard_columns.py`** - Enhanced column definitions with rich metadata
- **`converters.py`** - Data format converters (CSV ↔ Transaction)
- **`events.py`** - Event system for data operations

## Usage Examples

### Basic Database Operations

```python
from fm.core.data_services import DBIOService

# Initialize service
service = DBIOService()

# Import data
result = service.update_database(dataframe, source_file="bank_statement.csv")

# Get transactions with display formatting
df = service.get_transactions(
    use_display_names=True,
    only_columns_with_data=True
)
```

### Column Management

```python
from fm.core.data_services import ColumnManager, StandardColumns

# Initialize column manager
col_mgr = ColumnManager()

# Get columns for specific usage
display_cols = col_mgr.get_columns_by_usage("display")
categorize_cols = col_mgr.get_columns_by_usage("categorize")

# Convert DataFrame to display format
display_df = col_mgr.get_df_with_display_columns(raw_df)
```

### Date Formatting

```python
from fm.core.data_services import DateFormatService, DateDisplayFormat

# Format single date
display_date = DateFormatService.format_date_for_display("2023-12-25")
# Returns: "25/12/2023" (NZ format) or "12/25/2023" (US format)

# Format DataFrame dates
formatted_df = DateFormatService.format_dataframe_dates(df)
```

## Integration with Core Database

The data services layer integrates seamlessly with the core database:

```python
# Data services uses core database components
from fm.core.database import SQLiteTransactionRepository, Transaction

# But provides enhanced functionality
from fm.core.data_services import DBIOService

service = DBIOService()  # Automatically uses SQLiteTransactionRepository
```

## Column System Integration

The enhanced column system works with the canonical definitions:

- **`fm.core.data_services.standards.fm_standard_columns`** - Canonical source of truth
- **`fm.core.data_services.utils.standard_columns`** - Enhanced with metadata
- **Column Manager** - Handles conversion and user preferences

## Benefits

1. **Enhanced Functionality** - Rich metadata, user preferences, formatting
2. **Backward Compatibility** - Works with existing canonical column definitions
3. **Clean Separation** - Service layer vs core database implementation
4. **Developer Friendly** - Logical organization, easy to find components
5. **Extensible** - Easy to add new services and utilities

## Migration from Old System

If you're migrating from the old `database_v2` system:

```python
# OLD (superseded)
from fm.core.database_v2 import db_io_service

# NEW (enhanced)
from fm.core.data_services import DBIOService
service = DBIOService()
```

The new system provides all the functionality of the old system plus enhanced features.

## See Also

- **`core/database/`** - Core database implementation
- **`core/standards/`** - Canonical column definitions
- **`docs/`** - Additional documentation and design documents
