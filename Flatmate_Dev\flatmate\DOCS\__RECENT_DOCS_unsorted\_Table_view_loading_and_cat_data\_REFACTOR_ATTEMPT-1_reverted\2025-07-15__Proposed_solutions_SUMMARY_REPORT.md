# Summary Report: Proposed Solutions

**Generated:** 2025-07-15

---

## 1. Recommended Architecture

The proposed solution to the application's performance issues is a two-pronged architectural enhancement designed to work in tandem:

1.  **Centralized Data Caching:** At application startup, load the entire transaction database into a dedicated, in-memory cache (e.g., a pandas DataFrame). All subsequent read operations (filtering, sorting, searching) will query this cache, not the database, resulting in near-instantaneous UI updates.

2.  **Background Data Preparation:** Move the initial, computationally expensive data processing—such as transaction categorization and default sorting—to a non-blocking background thread. This process will run concurrently during application startup, preparing the data that will populate the cache.

This dual approach ensures that the UI remains responsive at all times. The initial application startup will feel fast because the UI is not blocked, and subsequent user interactions will be immediate because they operate on the pre-processed, in-memory data.

## 2. Key Components

### A. `TransactionCacheService` (or similar)
-   **Responsibility:** To hold the master, in-memory copy of the transaction data.
-   **Functionality:** It will be populated by the background preparation service. The existing `DBIOService` will be enhanced to use this cache transparently, meaning modules that request data from `DBIOService` will not need to be significantly refactored.
-   **Memory Management:** The service will include checks for available system memory to ensure it does not consume excessive resources, with a sensible default limit (e.g., 512MB) and the ability to fall back to direct database queries if the dataset is too large.

### B. `DataPreparationService`
-   **Responsibility:** To manage the background thread that prepares the data for the cache.
-   **Functionality:** Using a `QThreadPool` and a `QRunnable` worker, this service will fetch raw data, apply categorization logic, and perform initial sorting. Upon completion, it will hand the prepared DataFrame to the cache.
-   **UI Integration:** The service will provide signals to update the UI (e.g., an `InfoBar`) on its progress and completion, keeping the user informed.

## 3. Implementation Strategy

The implementation will be phased:

1.  **Phase 1: Build Core Services:** Create the `DataPreparationService` and the underlying `TransactionCacheService` or enhance `DBIOService` with caching capabilities.
2.  **Phase 2: Integrate into Application Flow:** Modify `main.py` to initiate the background data preparation on startup. Refactor the `CategorizePresenter` to fetch its data from the cache service instead of performing the processing itself. This will dramatically simplify the presenter's logic.
3.  **Phase 3: UI/UX Enhancements:** Ensure the `InfoBar` provides clear feedback on the background process. Implement graceful fallbacks for cases where the user navigates to a module before its data has been fully prepared.

This strategy directly addresses the identified bottlenecks by moving the heavy lifting to a background startup process, transforming the user experience from slow and blocking to fast and responsive.
