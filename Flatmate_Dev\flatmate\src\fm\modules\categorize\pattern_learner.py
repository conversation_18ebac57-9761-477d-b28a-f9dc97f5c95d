"""
Pattern learning and auto-categorization module.
Learns from user categorizations and suggests categories for new transactions.
"""

import json
import logging
from typing import Dict, List, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class PatternLearner:
    def __init__(self, profile_path: Path):
        self.profile_path = profile_path
        self.patterns_file = profile_path / 'learned_patterns.json'
        self.exact_matches_file = profile_path / 'known_transactions.json'
        
        # Load or initialize patterns
        self.patterns: Dict[str, List[str]] = self._load_json(self.patterns_file, {})
        self.known_transactions: Dict[str, str] = self._load_json(self.exact_matches_file, {})
        
    def _load_json(self, file_path: Path, default: dict) -> dict:
        """Load JSON file or return default if file doesn't exist."""
        try:
            if file_path.exists():
                with open(file_path, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"Error loading {file_path}: {e}")
        return default
        
    def _save_json(self, data: dict, file_path: Path):
        """Save data to JSON file."""
        try:
            with open(file_path, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving {file_path}: {e}")

    def learn_from_transaction(self, description: str, category: str):
        """Learn from a user-categorized transaction."""
        description = description.upper()
        
        # Save exact match
        self.known_transactions[description] = category
        self._save_json(self.known_transactions, self.exact_matches_file)
        
        # Initialize category patterns if needed
        if category not in self.patterns:
            self.patterns[category] = []
            
        # Extract potential patterns
        words = description.split()
        for word in words:
            if len(word) > 3:  # Ignore small words
                if word not in self.patterns[category]:
                    self.patterns[category].append(word)
                    logger.debug(f"Learned new pattern for {category}: {word}")
        
        self._save_json(self.patterns, self.patterns_file)

    def suggest_category(self, description: str) -> Optional[str]:
        """Suggest category for a transaction description."""
        description = description.upper()
        
        # Check exact matches first
        if description in self.known_transactions:
            return self.known_transactions[description]
            
        # Check patterns
        matches = {}
        for category, patterns in self.patterns.items():
            for pattern in patterns:
                if pattern in description:
                    matches[category] = matches.get(category, 0) + 1
        
        # Return category with most pattern matches
        if matches:
            return max(matches.items(), key=lambda x: x[1])[0]
            
        return None

    def get_similar_transactions(self, description: str) -> List[str]:
        """Find similar categorized transactions."""
        description = description.upper()
        similar = []
        
        # Find transactions with overlapping words
        words = set(w for w in description.split() if len(w) > 3)
        for known_desc, category in self.known_transactions.items():
            known_words = set(w for w in known_desc.split() if len(w) > 3)
            if words & known_words:  # If there's word overlap
                similar.append((known_desc, category))
                
        return similar[:5]  # Return top 5 similar transactions

    def bulk_categorize(self, transactions: List[dict]) -> List[dict]:
        """Auto-categorize a list of transactions."""
        for tx in transactions:
            if not tx.get('category'):  # Only suggest for uncategorized
                suggested = self.suggest_category(tx['description'])
                if suggested:
                    tx['category'] = suggested
                    tx['auto_categorized'] = True
                    logger.info(f"Auto-categorized: {tx['description']} -> {suggested}")
        return transactions
