# Export Functionality Tasks

## Implementation Steps (Phase 1)

- [ ] Ensure the export button in the Categorise view is connected to a dedicated `export_current_view()` function.
- [ ] Implement logic to extract the currently visible table data (including filters and sorts).
- [ ] Convert the extracted data to a `pandas.DataFrame` or equivalent.
- [ ] Implement a file dialog to prompt the user for destination and format (CSV required, Excel optional).
- [ ] Write the DataFrame to the chosen file using pandas or built-in methods.
- [ ] Display clear, user-facing error messages for any export failures.
- [ ] Keep all logic explicit, readable, and well-commented for maintainability.

## Future Steps (Phase 2+)

- [ ] Add support for additional formats (Excel, JSON, etc.).
- [ ] Implement advanced export panel/module for customisation and batch export.
- [ ] Add support for export presets and templates.
