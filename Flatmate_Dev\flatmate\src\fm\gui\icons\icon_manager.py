"""
Icon manager that automatically discovers and provides access to icons.
Works with the existing folder structure and provides a clean interface.
"""
from pathlib import Path
from typing import Dict, Optional, Union

# Base directory for all icons
ICON_DIR = Path(__file__).parent

# Icon categories and their base paths
ICON_CATEGORIES = {
    "nav": {
        "base_path": ICON_DIR / "nav_pane",
        "icons": ["home", "profiles", "import_data", "view_data"]
    },
    "utilities": {
        "base_path": ICON_DIR / "uitilities_pane",
        "icons": ["settings"]
    },
    "settings": {
        "base_path": ICON_DIR / "settings",
        "icons": ["gear"]
    }
}


class IconManager:
    """
    Discovers and provides access to icons in the existing folder structure.
    """
    _instance = None
    _icon_cache: Dict[str, Path] = {}
    
    def __new__(cls):
        # Singleton pattern to ensure we only create one instance
        if cls._instance is None:
            cls._instance = super(IconManager, cls).__new__(cls)
            cls._instance._discover_icons()
        return cls._instance
    
    def _discover_icons(self) -> None:
        """
        Scan the icon directories and build a cache of available icons.
        """
        # Clear existing cache
        self._icon_cache = {}
        
        # Discover icons in each category
        for category, info in ICON_CATEGORIES.items():
            base_path = info["base_path"]
            for icon_name in info["icons"]:
                icon_path = self._find_icon(base_path, icon_name)
                if icon_path:
                    cache_key = f"{category}.{icon_name}"
                    self._icon_cache[cache_key] = icon_path
    
    def _find_icon(self, base_path: Path, icon_name: str) -> Optional[Path]:
        """
        Find the first SVG icon in the given path.
        
        Args:
            base_path: Base directory to search in
            icon_name: Name of the icon to find
            
        Returns:
            Path to the icon if found, None otherwise
        """
        # Check for icon in the direct subfolder
        icon_dir = base_path / icon_name
        
        # Simply get the first SVG file in the directory
        if icon_dir.exists():
            svg_files = list(icon_dir.glob("*.svg"))
            if svg_files:
                return svg_files[0]
        
        # If no icon found in main directory, check in the 'selected' subfolder
        selected_dir = icon_dir / "selected"
        if selected_dir.exists():
            svg_files = list(selected_dir.glob("*.svg"))
            if svg_files:
                return svg_files[0]
        
        # If no icon found, return None
        return None
    
    def get_icon(self, category: str, name: str) -> Path:
        """
        Get the path to an icon.
        
        Args:
            category: Icon category (nav, utilities, settings)
            name: Icon name
            
        Returns:
            Path to the icon
            
        Raises:
            FileNotFoundError: If the icon doesn't exist
        """
        cache_key = f"{category}.{name}"
        
        # Check if we need to refresh the cache
        if not self._icon_cache:
            self._discover_icons()
            
        # Get icon from cache
        if cache_key in self._icon_cache:
            return self._icon_cache[cache_key]
        
        # Icon not found
        raise FileNotFoundError(f"Icon not found: {category}.{name}")
    
    def get_nav_icon(self, name: str) -> Path:
        """
        Convenience method to get a navigation icon.
        
        Args:
            name: Icon name (home, profiles, etc.)
            
        Returns:
            Path to the icon
        """
        return self.get_icon("nav", name)
    
    def get_settings_icon(self, name: str = "gear") -> Path:
        """
        Convenience method to get a settings icon.
        
        Args:
            name: Icon name (default: gear)
            
        Returns:
            Path to the icon
        """
        return self.get_icon("settings", name)
    
    def get_utilities_icon(self, name: str) -> Path:
        """
        Convenience method to get a utilities icon.
        
        Args:
            name: Icon name (settings, etc.)
            
        Returns:
            Path to the icon
        """
        return self.get_icon("utilities", name)
    
    def list_available_icons(self) -> Dict[str, Path]:
        """
        Get a dictionary of all available icons.
        
        Returns:
            Dictionary mapping icon names to paths
        """
        # Ensure cache is populated
        if not self._icon_cache:
            self._discover_icons()
            
        return self._icon_cache.copy()


# Create a singleton instance for easy import
icon_manager = IconManager()


# Simple usage examples:
# from fm.gui.icons.icon_manager import icon_manager
# 
# # Get a navigation icon
# home_icon = icon_manager.get_nav_icon("home")
# 
# # Get a settings icon
# settings_icon = icon_manager.get_settings_icon()
