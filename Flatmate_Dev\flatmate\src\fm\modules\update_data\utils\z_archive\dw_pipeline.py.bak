#!/usr/bin/env python3
"""
Pipeline functions for processing bank CSV files.
"""

import datetime

# Standard library imports
import os
import re
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np

# Third-party imports
import pandas as pd

# Core imports
from fm.core.services.logger import log
from fm.core.utils.date_utils import convert_df_dates

# Data service and converter imports for database updates
from fm.data_base_serviceeCSVToTransactionConverter, data_service
from fm.data.repository.transaction_repository import ImportResult
from fm.modules.update_data.utils.statement_handlers.standards.fm_column_format import (
    FmColumnFormat,
)

# Local imports
from ..config.ud_config import ud_config
from ..config.ud_keys import UpdateDataKeys
from .processing_tracker import processed_files_tracker
from .statement_handlers._handler_registry import get_handler


@dataclass
class UnrecognisedFile:
    filepath: str
    reason: str


def load_csv_file(filepath: str) -> Optional[pd.DataFrame]:
    """Load a CSV file into a DataFrame with basic validation.

    Args:
        filepath: Path to the CSV file

    Returns:
        DataFrame with filepath and filename attributes or None if loading fails
    """
    if not os.path.exists(filepath) or not os.path.isfile(filepath):
        log(f"File does not exist: {filepath}", level="error")
        return None

    try:
        # Read the CSV file into a DataFrame
        reader = pd.read_csv(filepath)

        # Handle both DataFrame and TextFileReader cases
        if hasattr(reader, "get_chunk"):
            # It's a TextFileReader, convert to DataFrame
            df = pd.DataFrame(reader)
        else:
            # It's already a DataFrame
            df = reader

        # Store filepath and filename as attributes using a dictionary approach
        # This avoids lint errors with direct attribute assignment
        df.attrs["filepath"] = filepath
        df.attrs["filename"] = os.path.basename(filepath)

        # Also set as direct attributes for backward compatibility
        df.filepath = filepath
        df.filename = os.path.basename(filepath)
        return df
    except Exception as e:
        log(f"Error loading CSV file: {str(e)}", level="error")
        return None


def process_with_handler(df: pd.DataFrame, filepath: str) -> Optional[pd.DataFrame]:
    """Process DataFrame with appropriate statement handler.

    Args:
        df: DataFrame to process
        filepath: Original file path for tracking

    Returns:
        Formatted DataFrame or None if no handler matches
    """
    # Get appropriate handler
    handler = get_handler(df, filepath)

    if not handler:
        log(f"No matching handler for: {filepath}", level="warning")
        return None

    # Format using handler
    try:
        formatted_df = handler.format_df(df)
        # Preserve filepath attribute
        if formatted_df is not None:
            formatted_df.filepath = filepath
            processed_files_tracker.add(filepath)
            return formatted_df
        else:
            log(f"Formatting failed for: {filepath}", level="warning")
            return None
    except Exception as e:
        log(f"Error formatting with handler: {str(e)}", level="error")
        return None


def merge_dataframes(
    formatted_dfs: List[pd.DataFrame],
) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """Merge formatted DataFrames and handle duplicates.

    Args:
        formatted_dfs: List of formatted DataFrames

    Returns:
        Tuple of (merged DataFrame, stats dictionary)
    """
    # Remove empty rows from input DataFrames
    clean_dfs = []
    for df in formatted_dfs:
        clean_df = df.dropna(how="all").copy()
        clean_dfs.append(clean_df)

    # Handle empty input case
    if not clean_dfs:
        return pd.DataFrame(), {"duplicates_removed": 0, "total_rows": 0}

    # Concatenate DataFrames
    df = pd.concat(clean_dfs, axis=0, ignore_index=True)

    # Count rows before deduplication
    before_count = len(df)

    # If 'Balance' column exists, include it in deduplication
    # Otherwise use default behavior which compares all columns
    if "Balance" in df.columns:
        # Include Balance in deduplication to handle discrete events with same attributes
        key_columns = ["Date", "Details", "Amount", "Balance"]
        if all(col in df.columns for col in key_columns):
            df = df.drop_duplicates(subset=key_columns, keep="first")
    else:
        # Use default drop_duplicates which compares all columns
        df = df.drop_duplicates(keep="first")

    # Count duplicates removed
    duplicates_removed = before_count - len(df)

    # Calculate stats
    stats = {"duplicates_removed": duplicates_removed, "total_rows": len(df)}

    return df, stats


def process_files(
    filepaths: List[str],
) -> Tuple[pd.DataFrame, Dict[str, Any], List[Dict[str, str]]]:
    """Process multiple files and return a merged DataFrame with stats.

    This encapsulates the entire file processing pipeline in one function.

    Args:
        filepaths: List of file paths to process

    Returns:
        Tuple of (merged DataFrame, stats dictionary, unrecognized files list)
    """
    # Initialize tracking for unrecognized files
    # Note: processed files are tracked via the processed_files_tracker singleton
    unrecognized_files = []

    # Step 1: Load all CSV files
    loaded_dfs = []
    for filepath in filepaths:
        try:
            df = load_csv_file(filepath)
            if df is not None:
                loaded_dfs.append(df)
            else:
                unrecognized_files.append(
                    {"filepath": filepath, "reason": "Failed to load"}
                )
        except Exception as e:
            unrecognized_files.append({"filepath": filepath, "reason": str(e)})

    # Step 2: Process each DataFrame with appropriate handler
    formatted_dfs = []
    for df in loaded_dfs:
        try:
            formatted_df = process_with_handler(df, df.filepath)
            if formatted_df is not None:
                formatted_dfs.append(formatted_df)
                # Note: processed_files_tracker.add() is already called in process_with_handler
            else:
                unrecognized_files.append(
                    {"filepath": df.filepath, "reason": "No matching handler"}
                )
        except Exception as e:
            unrecognized_files.append({"filepath": df.filepath, "reason": str(e)})

    # Step 3: Merge all formatted DataFrames
    if formatted_dfs:
        merged_df, stats = merge_dataframes(formatted_dfs)
        # Get processed files from the tracker instead of maintaining a separate list
        stats["processed_files"] = processed_files_tracker.get()
    else:
        merged_df = pd.DataFrame()
        stats = {"duplicates_removed": 0, "total_rows": 0, "processed_files": []}

    return merged_df, stats, unrecognized_files


def update_database_from_df(
    df: pd.DataFrame, source_file: str = ""
) -> Optional[ImportResult]:
    """Update the database with transactions from a DataFrame.

    Args:
        df: DataFrame containing transaction data
        source_file: Path to the source file for reference

    Returns:
        ImportResult object or None if update fails
    """
    try:
        # Check if DataFrame is empty
        if df.empty:
            return ImportResult()

        # Make a copy to avoid modifying the original
        df = df.copy()

        # Convert date objects to ISO format strings if needed
        date_col = FmColumnFormat.DATE.value
        if date_col in df.columns and pd.api.types.is_datetime64_any_dtype(
            df[date_col]
        ):
            # Convert datetime objects to ISO format strings
            df[date_col] = df[date_col].dt.strftime("%Y-%m-%d")

        # Import directly to database
        import_result = data_service.import_transactions_from_df(df, source_file)

        log(
            f"Database updated: {import_result.added_count} added, {import_result.duplicate_count} duplicates",
            level="info",
        )

        return import_result
    except Exception as e:
        log(f"Error updating database: {str(e)}", level="error")
        return None


def save_master_file(df: pd.DataFrame, save_path: str) -> Dict[str, Any]:
    """Save DataFrame to CSV file.

    Args:
        df: DataFrame to save
        save_path: Path to save the file

    Returns:
        Dictionary with save results
    """
    result = {"save_success": False}

    try:
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(save_path), exist_ok=True)

        # Find the date column if it exists
        date_column = None
        for col in df.columns:
            if col.lower() == "date":
                date_column = col
                break

        # Create a copy for CSV output
        df_to_save = df.copy()

        # Format dates for CSV output if needed
        if date_column and pd.api.types.is_datetime64_any_dtype(
            df_to_save[date_column]
        ):
            # Format as DD-MM-YY (UK style) for CSV
            df_to_save[date_column] = df_to_save[date_column].dt.strftime("%d-%m-%y")
            log("Formatted dates as DD-MM-YY for CSV output", level="info")

        # Save to CSV
        df_to_save.to_csv(save_path, index=False)
        log(f"Master file saved to: {save_path}", level="info")
        result["save_success"] = True

        return result
    except Exception as e:
        log(f"Error saving master file: {str(e)}", level="error")
        return result


def update_database_records(df: pd.DataFrame, source_file: str = "") -> Dict[str, Any]:
    """Update the database with transactions from a DataFrame.

    Args:
        df: DataFrame containing transaction data
        source_file: Path to the source file for reference

    Returns:
        Dictionary with database update results
    """
    result = {"database_updated": False, "import_result": None}

    try:
        if df.empty:
            return result

        # Create a copy for database update
        df_for_db = df.copy()

        # Remove any invalid_date column if it exists
        if "invalid_date" in df_for_db.columns:
            df_for_db = df_for_db.drop("invalid_date", axis=1)

        # Get the standard date column name
        date_col = FmColumnFormat.DATE.value

        # Check if date column exists
        if date_col not in df_for_db.columns:
            # Try to find a date column with a different name
            for col in df_for_db.columns:
                if col.lower() == "date":
                    # Rename to standard column name
                    df_for_db.rename(columns={col: date_col}, inplace=True)
                    log(f"Renamed column '{col}' to '{date_col}'")
                    break

        # Verify date column exists
        if date_col not in df_for_db.columns:
            log(
                f"Date column '{date_col}' not found in DataFrame. Cannot update database.",
                level="error",
            )
            return result

        # Get sample of current date formats for debugging
        sample_dates = df_for_db[date_col].head(3).tolist()
        log(f"Sample dates before conversion: {sample_dates}", level="info")

        # Convert dates to ISO format using our utility function
        convert_df_dates(df_for_db, date_col)
        log("Standardized dates to ISO format for database", level="info")

        # Check for null dates after conversion and drop those rows
        null_dates = df_for_db[date_col].isna().sum()
        if null_dates > 0:
            log(
                f"Found {null_dates} rows with null dates after conversion. Removing these rows.",
                level="warning",
            )
            df_for_db = df_for_db.dropna(subset=[date_col])

            # If all dates were null, return
            if df_for_db.empty:
                log(
                    "All rows had null dates. No data to update database with.",
                    level="error",
                )
                return result

        # Log sample dates for debugging
        if len(df_for_db) > 0:
            # Get up to 3 sample dates
            # Ensure df_for_db is a DataFrame with iloc attribute
            if not isinstance(df_for_db, pd.DataFrame):
                df_for_db = pd.DataFrame(df_for_db)

            if len(df_for_db) >= 3:
                sample_dates = df_for_db[date_col].iloc[:3].tolist()
            else:
                sample_dates = df_for_db[date_col].tolist()

            log(f"Sample dates for database: {sample_dates}", level="info")
        else:
            log("No dates available to sample", level="warning")

        # Update database with all records
        log(f"Sending {len(df_for_db)} records to database", level="info")
        # Ensure df_for_db is a DataFrame before passing to update_database_from_df
        if not isinstance(df_for_db, pd.DataFrame):
            log("Converting to DataFrame before database update", level="warning")
            df_for_db = pd.DataFrame(df_for_db)
        import_result = update_database_from_df(df_for_db, source_file=source_file)

        if import_result:
            result["database_updated"] = True
            result["import_result"] = {
                "added_count": import_result.added_count,
                "duplicate_count": import_result.duplicate_count,
                "error_count": import_result.error_count,
            }
            # Add details to the result for the presenter
            result["details"] = {
                "database_updated": True,
                "added_count": import_result.added_count,
                "duplicate_count": import_result.duplicate_count,
            }

        return result
    except Exception as e:
        log(f"Error updating database: {str(e)}", level="error")
        return result


def back_up_originals(
    filepaths_list: List[str], save_dir: Optional[str] = None
) -> Tuple[bool, Union[str, Dict[str, Any]]]:
    """Backup original files.

    Args:
        filepaths_list: List of file paths to back up
        save_dir: Optional directory to save backups

    Returns:
        Tuple of (success boolean, message)
    """
    if not filepaths_list:
        return False, "No files to backup"

    try:
        # Get backup directory
        if save_dir:
            backup_dir = os.path.join(save_dir, "originals")
        else:
            backup_dir = ud_config.get_path(UpdateDataKeys.Paths.BACKUP)

        # Create backup directory if it doesn't exist
        os.makedirs(backup_dir, exist_ok=True)

        import hashlib

        # Track statistics and file lists
        backed_up = 0
        skipped = 0
        backed_up_files = []
        skipped_files = []

        for filepath in filepaths_list:
            filename = os.path.basename(filepath)
            backup_path = os.path.join(backup_dir, filename)

            # Check if file already exists in backup directory
            if os.path.exists(backup_path):
                # Calculate hash of source file
                with open(filepath, "rb") as f:
                    source_hash = hashlib.md5(f.read()).hexdigest()

                # Calculate hash of existing backup file
                with open(backup_path, "rb") as f:
                    backup_hash = hashlib.md5(f.read()).hexdigest()

                # If files are identical, skip backup
                if source_hash == backup_hash:
                    log(
                        f"Skipping backup of {filename} - identical file already exists",
                        level="debug",
                    )
                    skipped += 1
                    skipped_files.append(filepath)
                    continue
                else:
                    # Files are different but have same name - create a new filename with incremental number
                    file_root, file_ext = os.path.splitext(filename)
                    counter = 1
                    while os.path.exists(backup_path):
                        new_filename = f"{file_root}_{counter}{file_ext}"
                        backup_path = os.path.join(backup_dir, new_filename)
                        counter += 1
                    log(
                        f"Files differ - saving as {os.path.basename(backup_path)}",
                        level="debug",
                    )

            # Copy file to backup location
            with open(filepath, "rb") as src, open(backup_path, "wb") as dst:
                dst.write(src.read())
            backed_up += 1
            backed_up_files.append(filepath)

        # Return structured stats for better reporting
        backup_stats = {
            "message": f"Backed up {backed_up} files to {backup_dir}, skipped {skipped} identical files",
            "backed_up_count": backed_up,
            "skipped_count": skipped,
            "backup_dir": backup_dir,
            "backed_up_files": backed_up_files,
            "skipped_files": skipped_files,
        }
        return True, backup_stats
    except Exception as e:
        return False, f"Backup failed: {str(e)}"


def move_to_unrecognised(filepath: str) -> bool:
    """Move unrecognised file to unrecognised folder.

    Args:
        filepath: Path to the file to move

    Returns:
        Boolean indicating success or failure
    """
    try:
        unrecognised_dir = ud_config.get_path(UpdateDataKeys.Paths.UNRECOGNISED)
        os.makedirs(unrecognised_dir, exist_ok=True)

        filename = os.path.basename(filepath)
        new_path = os.path.join(unrecognised_dir, filename)

        # If file exists, add timestamp
        if os.path.exists(new_path):
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            name, ext = os.path.splitext(filename)
            new_path = os.path.join(unrecognised_dir, f"{name}_{timestamp}{ext}")

        os.rename(filepath, new_path)
        log(f"Moved unrecognised file to: {new_path}", level="info")
        return True
    except Exception as e:
        log(f"Failed to move file to unrecognised: {e}", level="warning")
        return False


######### Error Handling #########
# ? many of these are basically dummy classes, purpose?


class PipelineError(Exception):
    """Base exception for all pipeline-related errors.

    This is the parent class for all custom pipeline errors. It adds:
    1. A details dictionary to store additional error context
    2. Automatic error printing for non-GUI usage
    3. Common error handling patterns for all pipeline errors

    Args:
        message (str): Main error message
        details (dict, optional): Additional error context like file paths or data state
    """

    def __init__(self, message, details=None):
        super().__init__(message)
        self.details = details or {}
        # Always print error for non-GUI usage
        print(f"Pipeline Error: {message}")
        if details:
            print("Details:", details)


class FileLoadError(PipelineError):
    """Raised when there's an error loading or validating CSV files.

    Used in load_csv_file() when:
    1. A CSV file cannot be opened or read
    2. The CSV format doesn't match expected bank format
    3. Required columns are missing
    """


class DataProcessingError(PipelineError):
    """Raised when there's an error processing the data.

    Used in process_with_handler() when:
    1. Data type conversion fails
    2. Required data transformations fail
    3. Data validation checks fail after processing
    """


class FileBackupError(PipelineError):
    """Raised when there's an error backing up original files.

    Used in back_up_originals() when:
    1. Backup directory cannot be created
    2. Files cannot be copied/moved to backup
    3. Source files cannot be cleaned up after backup
    """


class FileSaveError(PipelineError):
    """Raised when there's an error saving the output file.

    Used in save_master_file() when:
    1. Output directory is not writable
    2. Master CSV file cannot be created
    3. User cancels the save operation in file dialog
    """


if __name__ == "__main__":
    pass
