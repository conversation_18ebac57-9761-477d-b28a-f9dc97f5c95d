# Column System Architecture Options

## Current Implementation Overview

The current implementation uses an Enum-based approach with rich metadata for column definitions:

```python
class StandardColumns(Enum):
    COLUMN_NAME = ColumnMetadata(
        display_name="Display Name",
        used_in=["source", "display", "categorize"],
        editable=False,
        width=12,
        description="Description"
    )
```

## Identified Column Categories

1. **Bank Standard Columns**
   - Directly from bank statements (DATE, AMOUNT, DESCRIPTION)
   - Immutable after import
   - Core transaction data

2. **System-Generated Columns**
   - Created during import (e.g., IMPORT_HASH, DUPLICATE_FLAG)
   - Used for internal processing
   - Not directly editable by users

3. **User-Defined Columns**
   - Added by users (CATEGORY, TAGS, NOTES)
   - Fully editable
   - May have validation rules

4. **Internal System Columns**
   - Database IDs
   - Audit fields (created_at, updated_at)
   - Not shown in UI by default

## Proposed Architecture Options

### Option 1: Category-Based Enum Groups

```python
class ColumnCategory(Enum):
    BANK = "bank"
    SYSTEM = "system"
    USER = "user"
    INTERNAL = "internal"

class ColumnMetadata:
    def __init__(self, display_name: str, category: ColumnCategory, **kwargs):
        self.category = category
        # ... other metadata
```

**Pros**:
- Clear separation of concerns
- Easy to filter columns by category
- Backward compatible

**Cons**:
- Still a single enum class
- May become large

### Option 2: Class Hierarchy

```python
class BaseColumn:
    def __init__(self, display_name: str, **kwargs):
        self.display_name = display_name

class BankColumn(BaseColumn):
    def __init__(self, bank_specific_metadata=None, **kwargs):
        super().__init__(**kwargs)
        self.type = "bank"

class UserColumn(BaseColumn):
    def __init__(self, validation_rules=None, **kwargs):
        super().__init__(**kwargs)
        self.type = "user"
```

**Pros**:
- Type safety
- Different behaviors per column type
- Extensible

**Cons**:
- More complex
- Breaking change from current implementation

### Option 3: Registry Pattern

```python
class ColumnRegistry:
    _columns = {}
    
    @classmethod
    def register(cls, name, category, **metadata):
        cls._columns[name] = {"category": category, **metadata}
        
    @classmethod
    def get_columns(cls, category=None):
        if category:
            return {k: v for k, v in cls._columns.items() if v["category"] == category}
        return cls._columns
```

**Pros**:
- Dynamic column registration
- Runtime flexibility
- Easy to modify at runtime

**Cons**:
- Less IDE support
- No type checking

## Recommendation

Based on the current codebase and requirements, I recommend **Option 1 (Category-Based Enum Groups)** for these reasons:

1. **Gradual Migration**: Can be implemented incrementally
2. **Type Safety**: Maintains strong typing
3. **Discoverability**: Easy to find all columns in one place
4. **UI Integration**: Simple to implement in PySide tables

## Migration Steps

1. Add `category` field to `ColumnMetadata`
2. Categorize existing columns
3. Update UI code to handle categories
4. Add validation based on categories

## Example Implementation

```python
class ColumnCategory(Enum):
    BANK = "bank"
    SYSTEM = "system"
    USER = "user"
    INTERNAL = "internal"

@dataclass
class ColumnMetadata:
    display_name: str
    category: ColumnCategory
    used_in: List[str]
    editable: bool = False
    width: Optional[int] = None
    description: Optional[str] = None

class StandardColumns(Enum):
    # Bank columns
    DATE = ColumnMetadata(
        "Date",
        category=ColumnCategory.BANK,
        used_in=["source", "display"]
    )
    
    # System columns
    IMPORT_HASH = ColumnMetadata(
        "Import Hash",
        category=ColumnCategory.SYSTEM,
        used_in=["system"]
    )
    
    # User columns
    CATEGORY = ColumnMetadata(
        "Category",
        category=ColumnCategory.USER,
        used_in=["display", "categorize"],
        editable=True
    )
    
    # Internal columns
    DB_ID = ColumnMetadata(
        "ID",
        category=ColumnCategory.INTERNAL,
        used_in=["system"]
    )
```

## Next Steps

1. Review and refine categories
2. Plan UI changes for column management
3. Implement migration script
4. Update documentation
