03:26 AM, 19 Jun 2025

okay run n debug time ... 

Run notes:
Runs   PASS
categorize loads PASS ( I swear I'm going to call this module trans_cat)
Details column has lost default column width - X
Columns resizable and re orderable 
Default columns  displayed, all columns available check (more visual feedback might be good, higlight projected position divider between colums - not a deal breaker )
Filter details column 
- not live filtering - ...
When hit apply all transactions disapear....
When delete and apply all columns reappear....
type nbc ... apply .. all disapear
reapear on delete and apply ..
-enter known good dearch term 
no results 
Try another term "DIrect"
no results
Date filter in left panel appears to work, unclear whetehr re querying database or just filtering full records .>
Currently displaying 3 years of transactions and scrolling top to bottom effortlessly - good
highlights entire row.. good 
Text in higlighted row goes dark when click out of window .... probably not so good ..

Filter transactions by date widget  in left panel is clunky looking and takes u a lot of realestate.. shoul dbe collapsible 
possibly be transparent 
BUt we should prioritse these and add them to a task list... funcitonlaity first, aesthetics last ...


03:35 AM, 19 Jun 2025
left panel should be collapsible 


03:55 AM, 19 Jun 2025
The label for FIlter should probably be Search:
There should be an All collums at the top of the list 
then a divider line 
the combo box is quite wide this is because of OP ABNK ACCOUNT NUMBER 
Which should be shortand to OP Account in the fm_std_cols
It does occur to me that most of these are just the db_names capitalised and sans underscores ..but the flexibility is good ..
If we have a live connonical name for the display_names created at point of interpretation ( utils helper) then this would never be an issue ..IS this how it works.. should this be decclared by presenter ?

Okay - live search test - everything disapears as soon as any letter is typed 
( note should be case insensitive by default this could be configurable in a settings pane) 

Thats all  I have for now ...








