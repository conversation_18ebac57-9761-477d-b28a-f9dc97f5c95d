# ASB Bank CSV Metadata Parsing

## Metadata Structure

The ASB bank CSV exports contain metadata in the first few lines before the actual transaction data:

```
Created date / time : 25 March 2024 / 23:12:57
Bank 12; Branch 3053; Account 0478706-50 (Streamline)
From date ********
To date ********
Avail Bal : 9.47 as of ********
Ledger Balance : 110.97 as of ********
```

## Parsing Considerations

### Metadata Fields
- **Created Datetime**: First line, format "DD Month YYYY / HH:MM:SS"
- **Bank Details**: 
  - Bank number
  - Branch number
  - Account number
  - Account type
- **Date Range**:
  - From date
  - To date
- **Balance Information**:
  - Available Balance
  - Ledger Balance
  - Balance Date

### Parsing Strategy
- Requires line-by-line parsing
- Uses regex for extracting specific details
- Converts dates and balances to appropriate types

## Example Parsing Pseudocode

```python
def parse_asb_metadata(metadata_lines):
    metadata = {
        'created_datetime': parse_datetime(lines[0]),
        'bank_number': extract_bank_number(lines[1]),
        'branch_number': extract_branch_number(lines[1]),
        'account_number': extract_account_number(lines[1]),
        'account_type': extract_account_type(lines[1]),
        'from_date': parse_date(lines[2]),
        'to_date': parse_date(lines[3]),
        'available_balance': extract_balance(lines[4]),
        'ledger_balance': extract_balance(lines[5])
    }
    return metadata
```

## Potential Parsing Challenges
- Inconsistent formatting
- Variations in metadata structure
- Different date/time formats

## Recommended Parsing Approach
1. Create bank-specific parser
2. Handle potential parsing errors
3. Provide fallback or default values
4. Log any parsing inconsistencies
