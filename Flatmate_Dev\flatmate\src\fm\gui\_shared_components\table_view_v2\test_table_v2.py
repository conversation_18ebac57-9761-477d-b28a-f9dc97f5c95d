"""
Test script for CustomTableView_v2

This script tests the new table view implementation to verify:
- Configuration works correctly
- Dynamic methods work
- No competing override chains
- Auto-sizing with limits works
- Method chaining works
"""

import sys
import pandas as pd
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout

# Add the path to import the new table view
sys.path.append(r'C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src')

from fm.gui._shared_components.table_view_v2.fm_table_view import CustomTableView_v2


class TestWindow(QMainWindow):
    """Test window for CustomTableView_v2."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("CustomTableView_v2 Test")
        self.setGeometry(100, 100, 1000, 600)
        
        # Create test data
        self.test_data = pd.DataFrame({
            'date': ['2024-01-01', '2024-01-02', '2024-01-03'],
            'details': ['Short payment', 'Very long transaction description that should test the column width limits', 'Medium length transaction'],
            'amount': [100.50, -250.75, 75.25],
            'account': ['Checking', 'Savings', 'Checking'],
            'balance': [1000.50, 749.75, 825.00],
            'tags': ['food', 'rent', 'gas']
        })
        
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the test UI."""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Test buttons
        button_layout = QHBoxLayout()
        
        btn_basic = QPushButton("Test Basic Usage")
        btn_basic.clicked.connect(self.test_basic_usage)
        button_layout.addWidget(btn_basic)
        
        btn_configured = QPushButton("Test Configured Usage")
        btn_configured.clicked.connect(self.test_configured_usage)
        button_layout.addWidget(btn_configured)
        
        btn_dynamic = QPushButton("Test Dynamic Changes")
        btn_dynamic.clicked.connect(self.test_dynamic_changes)
        button_layout.addWidget(btn_dynamic)
        
        btn_chaining = QPushButton("Test Method Chaining")
        btn_chaining.clicked.connect(self.test_method_chaining)
        button_layout.addWidget(btn_chaining)
        
        layout.addLayout(button_layout)
        
        # Table container
        self.table_container = QWidget()
        self.table_layout = QVBoxLayout(self.table_container)
        layout.addWidget(self.table_container)
        
    def clear_table(self):
        """Clear the current table."""
        for i in reversed(range(self.table_layout.count())):
            child = self.table_layout.takeAt(i).widget()
            if child:
                child.deleteLater()
    
    def test_basic_usage(self):
        """Test basic usage - just works out of the box."""
        print("Testing basic usage...")
        self.clear_table()
        
        # Basic usage
        table = CustomTableView_v2()
        table.set_dataframe(self.test_data).show()
        
        self.table_layout.addWidget(table)
        print("✓ Basic usage test complete")
    
    def test_configured_usage(self):
        """Test configured usage with specific options."""
        print("Testing configured usage...")
        self.clear_table()
        
        # Configured usage
        table = CustomTableView_v2()
        table.configure(
            auto_size_columns=True,
            max_column_width=40,
            editable_columns=['tags'],
            default_visible_columns=['date', 'details', 'amount', 'tags'],
            show_toolbar=True,
            column_widths={'date': 12, 'amount': 15}
        ).set_dataframe(self.test_data).show()
        
        self.table_layout.addWidget(table)
        print("✓ Configured usage test complete")
    
    def test_dynamic_changes(self):
        """Test dynamic runtime changes."""
        print("Testing dynamic changes...")
        self.clear_table()
        
        # Create table
        table = CustomTableView_v2()
        table.configure(auto_size_columns=True, max_column_width=40)
        table.set_dataframe(self.test_data).show()
        
        # Test dynamic changes
        table.hide_columns(['balance', 'account'])
        table.resize_column('details', 60)
        table.show_columns(['tags'])
        
        self.table_layout.addWidget(table)
        print("✓ Dynamic changes test complete")
    
    def test_method_chaining(self):
        """Test method chaining works correctly."""
        print("Testing method chaining...")
        self.clear_table()
        
        # Method chaining
        table = (CustomTableView_v2()
                .configure(
                    auto_size_columns=True,
                    max_column_width=40,
                    editable_columns=['tags'],
                    show_toolbar=True
                )
                .set_dataframe(self.test_data)
                .hide_columns(['balance'])
                .resize_column('details', 50)
                .show())
        
        self.table_layout.addWidget(table)
        print("✓ Method chaining test complete")


def main():
    """Run the test application."""
    app = QApplication(sys.argv)
    
    window = TestWindow()
    window.show()
    
    print("CustomTableView_v2 Test Application")
    print("Click the buttons to test different usage patterns")
    print("Check console for test results")
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
