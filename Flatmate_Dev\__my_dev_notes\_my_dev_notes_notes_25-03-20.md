# FlatMate Development Notes

## Task Status Legend
- [*] = Todo
- [x] = Completed
- [?] = Needs Assessment/Refinement

---

## Current Architecture Concerns

### Component Organization
- **InfoBar Implementation** [*]
  - Not yet implemented in main window
  - May have indirect implementation in ud_data
  - Should be in center panel (main_window) with simple show/hide methods for module presenters

- **Icon Color and SVG Management** [?]
  - Current implementation uses custom renderer in nav_pane (complex)
  - Should be centralized for all components
  - Recommendation: Create a unified IconRenderer in `gui/icons/` using "calm white" for text and outline icons in dark mode

- **Navigation Components** [x]
  - Nav_pane should contain module icons (home, profiles, import_data, view_data)
  - Should appear vertically stacked in right panel in compact mode
  - Consider renaming to nav_bar for clarity

### File Structure Improvements

- **Icon Organization** [?]
  - Current location: `flatmate/src/fm/gui/icons/` (structure exists but no icons)
  - Recommended structure:
    ```
    icons/
    ├── nav_pane/
    │   ├── home/
    │   │   ├── home.svg           # Selected icon
    │   │   └── candidates/        # Alternative options
    │   ├── profiles/
    │   ├── import_data/
    │   └── view_data/
    └── settings_pane/
        └── [similar structure]
    ```

- **Module File Structure** [?]
  - Review purpose of:
    - `fm/modules/_base_module/_view/right_panel/widgets/nav_pane`
    - `fm/modules/_base_module/_view/right_panel/widgets/nav_pane.py`

---

## Implementation Tasks

### Right Panel Functionality [*]
1. Create settings icon in its own pane below nav_pane
2. When clicked, expand right panel to show full-height settings pane
3. Settings pane should contain module-specific settings
4. Dev settings should only show when dev_mode is True in core/config

### Widget Hierarchy Terminology
- **Groups**: Collections of related widgets (labels, menus, buttons)
- **Panes**: Containers of groups
- **Panels**: Major window areas containing multiple panes

### Data View Widget Refactoring [*]
- Move from `fm/modules/ud_data/_view/center_panel` to center panel widgets folder
- Ensures easier discovery and modification

### Logging System Assessment [?]
- Review current implementation
- Add ability to toggle reporting for different components
- Add option to show/hide debug events in terminal
- Make configurable through dev settings panel

---

## UI Design Considerations

### Panel States [x]
- Right panel should support multiple states:
  - Hidden: Not visible
  - Compact: Icon sidebar only
  - Expanded: Full panel with settings

### Dev Mode Settings Panel [*]
- Implement checkboxes for:
  - Time-stamped merged CSVs
  - Tidy up originals
  - Create DB output in destination folder
  - Update previous merged CSV master file
  - Show all GUI element tooltips
  - Production mode tooltips

### Navigation Options [x]
- Initial right panel state as sidebar with module icons
- Settings icon expands to reveal options
- Consider small gear icon under options pane in left panel