import os
from typing import TYPE_CHECKING, Optional

if TYPE_CHECKING:
    from ._base_statement_handler import StatementHandler
else:
    # Import for runtime
    from ._base_statement_handler import StatementHandler

from .asb_standard_csv_handler import ASBStandardCSVHandler
from .coop_standard_csv_handler import <PERSON>opStandardCSVHandler
from .kiwibank_basic_csv_handler import KiwibankBasicCSVHandler
from .kiwibank_full_csv_handler import KiwibankFullCSVHandler

# from .test_csv_handler import TestCSVHandler

# List of handler classes for registration
STATEMENT_HANDLERS = [
    # Put TestCSVHandler first so it's checked before other handlers
    <PERSON>wi<PERSON><PERSON>asi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    KiwibankFullCSVHandler,
    CoopStandardCSVHandler,
    ASBStandardCSVHandler,
]

# Create all handlers at module level for singleton usage
_handlers = [handler() for handler in STATEMENT_HANDLERS]


def get_handler(filepath: str) -> Optional["StatementHandler"]:
    """Get the first handler that can handle the file."""
    # Import logging here to avoid circular imports
    from fm.core.services.logger import log

    filename = os.path.basename(filepath)
    log.debug(f"[handler_registry] Trying to find handler for file: {filename}")

    for handler_instance in _handlers:
        handler_name = handler_instance.__class__.__name__
        log.debug(f"[handler_registry] Trying handler: {handler_name}")
        if handler_instance.__class__.can_handle_file(filepath):
            log.debug(f"[handler_registry] Found matching handler: {handler_name}")
            return handler_instance
        log.debug(f"[handler_registry] Handler {handler_name} did not match")

    log.debug(f"[handler_registry] No matching handler found for file: {filename}")
    return None
