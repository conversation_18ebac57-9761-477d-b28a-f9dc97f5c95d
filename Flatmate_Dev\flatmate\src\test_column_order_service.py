#!/usr/bin/env python3
"""
Test script for ColumnOrderService functionality.
"""

import tempfile
from pathlib import Path

from fm.core.data_services.standards.column_order_service import ColumnOrderService

def test_column_order_service():
    """Test the ColumnOrderService functionality."""
    print("=== ColumnOrderService Test ===\n")
    
    # Use a temporary file for testing
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        temp_prefs_path = Path(f.name)
    
    try:
        # Create service with temp preferences file
        service = ColumnOrderService(preferences_path=temp_prefs_path)
        
        # Test 1: Default order
        print("1. Testing default order:")
        default_order = service.get_default_order()
        print(f"   Default order (first 5): {default_order[:5]}")
        print(f"   Total columns: {len(default_order)}")
        print()
        
        # Test 2: Get order with no preferences (should return default)
        print("2. Testing get_column_order with no preferences:")
        categorize_order = service.get_column_order('categorize')
        print(f"   Categorize order (first 5): {categorize_order[:5]}")
        print(f"   Same as default: {categorize_order == default_order}")
        print()
        
        # Test 3: Set module-specific preference
        print("3. Testing set_column_order for module:")
        custom_order = ['category', 'tags', 'date', 'details', 'amount']
        service.set_column_order(custom_order, 'categorize')
        
        new_categorize_order = service.get_column_order('categorize')
        print(f"   Custom categorize order: {new_categorize_order}")
        print(f"   Preference saved: {new_categorize_order == custom_order}")
        print()
        
        # Test 4: Check that other modules still use default
        print("4. Testing other modules still use default:")
        update_data_order = service.get_column_order('update_data')
        print(f"   Update_data order (first 5): {update_data_order[:5]}")
        print(f"   Same as default: {update_data_order == default_order}")
        print()
        
        # Test 5: Set global preference
        print("5. Testing global preference:")
        global_custom_order = ['amount', 'date', 'details', 'account']
        service.set_column_order(global_custom_order)  # No module = global
        
        # New module should use global preference
        new_module_order = service.get_column_order('new_module')
        print(f"   New module order: {new_module_order}")
        print(f"   Uses global preference: {new_module_order == global_custom_order}")
        
        # Categorize should still use its specific preference
        categorize_order_after_global = service.get_column_order('categorize')
        print(f"   Categorize still custom: {categorize_order_after_global == custom_order}")
        print()
        
        # Test 6: Test sorting functionality
        print("6. Testing sort_columns_by_order:")
        unsorted_cols = ['notes', 'date', 'category', 'amount']
        sorted_cols = service.sort_columns_by_order(unsorted_cols, 'categorize')
        print(f"   Unsorted: {unsorted_cols}")
        print(f"   Sorted:   {sorted_cols}")
        print()
        
        # Test 7: Test preference checking
        print("7. Testing preference checking:")
        print(f"   Has categorize preference: {service.has_user_preference('categorize')}")
        print(f"   Has global preference: {service.has_user_preference()}")
        print(f"   Has unknown module preference: {service.has_user_preference('unknown')}")
        print()
        
        # Test 8: Test reset functionality
        print("8. Testing reset to default:")
        service.reset_to_default('categorize')
        reset_order = service.get_column_order('categorize')
        print(f"   After reset, uses global: {reset_order == global_custom_order}")
        
        service.reset_to_default()  # Reset global too
        final_order = service.get_column_order('categorize')
        print(f"   After global reset, uses default: {final_order == default_order}")
        print()
        
        print("=== All tests passed! ColumnOrderService is working correctly ===")
        
    finally:
        # Clean up temp file
        if temp_prefs_path.exists():
            temp_prefs_path.unlink()

if __name__ == "__main__":
    test_column_order_service()
