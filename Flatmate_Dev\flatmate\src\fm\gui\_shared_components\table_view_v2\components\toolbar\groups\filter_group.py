"""
Filter Group

Combines filter-related components into a cohesive group.
Contains all the component classes it uses for better cohesion.
"""

from PySide6.QtCore import Signal
from PySide6.QtWidgets import QWidget, QHBoxLayout, QLabel, QComboBox, QLineEdit, QPushButton


class ColumnSelector(QComboBox):
    """Dropdown for selecting which column to filter."""

    column_changed = Signal(object)  # Emits column index or "all_columns" when selection changes

    def __init__(self, parent=None):
        """Initialize the column selector."""
        super().__init__(parent)
        self.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)

        # Connect signal
        self.currentIndexChanged.connect(self._on_selection_changed)

    def _on_selection_changed(self, _index):
        """Handle selection changes."""
        column_data = self.currentData()
        print(f"DEBUG ColumnSelector._on_selection_changed: column_data='{column_data}'")
        if column_data is not None:
            # Safety check for signal existence (in case of reload issues)
            if hasattr(self, 'column_changed'):
                self.column_changed.emit(column_data)
            else:
                print("WARNING: column_changed signal not found!")

    def set_columns(self, columns, column_names=None):
        """Set available columns.

        Args:
            columns: List of column identifiers
            column_names: Optional dict mapping column IDs to display names
        """
        print(f"DEBUG ColumnSelector.set_columns: columns={columns}")
        print(f"DEBUG ColumnSelector.set_columns: column_names={column_names}")

        self.clear()

        # Add "All Columns" option at the top
        self.addItem("All Columns", "all_columns")

        # Add separator (visual divider)
        self.insertSeparator(1)

        for i, col in enumerate(columns):
            display_name = column_names.get(col, col) if column_names else col
            print(f"DEBUG: Adding item '{display_name}' with data '{col}'")
            self.addItem(str(display_name), col)  # Store column name, not index

        # Set default to 'details' if available, otherwise "All Columns"
        details_index = self.findData('details')
        print(f"DEBUG: Looking for 'details', found at index: {details_index}")
        if details_index >= 0:
            self.setCurrentIndex(details_index)
            print(f"DEBUG: Set current index to {details_index} (details)")
        else:
            self.setCurrentIndex(0)  # Default to "All Columns"
            print(f"DEBUG: Set current index to 0 (All Columns)")

    def get_selected_column(self):
        """Get the currently selected column index."""
        return self.currentData()

    def set_selected_column(self, column_name: str):
        """Set the selected column by name.

        Args:
            column_name: Name of the column to select (e.g., "details", "all_columns")
        """
        index = self.findData(column_name)
        if index >= 0:
            self.setCurrentIndex(index)
            print(f"DEBUG: Set column selector to '{column_name}' at index {index}")
        else:
            print(f"WARNING: Column '{column_name}' not found in selector")


class FilterInput(QLineEdit):
    """Text input field for entering filter patterns with live filtering."""

    filter_changed = Signal(str)  # Emits filter text as user types (live filtering)
    filter_requested = Signal(str)  # Emits filter text when Enter is pressed (legacy)

    def __init__(self, parent=None):
        """Initialize the filter input."""
        super().__init__(parent)
        self.setPlaceholderText("Search transactions... (e.g. coffee|tea, -refund)")

        # Connect signals for live filtering
        self.textChanged.connect(self._on_text_changed)
        self.returnPressed.connect(self._on_return_pressed)

    def _on_text_changed(self, text):
        """Handle text change for live filtering."""
        self.filter_changed.emit(text)

    def _on_return_pressed(self):
        """Handle Enter key press (legacy support)."""
        self.filter_requested.emit(self.text())

    def get_filter_text(self):
        """Get the current filter text."""
        return self.text()

    def clear_filter(self):
        """Clear the filter input."""
        self.clear()


class ApplyButton(QPushButton):
    """Button for applying filters."""

    apply_requested = Signal()  # Emitted when button is clicked

    def __init__(self, text="Apply", parent=None):
        """Initialize the apply button."""
        super().__init__(text, parent)
        self.clicked.connect(self.apply_requested)


class ClearButton(QPushButton):
    """Button for clearing filters."""

    clear_requested = Signal()  # Emitted when button is clicked

    def __init__(self, text="Clear", parent=None):
        """Initialize the clear button."""
        super().__init__(text, parent)
        self.clicked.connect(self.clear_requested)


class FilterGroup(QWidget):
    """Group of filter controls working together."""

    # Signals for external communication
    filter_applied = Signal(object, str)  # column (int or str), pattern
    filters_cleared = Signal()

    def __init__(self, parent=None, live_filtering=True):
        """Initialize the filter group.

        Args:
            parent: Parent widget
            live_filtering: If True, filters as user types. If False, requires Apply button.
        """
        super().__init__(parent)
        self.live_filtering = live_filtering

        # Set CSS class for styling
        self.setObjectName("FilterGroup")

        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(4)  # Reduced from 5px to 4px for tighter spacing

        # Search label (changed from "Filter:" for better UX)
        self.filter_label = QLabel("Search:")
        layout.addWidget(self.filter_label)

        # Column selector
        self.column_selector = ColumnSelector()
        layout.addWidget(self.column_selector)

        # Filter input
        self.filter_input = FilterInput()
        layout.addWidget(self.filter_input)

        # Apply button (only show if not using live filtering)
        if not self.live_filtering:
            self.apply_button = ApplyButton()
            layout.addWidget(self.apply_button)
        else:
            self.apply_button = None

        # Clear button
        self.clear_button = ClearButton()
        layout.addWidget(self.clear_button)
    
    def _connect_signals(self):
        """Connect internal signals."""
        if self.live_filtering:
            # Live filtering: apply filter as user types
            self.filter_input.filter_changed.connect(self._apply_filter_live)
            # Also support Enter key for immediate application
            self.filter_input.filter_requested.connect(self._apply_filter)
        else:
            # Traditional filtering: apply filter when button clicked or Enter pressed
            if self.apply_button:
                self.apply_button.apply_requested.connect(self._apply_filter)
            self.filter_input.filter_requested.connect(self._apply_filter)

        # Reapply filter when column selection changes (for live filtering)
        if hasattr(self.column_selector, 'column_changed'):
            self.column_selector.column_changed.connect(self._on_column_changed)

        # Clear filters when clear button clicked
        self.clear_button.clear_requested.connect(self._clear_filters)
    # todo this funciton should be dprctd with new live search function
    def _apply_filter(self):
        """Apply the current filter (traditional method)."""
        column = self.column_selector.get_selected_column()
        pattern = self.filter_input.get_filter_text()
        print(f"DEBUG _apply_filter: column='{column}', pattern='{pattern}'")
      
        if column is not None:
            self.filter_applied.emit(column, pattern)

    def _apply_filter_live(self, pattern):
        """Apply filter as user types (live filtering)."""
        column = self.column_selector.get_selected_column()
        print(f"DEBUG _apply_filter_live: column='{column}', pattern='{pattern}'")

        if column is not None:
            self.filter_applied.emit(column, pattern)

    def _on_column_changed(self, column):
        """Handle column selection change - reapply current filter to new column."""
        if self.live_filtering:
            current_pattern = self.filter_input.get_filter_text()
            if current_pattern:  # Only reapply if there's a pattern
                print(f"DEBUG _on_column_changed: Reapplying pattern '{current_pattern}' to column '{column}'")
                self.filter_applied.emit(column, current_pattern)

    def _clear_filters(self):
        """Clear all filters."""
        self.filter_input.clear_filter()
        self.filters_cleared.emit()
    
    def set_columns(self, columns, column_names=None):
        """Set available columns for filtering.

        Args:
            columns: List of column identifiers
            column_names: Optional dict mapping column IDs to display names
        """
        self.column_selector.set_columns(columns, column_names)

    def set_filter_state(self, column: str, pattern: str):
        """Set filter state externally (for persistence restore).

        Args:
            column: Column name to select
            pattern: Filter pattern to set
        """
        print(f"DEBUG: FilterGroup.set_filter_state called with column='{column}', pattern='{pattern}'")

        # Set column selector
        if hasattr(self.column_selector, 'set_selected_column'):
            self.column_selector.set_selected_column(column)
        else:
            print("WARNING: ColumnSelector doesn't have set_selected_column method")

        # Set filter text without triggering change signals
        self.filter_input.blockSignals(True)
        self.filter_input.setText(pattern)
        self.filter_input.blockSignals(False)

        # Manually emit the filter_applied signal to ensure the filter is actually applied
        if pattern:  # Only emit if there's a pattern to apply
            print(f"DEBUG: Emitting filter_applied signal for restoration")
            self.filter_applied.emit(column, pattern)

    def get_filter_state(self) -> tuple[str, str]:
        """Get current filter state.

        Returns:
            Tuple of (column, pattern)
        """
        column = self.column_selector.get_selected_column() if hasattr(self.column_selector, 'get_selected_column') else "details"
        pattern = self.filter_input.text()
        return column, pattern
