2025-06-28 @ 00:31:02

# Audit Database Logic 

- I renamed the aold database and am using a run and debug process to ausid the module to db_logic.
- The currently desired pattern is not to have modules access db logic directly or implement their own formatting where possible, and use a service layer db_io_service in core.database_services

----------------------

NOTES:

#dw_director : 
 has unused imports, reffering to fm.dataservice 
to do with converting csv to transaction
and getting import result
import result could be renamed db_update_result, appears unused.
 - ## note overly elaborate and complex, unused imports, needs a clean up
#pipline had similar, used in update_database function

