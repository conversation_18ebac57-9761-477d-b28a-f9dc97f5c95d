# FlatMate Configuration System

## Overview
The FlatMate configuration system is designed to provide centralized, flexible, and easy-to-use configuration management for the application.

## Core Components
- `config_manager.py`: Central configuration management
- `path_manager.py`: Application path discovery and management
- `settings_manager.py`: Application settings management
- [master_file_tracker.py](cci:7://file:///Users/<USER>/DEV/a_CODING_PROJECTS/a_flatmate_App_DEV/flatmate/src/flatmate/config/master_file_tracker.py:0:0-0:0): Master file location tracking
- `config_loader.py`: Configuration loading and environment management

## Usage

### Accessing Configuration

```python
from flatmate.config.config_manager import config

# Get a configuration value
font_size = config.get('font_size', default=12)

# Set a configuration value
config.set('font_size', 14)
```

### Master File Tracking

```python
# Update master file location
config.update_master_location('/path/to/master/file.csv')

# Get current master file location
master_info = config.get_master_location()

# Check if master file exists
if config.master_exists():
    master_path = config.get_master_path()
```

### Environment Mode Checking

```python
# Check application mode
if config.is_development_mode():
    print("Running in development mode")

if config.is_production_mode():
    print("Running in production mode")
```

## Configuration Principles
1. Singleton Pattern: Only one configuration instance exists
2. Modular Design: Separate modules for different configuration aspects
3. Flexible Access: Easy retrieval and modification of settings
4. Environment-Aware: Supports different configuration modes

## Best Practices
- Always use `config.get()` with a default value
- Avoid hardcoding configuration values
- Use environment-specific configurations
- Leverage the centralized configuration system