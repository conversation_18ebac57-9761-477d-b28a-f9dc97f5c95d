"""
<PERSON><PERSON>t to check for import errors in the update_data module widgets.
"""
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Try importing all the modules
try:
    from fm.modules.update_data._view.ud_view_widgets._base_panel_component import BasePanelComponent
    print("✓ Successfully imported BasePanelComponent")
    
    from fm.modules.update_data._view.ud_view_widgets.btns_center_panel import PanelActionButton
    print("✓ Successfully imported PanelActionButton")
    
    from fm.modules.update_data._view.ud_view_widgets.composite_panel import CompositePanel
    print("✓ Successfully imported CompositePanel")
    
    from fm.modules.update_data._view.ud_view_widgets.data_display_panel import DataDisplayPanel
    print("✓ Successfully imported DataDisplayPanel")
    
    from fm.modules.update_data._view.ud_view_widgets.file_display_panel import FileDisplayPanel
    print("✓ Successfully imported FileDisplayPanel")
    
    from fm.modules.update_data._view.ud_view_widgets.file_display_widget import FileDisplayWidget
    print("✓ Successfully imported FileDisplayWidget")
    
    from fm.modules.update_data._view.ud_view_widgets.status_info import StatusInfoWidget
    print("✓ Successfully imported StatusInfoWidget")
    
    from fm.modules.update_data._view.ud_view_widgets.welcome_panel import WelcomePanel
    print("✓ Successfully imported WelcomePanel")
    
    print("\nAll imports successful!")
    
except ImportError as e:
    print(f"Import error: {e}")
