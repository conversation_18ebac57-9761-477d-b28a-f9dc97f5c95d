# FlatMate Application Test Configuration
# These settings are used during testing to ensure consistent behavior

[app]
app_name = "FlatMate"
version = "1.0.0"
debug_mode = true
environment = "testing"

[paths]
base_data_dir = "/tmp/flatmate_test_data"
temp_dir = "/tmp/flatmate_test_data/temp"
backup_dir = "/tmp/flatmate_test_data/backups"

[logging]
level = "DEBUG"
file_path = "/tmp/flatmate_test_data/logs/test_app.log"
max_file_size_mb = 5
backup_count = 3

[ui]
theme = "testing"
language = "en"
font_size = 10

[data_processing]
batch_size = 10
cache_enabled = false
cache_dir = "/tmp/flatmate_test_data/cache"

[security]
encryption_enabled = false
data_anonymization = true

[testing]
mock_data_enabled = true
test_mode = true
use_test_data = true
test_data_location = "tests/test_data"
mock_file_operations = true

[profiles]
test_profile = {
    name = "Test Profile",
    description = "Profile used for automated testing"
}

[user_profile]
active_profile = "test_profile"
