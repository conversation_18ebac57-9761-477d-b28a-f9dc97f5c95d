"""
Home state management for the FlatMate application.
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any

from ...core.config import config
from ...core.config.keys import ConfigKeys
from ...core.services.event_bus import global_event_bus, Events


class HomeState:
    """Manages state for the home module."""

    def __init__(self):
        """Initialize home state."""
        self._is_first_run = config.get_value(ConfigKeys.App.IS_FIRST_RUN, default=True)
        self._current_module = None

    @property
    def is_first_run(self):
        """Check if this is the first run of the application."""
        return self._is_first_run

    @property
    def current_module(self):
        """Get the currently active module."""
        return self._current_module

    @current_module.setter
    def current_module(self, module_name):
        """Set the currently active module."""
        self._current_module = module_name

    def mark_first_run_complete(self):
        """Mark the first run as complete."""
        self._is_first_run = False
        config.set_value(ConfigKeys.App.IS_FIRST_RUN, False)
