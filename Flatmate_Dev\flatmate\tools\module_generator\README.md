# Module Generator Tool

## Overview
This document outlines a planned tool to automate the creation of new modules in the FlatMate application. The tool will generate the standard folder structure and boilerplate code needed for a new module, following the established architectural patterns.

## Planned Features
- Generate complete module folder structure
- Create boilerplate code for presenters, views, and panel components
- Set up proper imports and signal connections
- Include documentation templates
- Register the module with the ModuleCoordinator

## Folder Structure to Generate
```
modules/new_module_name/
├── __init__.py
├── module_presenter.py
├── _view/
│   ├── __init__.py
│   ├── module_view.py
│   ├── left_panel/
│   │   ├── __init__.py
│   │   ├── left_panel.py
│   │   └── _panel_manager.py
│   ├── center_panel/
│   │   ├── __init__.py
│   │   ├── center_panel.py
│   │   └── _panel_manager.py
│   └── components/
│       └── __init__.py
├── core/
│   ├── __init__.py
│   └── processor.py
├── config/
│   ├── __init__.py
│   ├── local_config.py
│   └── defaults.toml
└── services/
    ├── __init__.py
    └── local_services.py
```

## Usage (Planned)
```bash
# From project root
python -m tools.module_generator.create_module new_module_name "New Module Display Name"
```

## Implementation Plan
1. Create a Python script that takes module name and display name as arguments
2. Define templates for each file type
3. Generate the folder structure and files
4. Update the module registry in ModuleCoordinator
5. Add the module to the MODULE_MAP in modules_and_features.py

## Benefits
- Ensures consistency across modules
- Reduces boilerplate code writing
- Minimizes errors in module setup
- Accelerates development of new features
- Enforces architectural patterns

## Future Enhancements
- Interactive mode with customization options
- Module-specific template selection
- Integration with CI/CD for testing generated modules
- GUI for module generation

## Status
⚠️ **PLANNED** - This tool is currently in the planning stage and will be implemented in the future.