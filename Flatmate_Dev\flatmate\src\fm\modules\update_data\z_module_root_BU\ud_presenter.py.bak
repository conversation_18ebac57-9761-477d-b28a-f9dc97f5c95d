#!/usr/bin/env python3
"""
Update Data presenter implementation.
Coordinates between the view and the data processing pipeline.
"""

import os
from pathlib import Path

import pandas as pd

from ...core.services.event_bus import Events, global_event_bus
from ...core.services.logger import LogLevel, log, log_this
from ._view.ud_view import UpdateDataView
from ._view.ud_view_widgets.info_widget import UpdateDataInfoWidget
from .config.ud_config import ud_config
from .services.events import UpdateDataEvents
from .ud_types import SaveOptions, SourceOptions
from .utils.dw_director import dw_director

# No need to import QFileDialog directly anymore


class UpdateDataPresenter:
    """Presenter for the Update Data module."""

    @log_this(LogLevel.INFO)
    def __init__(self, main_window):
        """Initialize the Update Data presenter.

        Args:
            main_window: The main window instance
        """
        self.view = UpdateDataView(main_window)
        self.main_window = main_window

        # State tracking
        self.selected_source = None  # Dict containing source files/folder info
        self.save_location = None  # Selected save directory path
        self.job_sheet_dict = {}  # Current job sheet dictionary

        # Create info widget
        self.info_widget = UpdateDataInfoWidget()

        self._connect_signals()

    def _connect_signals(self):
        """Connect view signals to handlers."""
        self.view.cancel_clicked.connect(lambda: self.request_transition("home"))
        self.view.source_select_requested.connect(self._handle_source_select)
        self.view.save_select_requested.connect(self._handle_save_select)
        self.view.source_option_changed.connect(self._handle_source_option_change)
        self.view.save_option_changed.connect(self._handle_save_option_change)
        self.view.process_clicked.connect(self._handle_process)
        log("Signals connected", "d")

        # Subscribe to Update Data events
        global_event_bus.subscribe(
            UpdateDataEvents.FILE_PROCESSING_STARTED.name, self._on_processing_started
        )
        global_event_bus.subscribe(
            UpdateDataEvents.FILE_PROCESSING_STATS.name, self._on_processing_stats
        )
        global_event_bus.subscribe(
            UpdateDataEvents.UNRECOGNIZED_FILES_DETECTED.name,
            self._on_unrecognized_files,
        )
        global_event_bus.subscribe(
            UpdateDataEvents.FILE_PROCESSING_COMPLETED.name,
            self._on_processing_completed,
        )

    def initialize(self):
        """Initialize the Update Data module."""
        # Set up view in main window
        self.view.setup_in_main_window(self.main_window)
        log("ud_presenter called self.view.setup_in_main_window", "d")

        # Now that the view is set up in the main window, we can add the info widget
        if (
            hasattr(self.view, "center_display")
            and self.view.center_display.layout() is not None
        ):
            self.view.center_display.layout().addWidget(self.info_widget)
        else:
            log("Cannot add info widget - center display or layout not available", "e")

        # Check for existing master file
        recent_masters = ud_config.get_value("recent_masters", default=[])
        if recent_masters:
            master_path = recent_masters[-1]  # Get most recent master
            if os.path.exists(master_path):  # Ensure path exists
                try:
                    master_df = pd.read_csv(
                        str(master_path)
                    )  # Convert Path to string for pandas
                    self.view.display_master_csv(master_df)
                    self.view.set_exit_mode()  # Set to exit mode since we're displaying existing data
                except Exception as e:
                    log(f"Error loading master file: {e}", "e")
                    self.view.display_welcome()  # Fall back to welcome screen
            else:
                self.view.display_welcome()
        else:
            self.view.display_welcome()

    def _handle_source_select(self, selection_type: str):
        """Handle source selection request."""
        if selection_type == SourceOptions.SELECT_FOLDER.value:
            folder = QFileDialog.getExistingDirectory(
                self.view,
                "Select Source Folder",
                str(ud_config.get_value("last_source_dir", default=str(Path.home()))),
            )
            if folder:
                # Get all CSV files in the folder (case insensitive)
                file_paths = []
                for file_name in os.listdir(folder):
                    if file_name.lower().endswith(".csv"):
                        full_path = os.path.join(folder, file_name)
                        file_paths.append(full_path)

                if not file_paths:
                    self.view.show_error("No CSV files found in selected folder")
                    return

                self.selected_source = {
                    "type": "folder",
                    "path": folder,
                    "file_paths": file_paths,
                }

                ud_config.set_value("last_source_dir", folder)
                self.info_widget.set_status(f"Source selected: {folder}")

                # If save location is set to same as source, update it
                if (
                    self.view.left_buttons.get_save_option()
                    == SaveOptions.SAME_AS_SOURCE.value
                ):
                    self.save_location = folder
                    self.info_widget.set_status(f"Save location: {folder}")

        else:  # SourceOptions.SELECT_FILES
            files, _ = QFileDialog.getOpenFileNames(
                self.view,
                "Select Source Files",
                str(ud_config.get_value("last_source_dir", default=str(Path.home()))),
                "CSV Files (*.csv);;All Files (*.*)",
            )
            if files:
                # Filter for CSV files (case insensitive)
                csv_files = [f for f in files if f.lower().endswith(".csv")]
                if not csv_files:
                    self.view.show_error("No CSV files selected")
                    return

                source_dir = os.path.dirname(csv_files[0])
                self.selected_source = {
                    "type": "files",
                    "path": source_dir,
                    "file_paths": csv_files,
                }

                ud_config.set_value("last_source_dir", source_dir)
                self.info_widget.set_status(f"Source selected: {source_dir}")

                # If save location is set to same as source, update it
                if (
                    self.view.left_buttons.get_save_option()
                    == SaveOptions.SAME_AS_SOURCE.value
                ):
                    self.save_location = source_dir
                    self.info_widget.set_status(f"Save location: {source_dir}")

    def _handle_save_select(self):
        """Handle save location selection."""
        folder = QFileDialog.getExistingDirectory(
            self.view,
            "Select Save Location",
            str(ud_config.get_value("last_save_dir", default=str(Path.home()))),
        )
        if folder:
            self.save_location = folder
            ud_config.set_value("last_save_dir", folder)
            self.info_widget.set_status(f"Save location: {folder}")

    def _handle_source_option_change(self, option: str):
        """Handle source option change."""
        # Reset selected source when option changes
        self.selected_source = None
        self.info_widget.clear()
        self.info_widget.set_status(f"Selected source option: {option}")

    def _handle_save_option_change(self, option: str):
        """Handle save location option change."""
        if option == SaveOptions.SAME_AS_SOURCE.value and self.selected_source:
            # If we have a selected source, use its directory
            if self.selected_source["type"] == "folder":
                self.save_location = self.selected_source["path"]
            else:  # files
                self.save_location = os.path.dirname(
                    self.selected_source["file_paths"][0]
                )
            self.info_widget.set_status(f"Save location: {self.save_location}")
        else:
            # Reset save location if not using same as source
            self.save_location = None
            self.info_widget.set_status("")

    def _handle_process(self):
        """Handle process button click."""
        if not self.selected_source:
            self.view.show_error("Please select source files first")
            return

        if not self.save_location:
            self.view.show_error("Please select a save location")
            return

        try:
            # Create job sheet for the director
            job_sheet = {
                "filepaths": self.selected_source["file_paths"],
                "save_folder": self.save_location,
            }

            # Process files using the director
            result = dw_director(job_sheet)

            if result.get("status") == "success":
                self.info_widget.set_status(
                    f"Successfully processed {len(job_sheet['filepaths'])} files"
                )
                # Update master file history
                master_path = result.get("output_path")
                if master_path:
                    recent_masters = ud_config.get_value("recent_masters", default=[])
                    if master_path not in recent_masters:
                        recent_masters.append(master_path)
                        if len(recent_masters) > 10:  # Keep only last 10
                            recent_masters.pop(0)
                        ud_config.set_value("recent_masters", recent_masters)
            else:
                error_msg = result.get("message", "Unknown error occurred")
                self.info_widget.set_status(error_msg, is_error=True)
                log(f"Processing error: {error_msg}", "e")

        except Exception as e:
            error_msg = f"Error processing files: {str(e)}"
            self.info_widget.set_status(error_msg, is_error=True)
            log(error_msg, "e")

    def request_transition(self, target_view: str):
        """Request transition to another view."""
        self.view.cleanup()
        global_event_bus.publish(Events.VIEW_CHANGE_REQUESTED, target_view)

    def cleanup(self):
        """Clean up before being replaced."""
        if self.view:
            self.view.cleanup()

    @log_this()
    def _test_log_decorator(self, x: int) -> int:
        """
        Test function to demonstrate logging decorator

        Args:
            x: Input number

        Returns:
            Squared input number
        """
        return x * x

    @log_this(LogLevel.INFO)
    def _setup_view_from_config(self):
        """Set up view based on configuration."""
        # Set default save location
        self.save_location = ud_config.get_value("master")
        self.info_widget.set_status(f"Save location: {self.save_location}")

        # Load recent masters
        recent_masters = ud_config.get_pref("recent_masters", default=[])
        if recent_masters:
            # TODO: Add recent masters to view
            pass

    def _on_processing_started(self, job_sheet):
        """Handle processing started event."""
        log(
            f"Processing started for {len(job_sheet.get('filepaths', [])) or 0} files",
            "info",
        )
        self.info_widget.set_status(
            f"Processing {len(job_sheet.get('filepaths', [])) or 0} files..."
        )

    def _on_processing_stats(self, stats):
        """Handle processing stats event."""
        log(f"Processing stats: {stats}", "debug")
        total = stats.get("total_files", 0)
        processed = stats.get("processed_files", 0)
        unrecognized = stats.get("unrecognized_files", 0)

        self.info_widget.set_progress(processed, total)

    def _on_unrecognized_files(self, unrecognized_files):
        """Handle unrecognized files event."""
        log(f"Unrecognized files detected: {unrecognized_files}", "warning")
        for file_info in unrecognized_files:
            self.info_widget.set_error(
                f"Unrecognized file: {file_info['filepath']}\n"
                f"Reason: {file_info.get('reason', 'Unknown')}"
            )

    def _on_processing_completed(self, result):
        """Handle processing completed event."""
        log("File processing completed", "info")
        stats = result.get("stats", {})
        processed = stats.get("processed_files", 0)
        unrecognized = stats.get("unrecognized_files", 0)

        # Get backup stats if available
        backup_stats = result.get("backup_stats", {})
        backed_up = backup_stats.get("backed_up_count", 0)
        skipped = backup_stats.get("skipped_count", 0)

        # Build status message with all stats
        status_msg = f"Processing complete. {processed} files processed successfully."

        # Add backup stats if available
        if backed_up > 0 or skipped > 0:
            status_msg += (
                f" {backed_up} files backed up, {skipped} identical files skipped."
            )

        # Add error info if unrecognized files exist
        if unrecognized > 0:
            status_msg += f" {unrecognized} files unrecognized."
            self.info_widget.set_status(status_msg, is_error=True)
        else:
            self.info_widget.set_status(status_msg)
