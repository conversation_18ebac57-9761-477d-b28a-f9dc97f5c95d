from functools import wraps
from warnings import warn
from typing import Callable, TypeVar, Any, Optional, cast

T = TypeVar('T', bound=Callable[..., Any])

def deprecated(message: Optional[str] = None) -> Callable[[T], T]:
    # Simple decorator to mark functions/methods as deprecated
    def decorator(func: T) -> T:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            # Create default message if none provided
            warning_msg = message or f"{func.__name__} is deprecated and will be removed in a future version."
            warn(warning_msg, DeprecationWarning, stacklevel=2)
            return func(*args, **kwargs)
        return cast(T, wrapper)
    return decorator

# Example usage:
# @deprecated("Use new_function() instead")
# def old_function():
#     pass
