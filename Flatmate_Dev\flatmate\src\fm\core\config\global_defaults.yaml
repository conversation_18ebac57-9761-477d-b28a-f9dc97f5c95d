# FlatMate Application Default Configuration
app:
  name: "flatMate"
  version: "1.0.0"
  debug_mode: true
  environment: "development"
  is_first_run: false

paths:
  base_data_dir: "~/.flatmate"
  temp_dir: "~/.flatmate/temp"
  backup_dir: "~/.flatmate/backups"
  logs: "~/.flatmate/logs"
  config: "~/.flatmate/config"
  data: "~/.flatmate/data"
  cache: "~/.flatmate/cache"
  master: ""  # Current master file location
  master_history: []  # List of historical master file locations
  recent_masters: []
  recent_files: ["home", "update_data"]

logging:
  level: "DEBUG"  # General/file log level
  console_level: "INFO"  # Console output threshold
  file_path: "~/.flatmate/logs/app.log"
  max_file_size_mb: 10
  backup_count: 5
  show_info: true
  show_warnings: true

files:
  data_extensions:
    - .csv
    - .xlsx
    - .json
  config_extensions:
    - .json
    - .yaml
    - .yml
  image_extensions:
    - .png
    - .jpg
    - .jpeg
  supported_extensions:
    - .csv
    - .xlsx
    - .json
    - .yaml
    - .yml
  max_size: 10485760  # 10MB

security:  # not implemented
  encryption_enabled: true
  data_anonymization: false

profiles:
  default: {}  # Placeholder for default profile settings
  active_profile: "default"

testing:
  mock_data_enabled: false
  test_mode: false
