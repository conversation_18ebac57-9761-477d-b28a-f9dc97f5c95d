"""
Enhanced Filter Proxy Model

Filtering and sorting component for the Enhanced Table View System.
Provides per-column filtering and advanced search capabilities.
"""

from PySide6.QtCore import Qt, QSortFilterProxyModel


class EnhancedFilterProxyModel(QSortFilterProxyModel):
    """Enhanced filter proxy model with per-column filtering."""
    
    def __init__(self, parent=None):
        """Initialize the enhanced filter proxy model."""
        super().__init__(parent)
        self._column_filters = {}
    
    def set_column_filter(self, column, pattern: str):
        """Set filter for a specific column or all columns."""
        if not pattern:
            if column in self._column_filters:
                del self._column_filters[column]
        else:
            self._column_filters[column] = pattern
        self.invalidateFilter()
    
    def clear_filters(self):
        """Clear all filters."""
        self._column_filters.clear()
        self.invalidateFilter()
    
    def filterAcceptsRow(self, source_row, source_parent):
        """Check if row matches all column filters."""
        model = self.sourceModel()

        # If no filters, accept all rows
        if not self._column_filters:
            return True

        # Check each column filter
        for column, pattern in self._column_filters.items():

            # Handle "All Columns" search (optimized for performance)
            if column == "all_columns":
                # Parse pattern once for efficiency
                and_terms, exclude_terms = self._parse_filter_pattern(pattern)

                # If no terms, accept all rows
                if not and_terms and not exclude_terms:
                    continue

                # Build combined text from all visible columns for this row
                combined_text_parts = []
                for col_idx in range(model.columnCount()):
                    index = model.index(source_row, col_idx, source_parent)
                    if index.isValid():
                        data = model.data(index, Qt.DisplayRole)
                        if data is not None:
                            combined_text_parts.append(str(data))

                # Search the combined text (more efficient than checking each column separately)
                combined_text = " ".join(combined_text_parts).lower()

                # Check AND terms
                for term in and_terms:
                    if term not in combined_text:
                        return False

                # Check EXCLUDE terms
                for term in exclude_terms:
                    if term in combined_text:
                        return False
            else:
                # Single column search
                index = model.index(source_row, column, source_parent)
                if not index.isValid():
                    if source_row == 0:
                        print(f"DEBUG: Invalid index for column {column}")
                    continue

                data = model.data(index, Qt.DisplayRole)
                if data is None:
                    if source_row == 0:
                        print(f"DEBUG: No data for column {column}")
                    continue

                if not self._check_pattern_match(str(data), pattern):
                    return False

        return True

    def _parse_filter_pattern(self, pattern: str) -> tuple[list[str], list[str]]:
        """Parse pattern into AND terms and EXCLUDE terms.

        Args:
            pattern: Filter pattern (e.g., "foo bar -exclude")

        Returns:
            Tuple of (and_terms, exclude_terms)
        """
        if not pattern.strip():
            return [], []

        terms = pattern.split()
        and_terms = []
        exclude_terms = []

        for term in terms:
            if term.startswith('-') and len(term) > 1:
                # Exclude term (remove the -)
                exclude_terms.append(term[1:].lower())
            else:
                # AND term (including terms that are just "-")
                and_terms.append(term.lower())

        return and_terms, exclude_terms

    def _parse_filter_pattern_v2(self, pattern: str) -> dict:
        """Enhanced pattern parsing with OR support.

        Args:
            pattern: Filter pattern (e.g., "coffee|tea -decaf")

        Returns:
            Dict with parsed expression structure
        """
        if not pattern.strip():
            return {"type": "empty"}

        # Check if pattern contains OR operators
        if '|' in pattern:
            return self._parse_or_expression(pattern)
        else:
            # Fall back to original AND/exclude logic
            and_terms, exclude_terms = self._parse_filter_pattern(pattern)
            return {
                "type": "and_exclude",
                "and_terms": and_terms,
                "exclude_terms": exclude_terms
            }

    def _parse_or_expression(self, pattern: str) -> dict:
        """Parse pattern with OR operators.

        Args:
            pattern: Pattern like "coffee|tea -decaf" or "coffee|tea hot"

        Returns:
            Dict with OR expression structure
        """
        # Split by spaces to handle exclude terms and separate OR groups
        parts = pattern.split()
        or_groups = []
        exclude_terms = []

        for part in parts:
            if part.startswith('-') and len(part) > 1:
                # Exclude term - handle pipes in exclude terms properly
                exclude_part = part[1:]  # Remove the -
                if '|' in exclude_part:
                    # Multiple exclude terms: -term1|term2 means exclude term1 OR term2
                    exclude_list = [term.strip().lower() for term in exclude_part.split('|') if term.strip()]
                    exclude_terms.extend(exclude_list)
                else:
                    exclude_terms.append(exclude_part.lower())
            else:
                # OR group - split by pipe
                if '|' in part:
                    or_terms = [term.strip().lower() for term in part.split('|') if term.strip()]
                    or_groups.append(or_terms)
                else:
                    # Single term (treat as OR group with one item)
                    or_groups.append([part.lower()])

        return {
            "type": "or_expression",
            "or_groups": or_groups,
            "exclude_terms": exclude_terms
        }

    def _check_pattern_match(self, data_str: str, pattern: str) -> bool:
        """Check if data matches the filter pattern with enhanced OR/AND/exclude logic."""
        # Use enhanced parsing
        parsed = self._parse_filter_pattern_v2(pattern)

        if parsed["type"] == "empty":
            return True

        data_lower = data_str.lower()

        if parsed["type"] == "and_exclude":
            # Original AND/exclude logic
            and_terms = parsed["and_terms"]
            exclude_terms = parsed["exclude_terms"]

            # All AND terms must be present
            for term in and_terms:
                if term not in data_lower:
                    return False

            # No EXCLUDE terms may be present
            for term in exclude_terms:
                if term in data_lower:
                    return False

            return True

        elif parsed["type"] == "or_expression":
            # Enhanced OR logic
            or_groups = parsed["or_groups"]
            exclude_terms = parsed["exclude_terms"]

            # Check exclude terms first (if any exclude term matches, reject)
            for term in exclude_terms:
                if term in data_lower:
                    return False

            # All OR groups must have at least one matching term
            for or_group in or_groups:
                group_matched = False
                for term in or_group:
                    if term in data_lower:
                        group_matched = True
                        break

                # If this OR group didn't match, reject the row
                if not group_matched:
                    return False

            return True

        # Fallback (shouldn't happen)
        return True

    def lessThan(self, left, right):
        """Custom sorting to use original data values instead of display text."""
        # Get original data from UserRole for proper sorting
        left_data = self.sourceModel().data(left, Qt.UserRole)
        right_data = self.sourceModel().data(right, Qt.UserRole)

        # If we have original data, use it for comparison
        if left_data is not None and right_data is not None:
            # Handle None values (put them at the end)
            if left_data is None and right_data is not None:
                return False
            if left_data is not None and right_data is None:
                return True
            if left_data is None and right_data is None:
                return False

            # For dates and other comparable types, use direct comparison
            try:
                return left_data < right_data
            except TypeError:
                # Fallback to string comparison if types aren't comparable
                return str(left_data) < str(right_data)

        # Fallback to default behavior if no UserRole data
        return super().lessThan(left, right)
