"""
File Display Widget for showing file information.
"""

import os

from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import (QHBoxLayout, QHeaderView, QPushButton, QStyle,
                               QTreeWidget, QTreeWidgetItem, QVBoxLayout,
                               QWidget)

from .....gui.styles.button_types import ButtonType
from ..utils.file_display_helper import FileDisplayHelper


class FileDisplayWidget(QWidget):
    """Widget for displaying source files and job info."""
    
    publish_file_removed = Signal(str)  # Publishes path of removed file
    publish_file_selected = Signal(str)  # Publishes path of selected file
    
    def __init__(self, parent=None):
        """Initialize the file display widget."""
        super().__init__(parent)
        self._init_ui()
        self._connect_signals()
        
        # Keep track of folder items
        self.folder_items = {}
    
    def _init_ui(self):
        """Initialize the UI components."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # File tree
        self.file_tree = QTreeWidget()
        self.file_tree.setHeaderLabels([
            "Name", "Size", "Status"
        ])
        self.file_tree.header().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        self.file_tree.header().setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        self.file_tree.header().setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        self.file_tree.setAlternatingRowColors(False)
        
        layout.addWidget(self.file_tree)
        
        # Buttons
        btn_layout = QHBoxLayout()
        
        self.remove_btn = QPushButton("Remove Selected")
        self.remove_btn.setProperty("type", ButtonType.SECONDARY.as_prop())
        self.remove_btn.setEnabled(False)  # Disabled until selection
        btn_layout.addWidget(self.remove_btn)
        
        btn_layout.addStretch()
        layout.addLayout(btn_layout)
    
    def _connect_signals(self):
        """Connect internal signals."""
        self.file_tree.itemSelectionChanged.connect(self._handle_selection_change)
        self.remove_btn.clicked.connect(self._handle_remove)
    
    def _handle_selection_change(self):
        """Enable/disable remove button based on selection."""
        selected_items = self.file_tree.selectedItems()
        self.remove_btn.setEnabled(bool(selected_items))
        
        # Publish selected file path if it's a file
        if selected_items:
            item = selected_items[0]
            file_path = item.data(0, Qt.ItemDataRole.UserRole)
            if file_path:  # Only publish if it's a file (not a folder)
                self.publish_file_selected.emit(file_path)
    
    def _handle_remove(self):
        """Remove selected files from tree."""
        for item in self.file_tree.selectedItems():
            file_path = item.data(0, Qt.ItemDataRole.UserRole)
            if file_path:
                self.publish_file_removed.emit(file_path)
                parent = item.parent()
                if parent:
                    parent.removeChild(item)
                else:
                    self.file_tree.takeTopLevelItem(self.file_tree.indexOfTopLevelItem(item))
    
    def set_files(self, files: list, source_dir: str = ""):
        """Set the files to display in the tree.
        
        Args:
            files: List of file paths
            source_dir: Source directory for relative paths
        """
        self.file_tree.clear()
        self.folder_items = {}
        
        if not source_dir:
            source_dir = os.path.dirname(files[0]) if files else ""
        
        # Create root item
        root_name = os.path.basename(source_dir) if source_dir else "Files"
        root_item = QTreeWidgetItem(self.file_tree)
        root_item.setText(0, root_name)
        root_item.setIcon(0, self.style().standardIcon(QStyle.StandardPixmap.SP_DirIcon))
        self.folder_items[source_dir] = root_item
        
        # Process files using FileDisplayHelper
        file_infos = FileDisplayHelper.process_files(files)
        
        for file_path, file_info in file_infos.items():
            self._add_file_item(file_path, file_info, source_dir)
            
        self.file_tree.expandAll()
    
    def _add_file_item(self, file_path: str, file_info: dict, source_dir: str):
        """Add a single file item to the tree."""
        try:
            rel_path = os.path.relpath(file_path, source_dir)
        except ValueError:
            # If paths are on different drives or source_dir is empty
            rel_path = os.path.basename(file_path)
        
        # Navigate/create folder structure
        current_path = source_dir
        parent_item = self.folder_items[source_dir]  # Root folder
        
        # Handle subfolders if any
        for folder in rel_path.split(os.sep)[:-1]:
            current_path = os.path.join(current_path, folder)
            if current_path not in self.folder_items:
                folder_item = QTreeWidgetItem(parent_item)
                folder_item.setText(0, folder)
                folder_item.setIcon(0, self.style().standardIcon(QStyle.StandardPixmap.SP_DirIcon))
                self.folder_items[current_path] = folder_item
                parent_item = folder_item
            else:
                parent_item = self.folder_items[current_path]
        
        # Add file item
        file_item = QTreeWidgetItem(parent_item)
        file_item.setText(0, rel_path.split(os.sep)[-1])  # Just the filename
        file_item.setIcon(0, self.style().standardIcon(QStyle.StandardPixmap.SP_FileIcon))
        file_item.setData(0, Qt.ItemDataRole.UserRole, file_path)
        
        # Add file details from FileDisplayHelper
        file_item.setText(1, file_info.get('size_str', 'N/A'))
        
        # Display format information (bank type and format type)
        bank_type = file_info.get('bank_type', 'Unknown')
        format_type = file_info.get('format_type', '')
        format_display = f"{bank_type} {format_type}".strip()
        file_item.setText(2, format_display)
        
    def get_files(self) -> list[str]:
        """Get all file paths in the tree.
        
        Returns:
            list[str]: List of absolute file paths in the tree
        """
        file_paths = []
        
        # Helper function to recursively collect file paths
        def collect_files(item):
            # Check if this item has a file path stored
            file_path = item.data(0, Qt.ItemDataRole.UserRole)
            if file_path:  # It's a file, not a folder
                file_paths.append(file_path)
            
            # Process children
            for i in range(item.childCount()):
                collect_files(item.child(i))
        
        # Process all top-level items
        for i in range(self.file_tree.topLevelItemCount()):
            collect_files(self.file_tree.topLevelItem(i))
            
        return file_paths
    
    def display_welcome(self):
        """Display welcome message in the tree."""
        self.file_tree.clear()
        self.folder_items = {}
        
        # Create welcome message item
        welcome_item = QTreeWidgetItem(["Welcome to Update Data", "", ""])
        welcome_item.setExpanded(True)
        self.file_tree.addTopLevelItem(welcome_item)
        
        # Add instructions
        instructions = [
            "Select source files or folder using the buttons on the left panel.",
            "Choose a save location for processed files.",
            "Click 'Process Files' to start processing."
        ]
        
        for instruction in instructions:
            item = QTreeWidgetItem()
            item.setText(0, instruction)
            welcome_item.addChild(item)
