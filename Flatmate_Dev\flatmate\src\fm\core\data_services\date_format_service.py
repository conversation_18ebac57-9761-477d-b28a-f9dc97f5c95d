"""
Centralized date handling service.

This service provides comprehensive date handling including:
- Flexible date parsing from various formats
- Standardization to ISO format (YYYY-MM-DD)
- Conversion between display formats
- Excel date number support
"""

from datetime import datetime, date
from typing import Optional, Any, List

import locale
import pandas as pd
from dateutil import parser as date_parser

from fm.core.services.logger import log



class DateFormatService:
    """A unified service for all date parsing, formatting, and standardization needs."""

    FALLBACK_DISPLAY_FORMAT = "%d/%m/%Y"
    COMMON_FORMATS = [
        "%d/%m/%Y", "%m/%d/%Y", "%d-%m-%Y", "%Y-%m-%d",
        "%d.%m.%Y", "%d %b %Y", "%d/%m/%y", "%d-%m-%y"
    ]

    # --- Public Formatting API ---

    @classmethod
    def format_date_for_display(
        cls,
        date_input: Any,
        format_override: Optional[str] = None,
        **parse_kwargs
    ) -> str:
        """Formats a date for UI display, using the user's preferred format as a default."""
        display_format = format_override or cls.get_user_date_format()
        return cls.format_date(date_input, display_format, **parse_kwargs)

    @classmethod
    def format_date(
        cls, 
        date_input: Any, 
        date_format: str,
        **parse_kwargs
    ) -> str:
        """Parses a date-like input and formats it into a specified string format."""
        parsed_date = cls._parse_date(date_input, **parse_kwargs)
        if not parsed_date:
            return ""
        return parsed_date.strftime(date_format)

    @classmethod
    def standardize_date(
        cls, 
        date_input: Any, 
        **parse_kwargs
    ) -> Optional[str]:
        """Parses a date and standardizes it to ISO format (YYYY-MM-DD)."""
        parsed_date = cls._parse_date(date_input, **parse_kwargs)
        return parsed_date.strftime("%Y-%m-%d") if parsed_date else None

    @classmethod
    def format_dataframe_dates(
        cls, 
        df: pd.DataFrame, 
        date_columns: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """Formats specified date columns in a DataFrame for display using user's default."""
        display_df = df.copy()
        if date_columns is None:
            date_columns = [col for col in df.columns if 'date' in str(col).lower()]
        
        for col in date_columns:
            if col in display_df.columns:
                display_df[col] = display_df[col].apply(cls.format_date_for_display)
        
        return display_df

    # --- Public Configuration API ---

    @classmethod
    def get_user_date_format(cls) -> str:
        """Gets the user's preferred date format string from config."""
        try:
            from fm.core.config.config import config
            # Use the intelligent locale-based default if no config is set
            return config.get_value('display.date_format', cls._get_default_display_format())
        except ImportError:
            return cls._get_default_display_format()

    @classmethod
    def set_user_date_format(cls, date_format: str):
        """Sets the user's preferred date format string in config."""
        try:
            from fm.core.config.config import config
            config.set_value('display.date_format', date_format)
        except ImportError:
            pass

    @classmethod
    def get_date_format_choices(cls) -> List[tuple[str, str]]:
        """Returns a list of (Label, format_string) tuples for UI components."""
        return [
            ("DD/MM/YYYY", "%d/%m/%Y"),
            ("MM/DD/YYYY", "%m/%d/%Y"),
            ("DD-MM-YYYY", "%d-%m-%Y"),
            ("YYYY-MM-DD", "%Y-%m-%d"),
            ("DD.MM.YYYY", "%d.%m.%Y"),
            ("DD Mon YYYY", "%d %b %Y"),
            ("DD/MM/YY",   "%d/%m/%y"),
            ("DD-MM-YY",   "%d-%m-%y"),
        ]

    # --- Internal Parsing Logic ---

    @classmethod
    def _get_default_display_format(cls) -> str:
        """Determines a sensible default date format based on the user's OS locale."""
        try:
            locale_code, _ = locale.getdefaultlocale()
            if locale_code and locale_code.lower() in ('en_us',):
                return "%m/%d/%Y"  # US format
        except Exception:
            # Fallback in case of any error during locale detection
            log.debug("Could not determine OS locale. Using fallback display format.")
        return cls.FALLBACK_DISPLAY_FORMAT

    @classmethod
    def _parse_date(
        cls, 
        date_input: Any, 
        format_hint: Optional[str] = None,
        dayfirst: bool = True,
        yearfirst: bool = False
    ) -> Optional[date]:
        """
        Parse a date from various input types with flexible format detection.
        
        Args:
            date_input: Date to parse (str, int, float, date, datetime, pd.Timestamp)
            format_hint: Optional format string to try first (e.g., "%d/%m/%Y")
            dayfirst: Whether to interpret the first value as day (default: True for NZ dates)
            yearfirst: Whether to interpret the first value as year
            
        Returns:
            date object or None if parsing fails
        """
        # 1. Main case: String input (most common case first)
        if isinstance(date_input, str):
            date_str = date_input.strip()
            if not date_str:
                return None
                
            # 1.1 First try with format_hint if provided
            if format_hint:
                try:
                    return datetime.strptime(date_str, format_hint).date()
                except ValueError:
                    log.debug(f"Format hint '{format_hint}' failed. Falling back to flexible parsing.")
            
            ##  1.2 Try flexible parsing using hint as a guide 
            try:
                if format_hint:
                    # Use hint to guide parser, improving accuracy for ambiguous dates.
                    df = 'd' in format_hint.lower()[:2]
                    yf = 'y' in format_hint.lower()[:2]
                else:
                    df, yf = dayfirst, yearfirst
                    
                return date_parser.parse(date_str, dayfirst=df, yearfirst=yf).date()
            except (ValueError, OverflowError):
                log.debug("Flexible parser failed. Falling back to common formats.")
                
                # 1.3 Try common formats as last resort
                for fmt in cls.COMMON_FORMATS:
                    try:
                        return datetime.strptime(date_str, fmt).date()
                    except ValueError:
                        continue
                         
                log.error(f"All parsing methods failed for date string: '{date_str}'")
                return None
        
        # 2. Handle non-string types and edge cases
        if pd.isna(date_input) or not date_input:
            return None
        if isinstance(date_input, (datetime, pd.Timestamp)):
            return date_input.date()
        if isinstance(date_input, date):
            return date_input
        if isinstance(date_input, (int, float)) and cls._is_excel_date(date_input):
            try:
                return (datetime(1899, 12, 30) + pd.Timedelta(days=float(date_input))).date()
            except (ValueError, OverflowError):
                log.error(f"Failed to convert Excel date: {date_input}")
        
        # If we get here, it's an unsupported type
        log.error(f"Unsupported date input type: {type(date_input)}")
        return None

    @staticmethod
    def _is_excel_date(value: Any) -> bool:
        """Checks if a value appears to be an Excel-style date number."""
        return isinstance(value, (int, float)) and 0 < value < 100000
