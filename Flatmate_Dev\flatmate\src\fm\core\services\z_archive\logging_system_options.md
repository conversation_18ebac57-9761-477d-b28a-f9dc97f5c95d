# FlatMate Logging System: Console Output Control

This document outlines the discussion and proposed solution for controlling terminal output from the logging system in the FlatMate application.

## 1. Problem Statement

The current logging system (`fm.core.services.logger.py`) unconditionally prints all log messages to the console via a `print()` statement within its `log_handler` function. This leads to verbose terminal output, including all `DEBUG` level messages (such as full configuration dumps), which can clutter the console and make it difficult to see important informational messages, warnings, or errors.

## 2. Goal

Implement a system to control the verbosity of console log output based on log severity, configurable by the user. The system should allow users to specify a minimum log level (e.g., "INFO", "DEBUG", "WARNING") for messages appearing on the console, while ensuring that all log messages (regardless of level, down to `DEBUG`) are still written to the log file.

## 3. Proposed Solution: Level-Based Console Logging

This solution involves changes to the configuration keys, default configuration files, and the logger service.

### 3.1. Configuration Key

*   **New Key:** A new configuration key will be introduced specifically for the console's log level threshold.
    *   **Enum Member (in `fm.core.config.keys.ConfigKeys.Logging`):** `CONSOLE_LOG_LEVEL`
    *   **String Value (for YAML/internal use):** `'logging.console_level'`
*   **Default Value:**
    *   In `ConfigKeys.get_defaults()`: Set to `"INFO"`.
    *   In `global_defaults.yaml` (under `logging`): `console_level: "INFO"`.
*   **User Override:** Users can override this value in their `preferences.yaml` file (e.g., setting `console_level: "DEBUG"` to see more verbose output).

### 3.2. Logger Implementation (`fm.core.services.logger.py`)

1.  **Severity Mapping:**
    *   A global dictionary `_LOG_LEVEL_SEVERITIES` will map `LogLevel` string values (e.g., "DEBUG", "INFO") to numerical severities (e.g., 10, 20). This facilitates easy comparison of log levels.
    ```python
    _LOG_LEVEL_SEVERITIES = {
        LogLevel.DEBUG.value: 10,
        LogLevel.INFO.value: 20,
        LogLevel.WARNING.value: 30,
        LogLevel.ERROR.value: 40,
        LogLevel.CRITICAL.value: 50,
        'UNKNOWN': 60
    }
    ```

2.  **Console Log Threshold Storage:**
    *   A global variable `_CONSOLE_LOG_THRESHOLD_SEVERITY` will store the numerical severity for the console output. It will be initialized with a sensible default (e.g., severity of INFO).

3.  **Reading Configuration (`setup_logging` function):**
    *   The `setup_logging` function will be responsible for reading the `CONSOLE_LOG_LEVEL` setting from the `ConfigManager` (global `config` instance).
    *   It will convert the retrieved string level (e.g., "INFO") into its numerical severity using `_LOG_LEVEL_SEVERITIES`.
    *   This numerical severity will then update the global `_CONSOLE_LOG_THRESHOLD_SEVERITY`.
    *   **Critical Consideration (Timing):** `setup_logging` must be called *after* `ConfigManager` has loaded user preferences to ensure user-defined console log levels are respected. If this order cannot be guaranteed, a mechanism to update the logger's console threshold post-config-load will be necessary (e.g., a dedicated update function or an event-based update).

4.  **Conditional Printing (`log_handler` function):**
    *   The `log_handler` function, which receives all log events, will:
        *   Determine the numerical severity of the incoming `log_event`.
        *   Compare this `event_severity` with the `_CONSOLE_LOG_THRESHOLD_SEVERITY`.
        *   The `print(log_line_for_console)` statement will only be executed if `event_severity >= _CONSOLE_LOG_THRESHOLD_SEVERITY`.

### 3.3. Example Flow

1.  App starts.
2.  `ConfigManager` loads `global_defaults.yaml` (`console_level` is "INFO").
3.  `ConfigManager` loads `preferences.yaml`. If user set `console_level: "DEBUG"`, this overrides the default.
4.  `setup_logging()` is called.
    *   It reads `CONSOLE_LOG_LEVEL` from `ConfigManager` (gets "DEBUG" if user set it).
    *   Sets `_CONSOLE_LOG_THRESHOLD_SEVERITY` to 10 (the severity for DEBUG).
5.  A `DEBUG` message is logged by some module.
6.  `log_handler` receives the event.
    *   Event severity is 10 (DEBUG).
    *   `_CONSOLE_LOG_THRESHOLD_SEVERITY` is 10.
    *   Since 10 >= 10, the message is printed to the console.
7.  An `INFO` message is logged.
8.  `log_handler` receives the event.
    *   Event severity is 20 (INFO).
    *   Since 20 >= 10, the message is printed to the console.

If `console_level` was "INFO" (threshold 20), the `DEBUG` message (severity 10) would *not* print to console, but the `INFO` message (severity 20) *would*.

## 4. Benefits of this Approach

*   **User Control:** Users can easily adjust console verbosity without code changes.
*   **Standard Practice:** Aligns with how standard logging frameworks manage different handlers and their thresholds.
*   **Clarity:** Clear separation of concerns: `ConfigManager` provides the setting, `logger.py` implements the filtering logic.
*   **Preserves Full File Logs:** All messages, regardless of console settings, continue to be logged to the file.

## 5. Open Questions / Next Steps for Implementation

*   **Confirm Startup Order:** Verify that `ConfigManager` loads all configurations (including user preferences) *before* `setup_logging()` is called. If not, discuss strategies to ensure the logger gets the final, correct console log level (e.g., deferred update, event-based notification).
*   **Finalize `logger.py` Changes:** Implement the logic described in section 3.2.
*   **Testing:** Thoroughly test the new system with different `CONSOLE_LOG_LEVEL` settings in `preferences.yaml`.
*   **Documentation:** Update any relevant user or developer documentation regarding logging configuration.

This approach provides a robust and flexible way to manage console log output, addressing the initial problem of excessive verbosity.
