# Flatmate Feature Task Protocol

A structured protocol for implementing new features as tasks, inspired by <PERSON><PERSON>'s specification-driven approach.

---

## Protocol Overview

For each new feature, create a dedicated folder in flatmate/DOCS/_FEATURES/<feature_name>/ 
containing:

- `requirements.md` — Clear, testable requirements
- `design.md` — Architecture and technical design
- `tasks.md` — Actionable implementation steps

---

## 1. requirements.md
- List requirements as concise, testable statements (EARS syntax recommended).
- Example:
  - The system shall allow users to export transaction data as CSV.
  - The export shall include all visible columns in the transaction table.

## 2. design.md
- Describe the architecture, data flow, and main components.
- Specify endpoints, UI changes, and dependencies.
- Example:
  - Add 'Export' button to transaction UI.
  - Backend endpoint: `/api/export-csv`
  - Utilises `CSVFormatter` service.

## 3. tasks.md
- Break down the implementation into sequential, actionable steps.
- Example:
  1. Add export button to UI.
  2. Implement `/api/export-csv` endpoint.
  3. Write unit tests for CSV export.
  4. Update documentation.

---

## Usage
- Every new feature starts with these three files in its folder.
- All discussion, planning, and progress tracking happens in this folder.
- Link the feature folder in your main tracking document when work begins.

---

**This protocol ensures clarity, accountability, and modularity for all new feature work.**
