"""Simple pattern-based transaction categoriser for the MVP.

Later we can extend this with ML models. For now it loads regex / substring
patterns from a JSON file (resources/patterns.json) where each entry is:
{ "CATEGORY_NAME": ["pattern1", "pattern2", ...], ... }
Patterns are applied case-insensitively to the transaction description.
"""
from __future__ import annotations
from pathlib import Path
import json
import re
from typing import Dict, List
from fm.core.utils.timing_decorator import timing_decorator

_RESOURCE_PATH = Path(__file__).parent.parent / "resources" / "patterns.json"

class TransactionCategorizer:
    """Categorise transactions using simple pattern matching."""

    def __init__(self, patterns_file: Path | None = None):
        self.patterns_file = Path(patterns_file) if patterns_file else _RESOURCE_PATH
        self._compiled: Dict[str, List[re.Pattern]] = {}
        self._load_patterns()

    # ---------------------------------------------------------------------
    def _load_patterns(self) -> None:
        if not self.patterns_file.exists():
            self.patterns_file.write_text("{}", encoding="utf-8")
        data = json.loads(self.patterns_file.read_text(encoding="utf-8"))
        self._compiled = {
            category: [re.compile(pat, re.IGNORECASE) for pat in pats]
            for category, pats in data.items()
        }

    # ------------------------------------------------------------------
    def categorize_row(self, row):
        """Pandas apply helper – expects a Series with a `description` field."""
        desc = str(row.get("description", ""))
        for category, patterns in self._compiled.items():
            if any(pat.search(desc) for pat in patterns):
                return category
        return "Uncategorised"
