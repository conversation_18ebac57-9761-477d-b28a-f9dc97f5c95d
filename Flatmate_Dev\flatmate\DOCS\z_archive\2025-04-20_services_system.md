# FlatMate Services System

## Overview
The services system provides centralized functionality that can be used across multiple modules. It follows a similar pattern to the config system, with core services being wrapped by module-specific service layers.

## Architecture

### Core Services
Located in `src/fm/core/services/`:
```
core/services/
├── __init__.py
├── master_file_service.py    # Manages master file tracking
└── future_services.py        # (Future services will be added here)
```

### Module Service Wrappers
Each module that needs access to core services has its own service wrapper:
```
module_name/services/
├── __init__.py              # Exports module_services
├── local_services.py        # Module's service wrapper
└── README.md               # Documents module services
```

## Service Pattern

### Core Service Implementation
```python
class CoreService:
    """Core service implementation."""
    _instance = None  # Singleton pattern
    
    def __init__(self):
        self.config = config  # Access to core config
        self.events = global_event_bus  # Access to event system
```

### Module Service Wrapper
```python
class ModuleServices:
    """Module-specific service wrapper."""
    def __init__(self):
        self._core_service = core_service
        
    def module_specific_method(self):
        """Add module-specific functionality."""
        return self._core_service.some_method()
```

## Current Services

### Master File Service
- **Purpose**: Track and manage master file locations
- **Features**:
  - Track current master file
  - Maintain history
  - Validate master files
  - Emit events on changes
- **Usage**:
  ```python
  from .services import module_services
  
  # Through module wrapper
  module_services.update_master_location(file_path)
  ```

## Benefits
1. **Centralization**: Common functionality in one place
2. **Modularity**: Module wrappers provide isolation
3. **Flexibility**: Can add module-specific service methods
4. **Event Integration**: Services can emit events for system-wide coordination

## Adding New Services
1. Create new service in `core/services/`
2. Add to module wrappers that need it
3. Update module README.md
4. Document in this architecture doc
