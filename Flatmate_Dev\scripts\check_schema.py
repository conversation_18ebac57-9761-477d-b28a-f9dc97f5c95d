import sqlite3
import os

# Path to the database
db_path = os.path.expanduser("~/.flatmate/data/transactions.db")

# Connect to the database
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# Get the table info
cursor.execute("PRAGMA table_info(transactions)")
columns = cursor.fetchall()

print("Columns in transactions table:")
for col in columns:
    print(f"- {col[1]} ({col[2]})")

conn.close()
