# Database Service Documentation

## Overview
The database service provides a clean interface for managing financial transactions in the Flatmate application. It follows a layered architecture with clear separation of concerns between the service layer and the data access layer.

## Architecture

### 1. Core Components

#### 1.1 StandardColumns (fm/core/standards/fm_standard_columns.py)
- Defines all standard column names used throughout the application
- Each column has:
  - A display name (e.g., "Date", "Amount")
  - A database name (lowercase of the enum name, e.g., "date", "amount")

#### 1.2 DataService (service.py)
- Main service class that coordinates between the UI and the database
- Provides high-level operations like importing transactions
- Delegates to the repository for data access

#### 1.3 TransactionRepository (repository/transaction_repository.py)
- Abstract base class defining the repository interface
- Declares methods that must be implemented by concrete repositories

#### 1.4 SQLiteTransactionRepository (repository/sqlite_repository.py)
- Concrete implementation of TransactionRepository for SQLite
- Handles all database operations
- Manages the database schema and migrations

## Data Flow

### 1. Importing Transactions

```mermaid
graph TD
    A[Pipeline/UI] -->|DataFrame with display names| B[DataService]
    B -->|Calls| C[SQLiteTransactionRepository]
    C -->|Converts display names to db_names| D[(SQLite Database)]
```

1. **Input**: DataFrame with display column names (e.g., "Date", "Amount")
2. **Service Layer**: Validates and processes the data
3. **Repository Layer**:
   - Converts display names to database column names
   - Handles database operations
   - Manages transactions and error handling
4. **Output**: Operation result (success/failure, counts of affected rows)

### 2. Column Name Handling

#### Display Names vs Database Names
- **Display Names**: Used in the UI and DataFrames (e.g., "Date", "Amount")
- **Database Names**: Used in the database schema (e.g., "date", "amount")

#### Conversion
- Display Name → Database Name: `StandardColumns.DATE.db_name`
- Database Name → Display Name: `StandardColumns.from_display_name("Date").value`

## Database Schema

The database schema is defined in `SQLiteTransactionRepository._ensure_db_exists()` and includes:

### System Columns
- `id`: INTEGER PRIMARY KEY
- `import_date`: TEXT
- `modified_date`: TEXT
- `is_deleted`: INTEGER (0 or 1)

### Standard Columns
- Text Fields: `date`, `details`, `account`, etc.
- Numeric Fields: `amount`, `balance`, `credit_amount`, `debit_amount`

## Performance Considerations

### Current Implementation
- Column name conversion happens at runtime for each operation
- No caching of column name mappings
- Each repository method handles its own connection management

### Potential Optimizations
1. **Column Name Caching**:
   ```python
   _COLUMN_MAPPING = {col.value: col.db_name for col in StandardColumns}
   ```

2. **Bulk Operations**:
   - Use SQLite's bulk insert for better performance
   - Consider using executemany() for multiple inserts

3. **Connection Pooling**:
   - Implement connection pooling for better performance under load

## Error Handling

The service uses a consistent error handling pattern:
1. **Validation Errors**: Raised for invalid input data
2. **Database Errors**: Wrapped in a custom exception with context
3. **Transaction Management**: Uses context managers to ensure proper cleanup

## Testing

### Unit Tests
- Test each component in isolation
- Mock dependencies where necessary

### Integration Tests
- Test the full stack with an in-memory SQLite database
- Verify data integrity across operations

## Future Improvements

1. **Schema Versioning**:
   - Implement proper schema versioning and migrations

2. **Performance**:
   - Add connection pooling
   - Optimize bulk operations
   - Add database indexes for common queries

3. **Documentation**:
   - Add more detailed API documentation
   - Include examples for common operations

## Example Usage

```python
# Initialize the service
db_path = "path/to/database.db"
repo = SQLiteTransactionRepository(db_path)
service = DataService(repo)

# Import transactions
result = service.import_transactions_from_df(df)
print(f"Imported {result.added_count} transactions")

# Query transactions
transactions = service.get_transactions()
```

## See Also

- [Standard Columns Definition](../core/standards/fm_standard_columns.py)
- [DataService Implementation](./service.py)
- [SQLite Repository Implementation](./repository/sqlite_repository.py)
