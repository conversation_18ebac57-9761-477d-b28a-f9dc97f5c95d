# pylint: disable=unused-import
"""
Comprehensive Configuration and Path Management.
Centralizes application settings, path discovery,
and environment management.
"""

# Standard library imports

from pathlib import Path
from typing import Any, Optional, Union, Dict

# Local imports
from .config_loader import config_loader
from .master_file_tracker import master_file_tracker
from .path_manager import path_manager
from .settings_manager import settings_manager


class ConfigurationManager:
    """
    Centralized configuration management for the FlatMate application.

    Provides a unified interface:
    - for accessing and managing application configuration.
    - for managing the active profile.
    Implements a singleton pattern to ensure a single configuration instance.
    """

    _instance = None

    def __new__(cls):
        """
        Singleton pattern implementation.

        Ensures only one instance of ConfigurationManager exists.
        """
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialize()
        return cls._instance

    def __init__(self):
        """
        Initialize the configuration manager.

        This method is called for each new instance:
        but due to the singleton pattern,
        actual initialization happens in _initialize method.
        """
        # Initialize all attributes to None
        self.paths = None
        self._config = None
        self._master_file_tracker = None
        self._settings_manager = None
        self._active_profile = None

    def _initialize(self):
        """
        Initialize configuration by delegating to specialized modules.

        Centralizes initialization of paths,
        settings, and configuration loading.
        """
        try:
            # Actual initialization
            self.paths = path_manager.paths
            self._config = config_loader.get_setting("config", {})
            self._master_file_tracker = master_file_tracker
            self._settings_manager = settings_manager
            self._active_profile = settings_manager.get_active_profile()
        except Exception as e:
            raise RuntimeError(
                f"Configuration initialization failed: {e}"
            ) from e

    def update_master_location(self, file_path: Union[str, Path]):
        """
        Delegate master file location tracking to master_file_tracker.
        """
        return self._master_file_tracker.update_master_location(file_path)

    def get_master_location(self) -> Dict:
        """
        Retrieve master file location information.
        """
        return self._master_file_tracker.get_master_location()

    def get_master_path(self) -> Optional[Path]:
        """
        Fetch the most recently tracked master file path.
        """
        master_location = master_file_tracker.get_master_location()
        current = master_location.get("current")
        
        # Ensure current is a dictionary with a 'path' key
        if isinstance(current, dict) and "path" in current:
            return Path(current["path"])
        
        return None

    def master_exists(self) -> bool:
        """
        Check if a master file is currently tracked and accessible.
        """
        master_location = master_file_tracker.get_master_location()
        return master_location["current"] is not None

    def get(self, key: str, default: Optional[Any] = None) -> Any:
        """
        Retrieve a configuration value, delegating to settings manager.
        """
        return settings_manager.get_setting(key, default)

    def set(self, key: str, value: Any):
        """
        Set a configuration value, delegating to settings manager.
        """
        settings_manager.update_setting(key, value)

    def is_development_mode(self) -> bool:
        """
        Check if application is running in development mode.
        """
        return self._config.current_env == "development"

    def is_production_mode(self) -> bool:
        """
        Check if application is running in production mode.
        """
        return self._config.current_env == "production"

    def is_test_mode(self) -> bool:
        """
        Check if application is running in test mode.
        """
        return self._config.current_env == "test"

    def set_font_size(self, size_name: str) -> None:
        """
        Update application font size preference.

        Args:
            size_name (str): Name of the font size to set.
        """
        # Validate font size
        valid_sizes = ["small", "medium", "large", "extra_large"]
        if size_name not in valid_sizes:
            raise ValueError(f"Invalid font size. Choose from {valid_sizes}")

        # Update font size in settings
        self.set("font_size", size_name)

        # Optional: Reload configuration to reflect changes
        self._config = config_loader.get_setting("config", {})

    def get_font_size(self) -> str:
        """
        Get the current font size setting.
        """
        return self._config.get("font_size", "medium")

    def _load_active_profile(self):
        """
        Load the active user profile.
        """
        # Placeholder for profile loading logic
        self._active_profile = None

    def list_profiles(self) -> list:
        """
        List available profiles.
        """
        # Placeholder implementation
        return []

    def create_profile(self, name: Optional[str] = None) -> bool:
        """
        Create a new user profile.
        """
        settings_manager.create_profile(name or "new_profile")
        return True

    def switch_profile(self, profile_name: str) -> None:
        """
        Switch to a different profile.
        """
        # Note: This method might need more implementation
        # depending on the settings_manager
        settings_manager.create_profile(profile_name)

    def load_configuration(self, paths=None):
        """
        Load configuration from specified paths.

        This method is a compatibility wrapper for existing code.

        Args:
            paths (Dict, optional): Paths to load configuration from.

        Returns:
            Dict: Loaded configuration.
        """
        # Use the existing configuration or reload if paths are provided
        if paths:
            # Note: This is a simplified implementation
            return config_loader.get_setting("config", {})
        return self._config or {}


# Create a singleton instance
config = ConfigurationManager()
