"""
Centralized column name mapping service.

This service provides the elegant solution for db_name vs display_name mapping
throughout the application. It uses the central Columns registry as the canonical source
and provides consistent fallback strategies for unknown columns.
"""

import pandas as pd
from typing import Dict, List, Optional

from fm.core.data_services.standards.columns import Columns


class ColumnNameService:
    """
    Centralized column name mapping service for the entire application.
    """

    @staticmethod
    def get_display_mapping(df_columns: List[str],
                          custom_mapping: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """
        Get complete db_name -> display_name mapping for DataFrame columns.

        Args:
            df_columns: List of column names from DataFrame (db_names)
            custom_mapping: Optional custom db_name -> display_name overrides

        Returns:
            Complete mapping: db_name -> display_name
        """
        mapping = {}
        # Create reverse mapping from the Columns registry (db_name -> display_name)
        standard_mapping = {col.db_name: col.display_name for col in Columns.get_all()}

        for db_col in df_columns:
            if custom_mapping and db_col in custom_mapping:
                # Custom override takes precedence
                mapping[db_col] = custom_mapping[db_col]
            elif db_col in standard_mapping:
                # Use Columns registry canonical display name
                mapping[db_col] = standard_mapping[db_col]
            else:
                # Fallback for unknown columns - convert snake_case to Title Case
                mapping[db_col] = db_col.replace('_', ' ').title()

        return mapping

    @staticmethod
    def get_reverse_mapping(df_columns: List[str],
                          custom_mapping: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """
        Get complete display_name -> db_name mapping for DataFrame columns.

        Args:
            df_columns: List of column names from DataFrame (db_names)
            custom_mapping: Optional custom db_name -> display_name overrides

        Returns:
            Complete reverse mapping: display_name -> db_name
        """
        display_mapping = ColumnNameService.get_display_mapping(df_columns, custom_mapping)
        return {display_name: db_name for db_name, display_name in display_mapping.items()}

    @staticmethod
    def apply_display_names(df: pd.DataFrame,
                          custom_mapping: Optional[Dict[str, str]] = None) -> pd.DataFrame:
        """
        Apply display names to DataFrame columns.

        Args:
            df: DataFrame with db_names as columns
            custom_mapping: Optional custom db_name -> display_name overrides

        Returns:
            DataFrame with display names as columns
        """
        if df.empty:
            return df.copy()

        display_mapping = ColumnNameService.get_display_mapping(
            df.columns.tolist(),
            custom_mapping
        )

        display_df = df.copy()
        display_df.columns = [display_mapping.get(col, col) for col in df.columns]
        return display_df

    @staticmethod
    def apply_db_names(df: pd.DataFrame,
                      custom_mapping: Optional[Dict[str, str]] = None) -> pd.DataFrame:
        """
        Apply database names to DataFrame columns.

        Args:
            df: DataFrame with display_names as columns
            custom_mapping: Optional custom db_name -> display_name overrides

        Returns:
            DataFrame with db_names as columns
        """
        if df.empty:
            return df.copy()

        # Get reverse mapping (display_name -> db_name)
        reverse_mapping = ColumnNameService.get_reverse_mapping(
            df.columns.tolist(),
            custom_mapping
        )

        db_df = df.copy()
        db_df.columns = [reverse_mapping.get(col, col) for col in df.columns]
        return db_df

    @staticmethod
    def get_default_visible_columns_for_categorize() -> List[str]:
        """
        Get default visible columns (db_names) for the Categorize screen.
        """
        return [
            str(Columns.DATE),        # 'date'
            str(Columns.DETAILS),     # 'details'
            str(Columns.AMOUNT),      # 'amount'
            str(Columns.ACCOUNT),     # 'account'
            str(Columns.TAGS),        # 'tags'
            str(Columns.CATEGORY),    # 'category'
        ]

    @staticmethod
    def get_default_visible_columns_for_module(module_name: str) -> List[str]:
        """
        Get default visible columns (db_names) for a specific application module.

        Args:
            module_name: The name of the module (e.g., 'categorize', 'reports')

        Returns:
            List of db_names that should be visible by default
        """
        if module_name == 'categorize':
            return ColumnNameService.get_default_visible_columns_for_categorize()
        elif module_name == 'reports':
            # Future: Add report module defaults
            return ColumnNameService.get_default_visible_columns_for_categorize()  # Fallback
        else:
            # Default fallback - core transaction columns
            return [
                str(Columns.DATE),
                str(Columns.DETAILS),
                str(Columns.AMOUNT),
                str(Columns.ACCOUNT),
            ]

    @staticmethod
    def convert_transactions_to_dataframe(transactions: List,
                                        ensure_columns: Optional[List[str]] = None) -> pd.DataFrame:
        """
        Convert Transaction objects to DataFrame with proper db_name columns.

        Args:
            transactions: List of Transaction objects from database
            ensure_columns: Optional list of db_name columns to ensure exist (with empty values)

        Returns:
            DataFrame with db_name columns ready for display conversion
        """
        if not transactions:
            return pd.DataFrame()

        data = []
        all_cols = Columns.get_all()
        for transaction in transactions:
            trans_dict = {}
            for col in all_cols:
                db_name = col.db_name
                # Handle special field name mappings from the Transaction object
                if db_name == 'details' and hasattr(transaction, 'description'):
                    trans_dict[db_name] = getattr(transaction, 'description', '')
                elif db_name == 'account' and hasattr(transaction, 'account_number'):
                    trans_dict[db_name] = getattr(transaction, 'account_number', '')
                elif db_name == 'id' and hasattr(transaction, 'transaction_id'):
                    trans_dict[db_name] = getattr(transaction, 'transaction_id', '')
                elif hasattr(transaction, db_name):
                    trans_dict[db_name] = getattr(transaction, db_name, '')
                else:
                    trans_dict[db_name] = ''
            data.append(trans_dict)

        df = pd.DataFrame(data)

        if ensure_columns:
            for col in ensure_columns:
                if col not in df.columns:
                    df[col] = ""

        # Ensure the DataFrame has all standard columns, even if they were not in the transactions
        final_cols = [str(c) for c in all_cols]
        df = df.reindex(columns=final_cols, fill_value='')

        return df


# Convenience functions for backward compatibility
def get_column_display_mapping(df_columns: List[str],
                             custom_mapping: Optional[Dict[str, str]] = None) -> Dict[str, str]:
    """Convenience function for getting display mapping."""
    return ColumnNameService.get_display_mapping(df_columns, custom_mapping)


def apply_display_names_to_dataframe(df: pd.DataFrame,
                                   custom_mapping: Optional[Dict[str, str]] = None) -> pd.DataFrame:
    """Convenience function for applying display names."""
    return ColumnNameService.apply_display_names(df, custom_mapping)
