# Transaction Table Architecture

**Date:** 2025-01-14  
**Status:** Architectural Definition  
**Purpose:** Define the clear separation of concerns for transaction data handling

---

## Core Principle

**The transaction table contains EVERYTHING - it is the single source of truth for all transaction data.**

---

## Architecture Layers

### **1. Transaction Table = ALL Columns**
- **Contains:** Every column defined in the `Columns` registry
- **Purpose:** Complete data persistence and single source of truth
- **Implementation:** `Columns.get_all_columns()` defines what goes in the table
- **Includes:**
  - Bank data (date, amount, details, account, balance)
  - Handler metadata (source_filename, source_bank, source_type)
  - System columns (db_uid, import_date, modified_date, is_deleted)
  - User columns (category, notes, tags)

### **2. Statement Handlers = Data Input + Metadata Creation**
- **Bank data mapping:** Raw CSV → standardized columns (date, amount, details, source_uid)
- **Metadata creation:** Information gleaned or created during processing:
  - `source_filename` - which file this transaction came from
  - `source_bank` - which bank (ASB, Kiwibank, etc.)
  - `source_type` - statement type/handler used
  - `statement_date` - extracted from statement metadata
  - Account information, payment types, other party details
- **Do NOT create:** Database system columns (db_uid, import_date, etc.)
- **Output:** DataFrame with bank data + processing metadata

### **3. Database Layer = System Column Addition**
- **Input:** Handler DataFrame with bank data + metadata
- **Adds system columns:**
  - `db_uid` - generated hash for duplicate detection
  - `import_date` - when imported to database
  - `modified_date` - when last changed
  - `is_deleted` - soft delete flag
- **Handles:** Duplicate detection, data integrity, persistence
- **Output:** Complete transaction record in database

### **4. User Layer = Editable Fields**
- **User-editable columns:**
  - `category` - user categorization
  - `notes` - user notes  
  - `tags` - user tags
- **Persistence:** Changes saved back to transaction table
- **UI:** Provides interface for editing these fields

### **5. Display Layer = Configurable Subset**
- **Default display columns:** Sensible user-friendly subset
- **Never shows by default:** Hash, db_uid, system tracking columns
- **Configurable:** User can choose which columns to display
- **Persistent preferences:** Remember user's last column selection
- **Implementation:** Separate from data storage - pure presentation logic

---

## Data Flow

```
CSV File → Statement Handler → Database Layer → Transaction Table
                ↓                    ↓              ↓
         Bank Data +           + System        = Complete
         Metadata             Columns          Record
                                               
                                               ↓
                                        Display Layer
                                               ↓
                                    User-Friendly View
                                    (Configurable Subset)
```

---

## Column Responsibilities

### **Statement Handler Columns:**
- Core transaction data (date, amount, details, account, balance)
- Bank-provided identifiers (source_uid)
- Processing metadata (source_filename, source_bank, source_type)
- Extracted information (statement_date, payment_type, other_party)

### **Database System Columns:**
- Generated identifiers (db_uid for duplicate detection)
- Audit trail (import_date, modified_date)
- Data management (is_deleted)

### **User Editable Columns:**
- Categorization (category, tags)
- User annotations (notes)

### **Display Configuration:**
- Which columns to show (user preference)
- Column order and width (user preference)
- Default sensible subset for new users

---

## Key Architectural Decisions

### **1. Single Source of Truth**
- **Transaction table contains ALL columns** from `Columns` registry
- **No partial representations** - everything goes in the database
- **Other layers work with subsets** but table has complete data

### **2. Clear Separation of Concerns**
- **Statement handlers:** Data input and processing metadata only
- **Database layer:** System management and persistence only
- **Display layer:** Presentation and user preferences only
- **User layer:** Editable fields only

### **3. Data Integrity**
- **Bank data preservation:** Never overwrite source_uid with generated hash
- **Separate identifiers:** source_uid (bank) vs db_uid (generated)
- **Audit trail:** Track when and how data entered system
- **Soft deletes:** Preserve data integrity with is_deleted flag

### **4. User Experience**
- **Default display:** Sensible subset of user-relevant columns
- **Configurable:** Users can customize what they see
- **Persistent:** Remember user preferences across sessions
- **Hidden complexity:** System columns available but not prominent

---

## Implementation Guidelines

### **For Statement Handlers:**
- Map bank data to standard columns
- Create processing metadata during parsing
- Do NOT generate system columns
- Output clean DataFrame with bank data + metadata

### **For Database Layer:**
- Accept handler DataFrame
- Generate system columns (db_uid, timestamps)
- Handle duplicate detection using both source_uid and db_uid
- Persist complete record to transaction table

### **For Display Layer:**
- Load user's column preferences
- Show sensible defaults for new users
- Allow runtime column configuration
- Hide system columns unless specifically requested

### **For User Interface:**
- Provide editing for category, notes, tags
- Allow column selection and ordering
- Persist user preferences
- Export capabilities for any column subset

---

## Future Considerations

- **Performance:** Indexing strategy for large datasets
- **Migration:** Schema changes and data migration
- **Backup:** Master CSV export as user-accessible backup
- **Reporting:** Custom reports from filtered/categorized data
- **API:** Programmatic access to transaction data

---

**Bottom Line:** The transaction table is the complete repository of all transaction data. Other layers work with appropriate subsets but the database contains everything.
