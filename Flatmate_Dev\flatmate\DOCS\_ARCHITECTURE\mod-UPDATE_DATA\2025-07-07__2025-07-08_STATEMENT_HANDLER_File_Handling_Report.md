# Statement Handler File Handling Report - 2025-07-08

## Overview
This report documents the recent changes to file handling in the statement handler system, focusing on the transition from DataFrame-based to filepath-based processing.

## Key Changes

### 1. Handler Initialization
- **Old Flow**: Handler received a pre-loaded DataFrame
- **New Flow**: Handler receives a filepath and manages its own file loading

### 2. File Handling Responsibility
- **Old Responsibility**: `dw_director` loaded files and passed DataFrames
- **New Responsibility**: Handlers now load files directly in `can_handle_file`

## Current Implementation Analysis

### `can_handle_file` Method
- Now handles file loading directly
- Reads a small preview of the file (20 rows by default)
- Performs lightweight validation:
  - Filename pattern matching
  - Header/content validation
  - Account number extraction

### `format_df` Method
- **Current Signature**: `format_df(self, df: pd.DataFrame, filepath: str) -> pd.DataFrame`
- **Issue**: Still expects a DataFrame parameter despite the new file handling approach
- **Potential Inconsistency**: File loading happens in two places (`can_handle_file` and potentially in `dw_director`)

## Impact on Data Pipeline

### Integration Points
1. **Handler Registration**: `_handler_registry` now needs to handle file-based validation
2. **Director Flow**: `dw_director` still passes DataFrames to `format_df`
3. **Error Handling**: File-related errors now need to be handled at the handler level

### Potential Issues
1. **Duplicate File Loading**: Files may be loaded multiple times
2. **Inconsistent State**: `format_df` might receive a DataFrame that was loaded differently than in `can_handle_file`
3. **Memory Usage**: Large files are loaded into memory in multiple places

## Recommended Actions

### Immediate Fixes
1. Update `format_df` to accept a filepath instead of a DataFrame
2. Move file loading logic to a shared method in the base handler
3. Update `dw_director` to pass filepaths instead of DataFrames

### Code Changes Required

#### 1. Base Handler Update
```python
def format_df(self, filepath: str) -> pd.DataFrame:
    # Load the file once using the same method as can_handle_file
    df = self._load_file_preview(filepath, rows=None)  # None means load all rows
    # Rest of the formatting logic...
```

#### 2. Director Update
```python
# In _load_and_process_files
for path in filepaths:
    handler = get_handler(path)
    if handler:
        try:
            processed_df = handler.format_df(path)  # Pass filepath, not DataFrame
            # Rest of the processing...
```

## Next Steps
1. Update the base handler interface
2. Refactor `dw_director` to work with filepaths
3. Update all concrete handlers to match the new interface
4. Add comprehensive tests for the new file handling logic
