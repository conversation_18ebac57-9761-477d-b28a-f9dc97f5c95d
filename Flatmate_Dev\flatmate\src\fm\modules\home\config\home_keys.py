"""Configuration keys for Home module."""

from enum import Enum

class HomeKeys:
    """Configuration keys specific to Home module."""
    
    class Paths(str, Enum):
        """Path settings"""
        DASHBOARD = 'paths.home.dashboard'
        WIDGETS = 'paths.home.widgets'
    
    class Layout(str, Enum):
        """Layout settings"""
        COLUMNS = 'home.layout.columns'
        SPACING = 'home.layout.spacing'
        WIDGET_ORDER = 'home.layout.widget_order'
    
    class Recent(str, Enum):
        """Recent items settings"""
        MAX_ITEMS = 'home.recent.max_items'
        SHOW_DATES = 'home.recent.show_dates'
    
    @classmethod
    def get_defaults(cls) -> dict:
        """Get default values for home settings."""
        return {
            cls.Layout.COLUMNS: 2,
            cls.Layout.SPACING: 10,
            cls.Layout.WIDGET_ORDER: ['recent', 'stats', 'actions'],
            cls.Recent.MAX_ITEMS: 5,
            cls.Recent.SHOW_DATES: True
        }
