categorize:
  display:
    description_width: 40
    date_width: 12
    amount_width: 12
    account_width: 15
    tags_width: 20
    category_width: 20
    notes_width: 30
    visible_columns:
      - date
      - description
      - amount
      - account
      - tags
      - category
    default_sort_column: date
    default_sort_order: descending
    date_format: "yyyy-MM-dd"
    amount_decimals: 2
  
  filters:
    default_days: 30
    remember_last: true
  
  tags:
    auto_suggest: true
    recent_tags: []
  
  patterns:
    file_path: "~/.flatmate/data/categorize/patterns.json"
    auto_learn: true
