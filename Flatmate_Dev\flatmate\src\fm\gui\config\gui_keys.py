"""GUI-specific configuration keys."""

from enum import Enum
from typing import Union

class Window(str, Enum):
    """Window settings"""
    WIDTH = 'gui.window.width'
    HEIGHT = 'gui.window.height'

class Panel(str, Enum):
    """Panel settings"""
    LEFT_LAST_WIDTH = 'gui.window.panels.left.last_width'
    RIGHT_LAST_WIDTH = 'gui.window.panels.right.last_width'
    LEFT_DEFAULT_WIDTH = 'gui.window.panels.left.default_width'
    RIGHT_DEFAULT_WIDTH = 'gui.window.panels.right.default_width'
    RIGHT_STATE = 'gui.window.panels.right.state'
    RIGHT_COMPACT_WIDTH = 'gui.window.panels.right.compact_width'

class Theme(str, Enum):
    """Theme settings"""
    FONT_SIZE = 'gui.theme.font_size'

class Layout(str, Enum):
    """Layout settings"""
    SPACING = 'gui.layout.spacing'
    MARGINS = 'gui.layout.margins'

GuiKeyType = Union[Window, Panel, Theme, Layout]

class GuiKeys:
    """Configuration keys specific to GUI."""
    
    Window = Window
    Panel = Panel
    Theme = Theme
    Layout = Layout

    @classmethod
    def get_defaults(cls) -> dict:
        """Get default values for GUI settings."""
        return {
            cls.Window.WIDTH: 1200,
            cls.Window.HEIGHT: 800,
            cls.Panel.LEFT_LAST_WIDTH: 240,
            cls.Panel.RIGHT_LAST_WIDTH: 240,
            cls.Panel.LEFT_DEFAULT_WIDTH: 240,
            cls.Panel.RIGHT_DEFAULT_WIDTH: 240,
            cls.Panel.RIGHT_STATE: 'compact',
            cls.Panel.RIGHT_COMPACT_WIDTH: 50,
            cls.Theme.FONT_SIZE: 14,
            cls.Layout.SPACING: 10,
            cls.Layout.MARGINS: 8,
        }
