"""
Navigation pane component for the application.
Provides a vertical navigation bar with module icons.
"""

from pathlib import Path

from fm.gui.icons.icon_manager import icon_manager
from fm.gui.icons.icon_renderer import IconRenderer
from PySide6.QtCore import QSize, Qt, Signal
from PySide6.QtGui import QColor, QIcon
from PySide6.QtWidgets import (
    QApplication,
    QButtonGroup,
    QMainWindow,
    QSizePolicy,
    QToolButton,
    QVBoxLayout,
    QWidget,
)

# from PySide6.QtSvg import QSvgRenderer

# Navigation item identifiers
NAV_HOME = "home"
NAV_PROFILES = "profiles"
NAV_IMPORT_DATA = "import_data"
NAV_VIEW_DATA = "view_data"


class NavButton(QToolButton):
    """Custom navigation button with icon and optional text."""

    # Define colors for different states
    ACTIVE_COLOR = QColor(
        72, 142, 82
    )  # #488E52 - Secondary hover green for active state
    INACTIVE_COLOR = QColor(204, 204, 204)  # #CCCCCC - calm-white for inactive 
    '''#TODO: I don't like hard coaded colours - this colour is called calm-white (for dark mode)in gui/styles/palette.qss
    creating a dynamic system has proved difficult, qt qss doesnt do vars, or if it does, the documentation is thin on the ground'''
    
    HOVER_COLOR = QColor(255, 255, 255)  # White for hover state

    def __init__(self, icon_path=None, text="", parent=None):
        super().__init__(parent)

        # Set up appearance - transparent and borderless
        self.setCheckable(True)
        self.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonIconOnly)
        self.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        self.setIconSize(QSize(32, 32))

        # Use the QSS class-based styling instead of inline styles
        self.setProperty("class", "nav_button")

        # Set text if provided
        if text:
            self.setText(text)

        # Store the icon path for later use when changing states
        self.icon_path = icon_path
        self.is_hovered = False
        self.is_active = False

        # Set initial icon (inactive state)
        if icon_path:
            self.setIcon(self._create_colored_icon(icon_path, self.INACTIVE_COLOR))

    def _create_colored_icon(self, icon_path, color):
        """Create a colored icon from an SVG file with explicit color control.

        Args:
            icon_path: Path to the SVG icon file
            color: Color to apply to the icon

        Returns:
            QIcon: Colored icon
        """
        try:
            # Use the IconRenderer utility to create a colored icon with the specified color
            return IconRenderer.create_colored_icon(
                icon_path, size=self.iconSize(), color=color
            )
        except Exception as e:
            print(f"Error creating colored icon: {e}")
            return QIcon(str(icon_path))  # Fallback to standard loading

    def enterEvent(self, event):
        """Handle mouse enter event to update icon color for hover state."""
        self.is_hovered = True
        self._update_icon_state()
        super().enterEvent(event)

    def leaveEvent(self, event):
        """Handle mouse leave event to revert icon color."""
        self.is_hovered = False
        self._update_icon_state()
        super().leaveEvent(event)

    def setActive(self, active):
        """Set the active state of the button and update icon color accordingly."""
        self.is_active = active
        self.setChecked(active)
        self._update_icon_state()

    def _update_icon_state(self):
        """Update the icon color based on current button state."""
        if not self.icon_path:
            return

        # Determine the appropriate color based on state
        if self.is_active:
            color = self.ACTIVE_COLOR
        elif self.is_hovered:
            color = self.HOVER_COLOR
        else:
            color = self.INACTIVE_COLOR

        # Update the icon with the selected color
        self.setIcon(self._create_colored_icon(self.icon_path, color))


class NavPane(QWidget):
    """
    Navigation pane component that provides a vertical navigation bar
    with module icons.
    """

    # Signal emitted when a navigation item is selected
    # Passes the identifier of the selected item
    navigationSelected = Signal(str)

    # Mapping between module IDs and navigation button IDs
    # This allows modules to have different names than their navigation buttons
    MODULE_TO_NAV_MAP = {
        # Direct mappings (module ID -> nav button ID)
        "home": NAV_HOME,
        "profiles": NAV_PROFILES,
        "view_data": NAV_VIEW_DATA,
        # Special mappings for mismatched names
        "update_data": NAV_IMPORT_DATA,  # Module is 'update_data' but nav button is 'import_data'
        "categorize": NAV_VIEW_DATA      # Module is 'categorize' but nav button is 'view_data'
    }

    def __init__(self, parent=None):
        super().__init__(parent)

        # Set size policy to make the widget conform to its content
        self.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Preferred)
        
        # Ensure minimum width to properly display buttons
        self.setMinimumWidth(40)

        # Set up the UI
        self._setup_nav_pane()

    def _setup_nav_pane(self):
        """Set up the navigation pane UI."""
        # Main layout with same margins as UtilitiesPane for consistency
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 0, 8, 8)  # Match UtilitiesPane margins
        layout.setSpacing(8)
        # Align top but not setting size constraint to match UtilitiesPane behavior
        layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        # Make the background fully transparent
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setStyleSheet("background-color: transparent;")

        # Button group to manage selection
        self.button_group = QButtonGroup(self)
        self.button_group.setExclusive(True)
        self.button_group.buttonClicked.connect(self._on_button_clicked)

        # Navigation buttons
        self.nav_buttons = {}

        # Create navigation buttons
        nav_items = [
            (NAV_HOME, "Home"),
            (NAV_PROFILES, "Profiles"),
            (NAV_IMPORT_DATA, "Import Data"),
            (NAV_VIEW_DATA, "View Data"),
        ]

        for item_id, item_text in nav_items:
            # Map nav item ID to icon path using IconManager
            icon_path = None
            try:
                # Convert nav button ID to icon name (lowercase without NAV_ prefix)
                icon_name = item_id.lower().replace('nav_', '')
                # Get icon path from the manager
                icon_path = icon_manager.get_nav_icon(icon_name)
                # Create button with icon
                button = NavButton(icon_path)
            except FileNotFoundError as e:
                print(f"Warning: Icon not found for {item_id}: {e}")
                # Create button without icon as fallback
                button = NavButton()

            # Add tooltip
            button.setToolTip(item_text)
            button.setObjectName(f"nav_{item_id}")  # ? where are these defined?

            # Add to layout
            layout.addWidget(button)

            # Add to button group
            self.button_group.addButton(button)

            # Store reference
            self.nav_buttons[item_id] = button

        # Add stretch to push buttons to the top
        layout.addStretch()
        
        # Developer panel button has been removed

    def _on_button_clicked(self, button):
        """Handle button clicks in the navigation pane."""
        # Find which item was clicked
        for item_id, btn in self.nav_buttons.items():
            if btn == button:
                self._select_item(item_id)
                break

    def clear_all_highlights(self):
        """Clear the active state of all navigation buttons.

        This should be called before highlighting a new button to ensure
        only one button is highlighted at a time.
        """
        for button in self.nav_buttons.values():
            button.setActive(False)

    def highlight_item(self, item_id):
        """Highlight a navigation item without emitting the navigation signal.

        This only updates the visual state of the button without triggering navigation.
        Useful when a module is activated through other means (coordinator, event bus)
        and you want the nav button to reflect the current state.

        Args:
            item_id: Identifier of the navigation item to highlight

        Returns:
            bool: True if the item was highlighted, False if it doesn't exist
        """
        # First, clear all highlights to ensure only one button is active
        self.clear_all_highlights()

        # Map module ID to navigation button ID if needed
        nav_id = self.MODULE_TO_NAV_MAP.get(item_id, item_id)

        # Check if the mapped ID exists in our navigation buttons
        if nav_id in self.nav_buttons:
            button = self.nav_buttons[nav_id]
            # Update button state (highlight it) without emitting signal
            button.setActive(True)
            return True
        else:
            # Item doesn't exist in navigation - log this for debugging
            print(
                f"Warning: No navigation button found for '{item_id}' (mapped to '{nav_id}')"
            )
            return False

    def _select_item(self, item_id):
        """Select a navigation item programmatically and emit the navigation signal.

        This highlights the button and emits the navigationSelected signal with the item_id.

        Args:
            item_id: Identifier of the navigation item to select
        """
        # Highlight the button
        self.highlight_item(item_id)

        # Emit signal to notify listeners that this item was selected
        print(f"[NavPane] Emitting navigationSelected signal with item_id: '{item_id}'")
        self.navigationSelected.emit(item_id)

    def select_home(self):
        """Convenience method to select the home item."""
        self._select_item(NAV_HOME)

    def select_profiles(self):
        """Convenience method to select the profiles item."""
        self._select_item(NAV_PROFILES)

    def select_import_data(self):
        """Convenience method to select the import data item."""
        self._select_item(NAV_IMPORT_DATA)

    def select_view_data(self):
        """Convenience method to select the view data item."""
        self._select_item(NAV_VIEW_DATA)

    # Developer panel functionality has been removed


if __name__ == "__main__":
    # Create standalone test application
    app = QApplication([])

    # Create main window with fixed size
    main_window = QMainWindow()
    main_window.setWindowTitle("Navigation Pane Test")
    main_window.setFixedSize(80, 300)  # Fixed width to match nav pane
    main_window.setStyleSheet(
        "background-color: #2D2D30;"
    )  # Dark background to match app theme

    # Create navigation pane
    nav_pane = NavPane()

    # Connect navigation signal to print the selected item
    def on_nav_selected(item_id):
        print(f"Navigation selected: {item_id}")

    nav_pane.navigationSelected.connect(on_nav_selected)

    # Set as central widget
    main_window.setCentralWidget(nav_pane)

    # Show window
    main_window.show()

    # Run application
    app.exec()

    # main for testing
