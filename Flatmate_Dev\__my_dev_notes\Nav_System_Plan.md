# Navigation System Implementation Plan

## Phase 1: Event System Setup

### 1.1 Navigation Event Channel
```markdown
## Task Overview
Implement navigation channel in existing event_bus system

## Relevant Files
- [@event_bus](/flatmate/src/fm/core/event_bus.py)
- [@nav_events](/flatmate/src/fm/core/events/nav_events.py) [new]

## Context
- Event bus supports multiple channels
- Need clean event structure for navigation
- Must support module registration/deregistration

## Requirements
1. Define navigation event types
2. Create channel registration
3. Implement event routing
4. Add type hints and validation

## Architectural Principles
- Follow existing event_bus patterns
- Use pub/sub terminology
- Self-documenting code
- Type-safe event handling

## Implementation Steps
1. Create nav_events.py
2. Define event classes
3. Add channel to event_bus
4. Create test cases
```

### 1.2 Event Structure Design
```markdown
## Task Overview
Design navigation event structure and types

## Relevant Files
- [@nav_events](/flatmate/src/fm/core/events/nav_events.py)

## Context
- Need to support module navigation
- Must handle dynamic registration
- Should support future extensions

## Requirements
1. Define core event types:
   - ModuleNavigationEvent
   - ModuleRegistrationEvent
   - NavigationStateEvent
2. Include all necessary metadata
3. Support validation

## Event Structure
ModuleNavigationEvent:
- module_id: str
- params: dict (optional)
- source: str

ModuleRegistrationEvent:
- module_id: str
- display_name: str
- icon_name: str
- position: int (optional)

## Implementation Steps
1. Create event classes
2. Add validation
3. Write tests
4. Document usage
```

## Phase 2: Base Components

### 2.1 Base Navigation Classes
```markdown
## Task Overview
Create abstract base classes for navigation components

## Relevant Files
- [@base_nav](/flatmate/src/fm/gui/components/base_nav.py) [new]
- [@nav_types](/flatmate/src/fm/gui/components/nav_types.py) [new]

## Context
- Need consistent interface
- Support multiple button types
- Handle state management

## Requirements
1. Create base classes:
   - BaseNavComponent
   - BaseNavButton
   - BaseNavPane
2. Define interfaces
3. Implement core logic

## Class Structure
BaseNavComponent:
- event_bus: EventBus
- register()
- unregister()

BaseNavButton(BaseNavComponent):
- icon: IconService
- text: str
- publish_navigation()

BaseNavPane(BaseNavComponent):
- buttons: List[BaseNavButton]
- layout: QVBoxLayout
- handle_registration()

## Implementation Steps
1. Create base classes
2. Add type hints
3. Implement core methods
4. Write documentation
```

### 2.2 Navigation Manager
```markdown
## Task Overview
Implement NavManager for coordinating navigation

## Relevant Files
- [@nav_manager](/flatmate/src/fm/gui/components/nav_manager.py) [new]
- [@base_nav](/flatmate/src/fm/gui/components/base_nav.py)

## Context
- Central coordinator for navigation
- Handles module registration
- Manages navigation state

## Requirements
1. Module registration handling
2. Navigation state management
3. Event coordination
4. Error handling

## Implementation Steps
1. Create NavManager class
2. Implement registration logic
3. Add state management
4. Create tests
```

## Phase 3: Migration

### 3.1 Component Migration
```markdown
## Task Overview
Migrate existing components to new system

## Relevant Files
- [@nav_pane](/flatmate/src/fm/gui/components/nav_pane.py)
- [@nav_buttons](/flatmate/src/fm/gui/components/nav_buttons.py)

## Context
- Current direct coupling
- Need to preserve functionality
- Maintain UI consistency

## Requirements
1. Update NavPane
2. Convert buttons
3. Preserve styling
4. Maintain compatibility

## Migration Steps
1. Create new classes
2. Migrate functionality
3. Update references
4. Test thoroughly
```

### 3.2 Integration
```markdown
## Task Overview
Integrate new navigation system

## Relevant Files
- [@main_window](/flatmate/src/fm/gui/main_window.py)
- [@module_co-ordinator](/flatmate/src/fm/modules/_module_co-ordinator)

## Context
- Need smooth transition
- Maintain existing functionality
- Support all modules

## Requirements
1. Update module registration
2. Integrate NavManager
3. Update event handling
4. Verify all modules

## Implementation Steps
1. Add NavManager
2. Update registration
3. Test navigation
4. Document changes
```

## Testing Strategy
- Unit tests for each component
- Integration tests for navigation flow
- Event handling validation
- UI interaction testing

## Rollout Plan
1. Implement event system
2. Create and test base classes
3. Build NavManager
4. Migrate one module as pilot
5. Full system migration
6. Testing and validation

---
Note: Each section follows clean architectural principles and maintains proper separation of concerns. Implementation will be incremental with testing at each stage.
