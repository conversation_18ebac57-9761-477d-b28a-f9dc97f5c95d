"""
Path Management for FlatMate Application.

Handles discovery and creation of application directories.
"""

import os
import sys
from pathlib import Path
from typing import Dict, Optional


class PathManager:
    """
    Manages application paths and directory structures.
    
    Provides centralized path discovery and directory creation.
    """
    
    # User-level Base Directories
    FLATMATE_HOME = Path.home() / '.flatmate'
    BACKUPS_DIR = FLATMATE_HOME / 'backups'
    LOGS_DIR = FLATMATE_HOME / 'logs'
    OUTPUTS_DIR = FLATMATE_HOME / 'outputs'
    CONFIG_DIR = FLATMATE_HOME / 'config'
    APP_DATA_DIR = FLATMATE_HOME / 'app_data'

    def __init__(self, app_root: Optional[Path] = None):
        """
        Initialize path manager with optional app root.
        
        Args:
            app_root (Optional[Path]): Root directory of the application.
        """
        self.app_root = app_root or self._find_app_root()
        self.paths = self._setup_app_paths()
        self._ensure_flatmate_dirs()

    def _find_app_root(self) -> Path:
        """
        Locate the root directory of the FlatMate application.
        
        Traverses up the directory tree to find the project root.
        Fails if no valid root is discovered.
        """
        current_dir = Path(__file__).resolve().parent
        while not (current_dir / 'src' / 'fm').exists():
            current_dir = current_dir.parent
            if current_dir == current_dir.parent:
                raise RuntimeError("Could not find app root directory (no src/fm found)")
        return current_dir

    def _setup_app_paths(self) -> Dict[str, Path]:
        """
        Generate and create essential application directories.
        
        Returns:
            Dict[str, Path]: Dictionary of application paths.
        """
        # Project-relative paths
        paths = {
            'app_root': self.app_root,
            'app_config_dir': self.app_root / 'src' / 'fm' / 'config',
            'default_settings_path': self.app_root / 'src' / 'fm' / 'config' / 'default_app_settings.toml',
            'test_settings_path': self.app_root / 'src' / 'fm' / 'config' / 'test_app_settings.toml',
            
            # User-specific settings
            'user_settings_dir': self.app_root / 'user_settings',
            'user_settings_path': self.app_root / 'user_settings' / 'app_settings.toml',
            'profiles_dir': self.app_root / 'user_settings' / 'profiles',
            
            # Logging and data
            'log_dir': self.app_root / 'logs',
            'data_dir': self.app_root / 'data',
            'backup_dir': self.app_root / 'data' / 'backups'
        }
        
        # User-level paths
        user_paths = {
            'flatmate_home': self.FLATMATE_HOME,
            'backups_dir': self.BACKUPS_DIR,
            'logs_dir': self.LOGS_DIR,
            'outputs_dir': self.OUTPUTS_DIR,
            'config_dir': self.CONFIG_DIR,
            'app_data_dir': self.APP_DATA_DIR
        }
        
        # Merge and ensure all directories exist
        all_paths = {**paths, **user_paths}
        for key, path in all_paths.items():
            if 'dir' in key and isinstance(path, Path):
                path.mkdir(parents=True, exist_ok=True)
        
        return all_paths

    def _ensure_flatmate_dirs(self):
        """
        Create all necessary Flatmate directories if they don't exist.
        """
        dirs_to_create = [
            self.FLATMATE_HOME,
            self.BACKUPS_DIR,
            self.LOGS_DIR,
            self.OUTPUTS_DIR,
            self.CONFIG_DIR,
            self.APP_DATA_DIR
        ]
        
        for directory in dirs_to_create:
            directory.mkdir(parents=True, exist_ok=True)

    @property
    def app_paths(self) -> Dict[str, Path]:
        """
        Retrieve all configured application paths.
        
        Returns:
            Dict[str, Path]: Dictionary of application paths.
        """
        return self.paths

# Create a singleton instance
path_manager = PathManager()
