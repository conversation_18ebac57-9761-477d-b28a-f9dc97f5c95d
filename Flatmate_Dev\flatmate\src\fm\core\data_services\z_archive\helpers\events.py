"""
Event system for data-related events.
Uses pub/sub pattern for loose coupling between components.
"""
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional, Set


@dataclass
class DataEvent:
    """Base class for data events."""
    event_type: str
    timestamp: Optional[datetime] = None
    data: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.data is None:
            self.data = {}


class EventBus:
    """
    Event bus for pub/sub communication between components.
    
    Allows components to publish events and subscribe to event types
    without direct coupling.
    """
    
    def __init__(self):
        self._subscribers: Dict[str, Set[Callable]] = {}
    
    def publish(self, event_type: str, data: Optional[Dict[str, Any]] = None) -> None:
        """
        Publish an event to all subscribers.
        
        Args:
            event_type: Type of event being published
            data: Optional data associated with the event
        """
        event = DataEvent(event_type=event_type, data=data)
        
        if event_type in self._subscribers:
            for callback in self._subscribers[event_type]:
                callback(event)
    
    def subscribe(self, event_type: str, callback: Callable[[DataEvent], None]) -> None:
        """
        Subscribe to an event type.
        
        Args:
            event_type: Type of event to subscribe to
            callback: Function to call when event is published
        """
        if event_type not in self._subscribers:
            self._subscribers[event_type] = set()
        
        self._subscribers[event_type].add(callback)
    
    def unsubscribe(self, event_type: str, callback: Callable[[DataEvent], None]) -> None:
        """
        Unsubscribe from an event type.
        
        Args:
            event_type: Type of event to unsubscribe from
            callback: Function to remove from subscribers
        """
        if event_type in self._subscribers and callback in self._subscribers[event_type]:
            self._subscribers[event_type].remove(callback)
            
            # Clean up empty sets
            if not self._subscribers[event_type]:
                del self._subscribers[event_type]


# Singleton instance for application-wide use
event_bus = EventBus()


# Standard event types
class DataEvents:
    """Standard event types for data operations."""
    TRANSACTIONS_IMPORTED = "transactions.imported"
    TRANSACTIONS_DELETED = "transactions.deleted"
    TRANSACTION_UPDATED = "transaction.updated"
    TRANSACTION_DELETED = "transaction.deleted"
    DATABASE_INITIALIZED = "database.initialized"
