"""
Developer Mode Settings Pane

Provides a configurable pane for developer options that can be integrated
into the right panel or other panel containers.
"""

from PySide6.QtCore import Signal, Qt
from PySide6.QtWidgets import (
    QCheckBox, QLabel, QVBoxLayout, QHBoxLayout, 
    QWidget, QGroupBox, QPushButton
)

from ..base import BasePane


class DevSettingsPaneCore(BasePane):
    """
    Pane containing developer mode settings and options.
    
    This pane can be integrated into panel containers (like the right panel)
    to provide developer-specific configuration options.
    
    Inherits from BasePane to provide:
    - Activation/deactivation behavior
    - Publishing signals for state changes
    - Standard pane lifecycle methods
    
    Signals:
        option_changed: Emitted when any option is changed, with option name and value
        publish_activated: Inherited from BasePane
        publish_deactivated: Inherited from BasePane
    """
    
    option_changed = Signal(str, bool)
    
    def __init__(self, parent=None):
        """Initialize the developer settings pane."""
        super().__init__(parent)
        self._options = {}  # Store option states
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Title
        self.title = QLabel("Developer Options")
        self.title.setObjectName("heading")
        layout.addWidget(self.title)
        
        # Processing Options Group
        self.processing_group = QGroupBox("Processing")
        processing_layout = QVBoxLayout(self.processing_group)
        
        # Create checkboxes for processing options
        self.cb_timestamped_csv = self._create_checkbox(
            "timestamped_csv", 
            "Time-stamped merged CSVs",
            "Create time-stamped copies of merged CSV files"
        )
        processing_layout.addWidget(self.cb_timestamped_csv)
        
        self.cb_tidy_originals = self._create_checkbox(
            "tidy_originals", 
            "Tidy up originals",
            "Move original files to an archive folder after processing"
        )
        processing_layout.addWidget(self.cb_tidy_originals)
        
        self.cb_db_output = self._create_checkbox(
            "db_output", 
            "Create DB output in destination folder",
            "Export database records to a CSV file in the destination folder"
        )
        processing_layout.addWidget(self.cb_db_output)
        
        self.cb_update_master = self._create_checkbox(
            "update_master", 
            "Update previous merged CSV master file",
            "Update the master CSV file with new transactions"
        )
        processing_layout.addWidget(self.cb_update_master)
        
        layout.addWidget(self.processing_group)
        
        # Add spacer
        layout.addStretch()
    
    def _create_checkbox(self, option_id, label_text, tooltip=None):
        """
        Create a checkbox with the given label and tooltip.
        
        Args:
            option_id: Unique identifier for this option
            label_text: Text to display next to the checkbox
            tooltip: Optional tooltip text
            
        Returns:
            QCheckBox: The created checkbox
        """
        checkbox = QCheckBox(label_text)
        if tooltip:
            checkbox.setToolTip(tooltip)
        
        # Store the initial state
        self._options[option_id] = False
        
        return checkbox
    
    def _connect_signals(self):
        """Connect widget signals."""
        # Connect all checkboxes to the _on_option_changed method
        self.cb_timestamped_csv.stateChanged.connect(
            lambda state: self._on_option_changed("timestamped_csv", state == Qt.CheckState.Checked)
        )
        self.cb_tidy_originals.stateChanged.connect(
            lambda state: self._on_option_changed("tidy_originals", state == Qt.CheckState.Checked)
        )
        self.cb_db_output.stateChanged.connect(
            lambda state: self._on_option_changed("db_output", state == Qt.CheckState.Checked)
        )
        self.cb_update_master.stateChanged.connect(
            lambda state: self._on_option_changed("update_master", state == Qt.CheckState.Checked)
        )
    
    def _on_option_changed(self, option_id, checked):
        """
        Handle option state changes.
        
        Args:
            option_id: Identifier of the changed option
            checked: New state of the option
        """
        self._options[option_id] = checked
        self.option_changed.emit(option_id, checked)
    
    def get_option(self, option_id):
        """
        Get the current state of an option.
        
        Args:
            option_id: Identifier of the option
            
        Returns:
            bool: Current state of the option
        """
        return self._options.get(option_id, False)
    
    def set_option(self, option_id, checked):
        """
        Set the state of an option.
        
        Args:
            option_id: Identifier of the option
            checked: New state of the option
        """
        if option_id == "timestamped_csv":
            self.cb_timestamped_csv.setChecked(checked)
        elif option_id == "tidy_originals":
            self.cb_tidy_originals.setChecked(checked)
        elif option_id == "db_output":
            self.cb_db_output.setChecked(checked)
        elif option_id == "update_master":
            self.cb_update_master.setChecked(checked)
        
        self._options[option_id] = checked


class DevSettingsPaneWithButton(BasePane):
    """
    A version of the dev settings pane that includes a toggle button.
    
    This can be used to create a collapsible pane that can be shown/hidden
    with a gear icon or similar button. Useful for integration into toolbars
    or compact panel areas.
    
    Inherits from BasePane to provide standard pane behavior.
    """
    
    option_changed = Signal(str, bool)
    
    def __init__(self, parent=None):
        """Initialize the pane with toggle button."""
        super().__init__(parent)
        self._visible = False
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # Button for toggling visibility
        button_layout = QHBoxLayout()
        button_layout.setAlignment(Qt.AlignmentFlag.AlignRight)
        
        self.toggle_button = QPushButton("⚙️ Dev Options")
        self.toggle_button.setProperty("type", "tool_btn")
        self.toggle_button.setCheckable(True)
        button_layout.addWidget(self.toggle_button)
        
        layout.addLayout(button_layout)
        
        # Settings pane (initially hidden)
        self.settings_pane = DevSettingsPaneCore(self)
        self.settings_pane.setVisible(False)
        layout.addWidget(self.settings_pane)
    
    def _connect_signals(self):
        """Connect widget signals."""
        self.toggle_button.toggled.connect(self._toggle_panel)
        self.settings_pane.option_changed.connect(self.option_changed)
    
    def _toggle_panel(self, checked):
        """Toggle the visibility of the settings pane."""
        self.settings_pane.setVisible(checked)
        self._visible = checked
        
        # Handle pane activation/deactivation
        if checked:
            self.settings_pane.activate()
        else:
            self.settings_pane.deactivate()
    
    def get_option(self, option_id):
        """Get the current state of an option."""
        return self.settings_pane.get_option(option_id)
    
    def set_option(self, option_id, checked):
        """Set the state of an option."""
        self.settings_pane.set_option(option_id, checked)
