import tomli
import tomli_w
from pathlib import Path
from typing import Any, Dict, Optional

class BaseConfig:
    def __init__(self, module_name: str):
        """
        Initialize configuration for a specific module.
        
        Args:
            module_name: Name of the module (used for config file paths)
        """
        self.module_name = module_name
        
        # Paths for configuration files
        self.module_config_dir = Path(__file__).parent.parent / 'modules' / module_name / 'config'
        self.default_config_path = self.module_config_dir / 'default.toml'
        
        # User-specific configuration path
        self.user_config_dir = Path('~/flatmate/configs/modules').expanduser()
        self.user_config_path = self.user_config_dir / f'{module_name}.toml'
        
        # Load configuration
        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """
        Load configuration from default and user-specific files.
        
        Returns:
            Merged configuration dictionary
        """
        config = {}
        
        # Load default module configuration
        if self.default_config_path.exists():
            with open(self.default_config_path, 'rb') as f:
                config.update(tomli.load(f))
        
        # Load and merge user-specific configuration
        if self.user_config_path.exists():
            with open(self.user_config_path, 'rb') as f:
                user_config = tomli.load(f)
                config = self._deep_merge(config, user_config)
        
        return config

    def _deep_merge(self, base: Dict, update: Dict) -> Dict:
        """
        Recursively merge two dictionaries.
        
        Args:
            base: Original configuration dictionary
            update: Configuration dictionary to merge on top
        
        Returns:
            Merged configuration dictionary
        """
        for key, value in update.items():
            if isinstance(value, dict):
                # Recursively merge nested dictionaries
                base[key] = self._deep_merge(base.get(key, {}), value)
            else:
                # Simple key-value replacement
                base[key] = value
        return base

    def get(self, key: str, default: Optional[Any] = None) -> Any:
        """
        Retrieve a configuration value using dot notation.
        
        Args:
            key: Dot-separated configuration key
            default: Default value if key is not found
        
        Returns:
            Configuration value or default
        
        Example:
            config.get('database.host', 'localhost')
        """
        keys = key.split('.')
        value = self.config
        for k in keys:
            if not isinstance(value, dict):
                return default
            value = value.get(k, {})
        return value if value != {} else default

    def set(self, key: str, value: Any) -> None:
        """
        Set a configuration value and persist to user config.
        
        Args:
            key: Dot-separated configuration key
            value: Value to set
        """
        keys = key.split('.')
        current = self.config
        for k in keys[:-1]:
            current = current.setdefault(k, {})
        current[keys[-1]] = value
        
        # Persist to user config
        self._save_user_config()

    def _save_user_config(self) -> None:
        """
        Save current configuration to user-specific config file.
        """
        # Ensure directory exists
        self.user_config_dir.mkdir(parents=True, exist_ok=True)
        
        with open(self.user_config_path, 'wb') as f:
            tomli_w.dump(self.config, f)

    def reset(self) -> None:
        """
        Reset configuration to default state by removing user config.
        """
        # Remove user config file if it exists
        if self.user_config_path.exists():
            self.user_config_path.unlink()
        
        # Reload default configuration
        self.config = self._load_config()
