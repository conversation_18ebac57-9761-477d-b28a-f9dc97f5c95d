"""Local configuration manager for GUI module."""

from pathlib import Path
from typing import Any, Union

import yaml

from ...core.config.base_local_config import BaseLocalConfig
from ...core.services.event_bus import Events, global_event_bus
from .gui_keys import Gui<PERSON><PERSON><PERSON>, GuiKeyType

# Type alias for all possible GUI key types
# GuiKeyType = Union[
#     GuiKeys.Window,
#     GuiKeys.Theme,
#     GuiKeys.Panel,
#     GuiKeys.Layout
# ]


class GuiConfig(BaseLocalConfig[GuiKeyType]):
    """Configuration manager for GUI settings.

    Provides access to:
    - Window dimensions
    - Panel dimensions
    - Theme settings
    - Layout settings
    """

    def __init__(self):
        """Initialize GUI config."""
        super().__init__()
        self.events = global_event_bus

        # Log initialization
        self.events.publish(
            Events.LOG_EVENT,
            {
                "level": "DEBUG",
                "module": "gui.config",
                "message": "GUI Configuration initialized with default settings",
            },
        )

    def get_defaults(self) -> dict:
        """Get default values for GUI settings."""
        return GuiKeys.get_defaults()

    def get_defaults_file_path(self) -> Path:
        """Get the path to the GUI defaults.yaml file."""
        return Path(__file__).parent / "defaults.yaml"

    def get_window_size(self) -> tuple[int, int]:
        """Get window size."""
        width = self.get_value(GuiKeys.Window.WIDTH, default=1200)
        height = self.get_value(GuiKeys.Window.HEIGHT, default=800)
        return width, height

    def set_window_size(self, width: int, height: int):
        """Set window size."""
        self.set_value(GuiKeys.Window.WIDTH, width)
        self.set_value(GuiKeys.Window.HEIGHT, height)

    def get_panel_width(self, panel: str, default: int = 233) -> int:
        """Get panel width."""
        if panel.lower() == "left":
            return self.get_value(GuiKeys.Panel.LEFT_LAST_WIDTH, default=default)
        elif panel.lower() == "right":
            return self.get_value(GuiKeys.Panel.RIGHT_LAST_WIDTH, default=default)
        return default

    def set_panel_width(self, panel: str, width: int):
        """Set panel width."""
        if panel.lower() == "left":
            self.set_value(GuiKeys.Panel.LEFT_LAST_WIDTH, width)
        elif panel.lower() == "right":
            self.set_value(GuiKeys.Panel.RIGHT_LAST_WIDTH, width)


# Global instance
gui_config = GuiConfig()
