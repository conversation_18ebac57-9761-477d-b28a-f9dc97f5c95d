# Refactoring Table View - Actionable Task List

## Prerequisites
- [ ] Copy `fm_table_view.py` to `fm_table_view_v2.py`
- [ ] Copy `table_view_core.py` to `table_view_core_v2.py`
- [ ] Copy `enhanced_table_model.py` to `enhanced_table_model_v2.py`

## 1. Create Configuration System

### 1.1 Create TableConfig
- [ ] Create `table_config_v2.py` in components folder
- [ ] Add dataclass with all configuration options:
  - `auto_size_columns: bool = True`
  - `max_column_width: int = 40`
  - `column_widths: Dict[str, int] = field(default_factory=dict)`
  - `editable_columns: List[str] = field(default_factory=list)`
  - `default_visible_columns: Optional[List[str]] = None`
  - `show_toolbar: bool = True`
  - `column_display_mapping: Optional[Dict[str, str]] = None`

## 2. Refactor CustomTableView (fm_table_view_v2.py)

### 2.1 Update Class Structure
- [ ] Rename class to `CustomTableView_v2`
- [ ] Add `_config = TableConfig()` in `__init__`
- [ ] Add `_dataframe = None` in `__init__`
- [ ] Add `_is_shown = False` in `__init__`

### 2.2 Implement Core Methods
- [ ] Implement `configure(**kwargs)` method:
  - Update config attributes from kwargs
  - Validate unknown options
  - Apply config if already shown
  - Return self for chaining

- [ ] Update `set_dataframe(df, column_mapping=None)` method:
  - Store dataframe and column mapping
  - Apply config if already shown
  - Return self for chaining

- [ ] Override `show()` method:
  - Set `_is_shown = True`
  - Apply configuration if dataframe exists
  - Call `super().show()`
  - Return self for chaining

- [ ] Override `hide()` method:
  - Set `_is_shown = False`
  - Call `super().hide()`
  - Return self for chaining

### 2.3 Implement Dynamic Methods
- [ ] `set_visible_columns(columns)`:
  - Update config
  - Apply to table if shown
  - Return self

- [ ] `hide_columns(columns)`:
  - Remove from visible columns
  - Update config and UI
  - Return self

- [ ] `show_columns(columns)`:
  - Add to visible columns
  - Update config and UI
  - Return self

- [ ] `resize_column(column, width)`:
  - Update config column_widths
  - Apply to table if shown
  - Return self

- [ ] `auto_resize_columns()`:
  - Re-trigger auto-sizing
  - Return self

- [ ] `show_toolbar()` / `hide_toolbar()`:
  - Update config
  - Show/hide toolbar widget
  - Return self

## 3. Refactor TableViewCore (table_view_core_v2.py)

### 3.1 Clean Up Width Logic
- [ ] Remove competing override chains in `set_dataframe()`
- [ ] Update `set_column_widths()` to use single clear logic:
  - Auto-resize first if enabled
  - Apply explicit widths with max limit
  - No saved width restoration conflicts

### 3.2 Add Configuration Support
- [ ] Add `apply_configuration(config)` method
- [ ] Remove hardcoded width restoration
- [ ] Ensure `setStretchLastSection(False)` is set

## 4. Update Internal Methods

### 4.1 Configuration Application
- [ ] Implement `_apply_configuration()` in CustomTableView_v2:
  - Apply auto-sizing if enabled
  - Apply explicit column widths
  - Apply column visibility
  - Apply editable columns
  - Apply toolbar visibility

### 4.2 Column Width Logic
- [ ] Ensure auto-sizing respects max_column_width
- [ ] Ensure explicit widths override auto-sizing
- [ ] Remove any competing width-setting code

## 5. Integration and Testing

### 5.1 Update Imports
- [ ] Update `transaction_view_panel.py` imports:
  - Change `from ...table_view.fm_table_view import CustomTableView`
  - To `from ...table_view.fm_table_view_v2 import CustomTableView_v2 as CustomTableView`

### 5.2 Update Usage
- [ ] Replace table creation in `transaction_view_panel.py`:
  - Remove `_apply_column_widths()` call
  - Use `configure()` method instead
  - Test with existing data

### 5.3 Test Configuration
- [ ] Test auto-sizing with 40-character limit
- [ ] Test explicit column widths
- [ ] Test column visibility changes
- [ ] Test editable columns
- [ ] Test toolbar show/hide

### 5.4 Test Dynamic Methods
- [ ] Test `hide_columns()` and `show_columns()`
- [ ] Test `resize_column()`
- [ ] Test method chaining
- [ ] Verify no override conflicts

## 6. Cleanup and Validation

### 6.1 Remove Old Override Code
- [ ] Remove `_apply_column_widths()` from transaction_view_panel
- [ ] Remove competing width logic from table_view_core_v2
- [ ] Remove saved width restoration conflicts

### 6.2 Verify Functionality
- [ ] Horizontal scrollbar appears when needed
- [ ] Column content is not cut off
- [ ] Auto-sizing works correctly
- [ ] All categorize module features work
- [ ] Performance is acceptable

## 7. Documentation
- [ ] Document new API in comments
- [ ] Add usage examples in docstrings
- [ ] Update any relevant README files

## Success Criteria
✅ **Must Work:**
- Auto-sizing with 40-character limit
- Horizontal scrollbar when content exceeds view
- Column visibility changes work dynamically
- Method chaining works
- No competing override chains
- All current categorize functionality preserved

✅ **Configuration Example:**
```python
table = CustomTableView_v2()
table.configure(
    auto_size_columns=True,
    max_column_width=40,
    editable_columns=['tags'],
    show_toolbar=True
).set_dataframe(df).show()

# Dynamic changes
table.hide_columns(['balance']).resize_column('details', 50)
```

## Quick Validation Commands
```python
# Test basic functionality
table = CustomTableView_v2()
table.configure(auto_size_columns=True, max_column_width=40)
table.set_dataframe(df)
table.show()

# Test dynamic changes
table.hide_columns(['account'])
table.resize_column('details', 60)
table.auto_resize_columns()
```
