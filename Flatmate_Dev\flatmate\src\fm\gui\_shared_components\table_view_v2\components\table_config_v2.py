"""
Table View Configuration System v2

Provides centralized configuration for table view behavior and appearance.
Follows the app-wide widget pattern for consistent configuration management.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional


@dataclass
class TableConfig:
    """Configuration for table view behavior and appearance.
    
    This class centralizes all table configuration options and provides
    sensible defaults for immediate usability.
    """
    
    # === Column Sizing ===
    auto_size_columns: bool = True
    """Automatically size columns to fit content."""
    
    max_column_width: int = 40
    """Maximum width for columns in characters when auto-sizing."""
    
    min_column_width: int = 8
    """Minimum width for columns in characters."""
    
    column_widths: Dict[str, int] = field(default_factory=dict)
    """Explicit column widths in characters. Overrides auto-sizing for specified columns."""
    
    # === Column Behavior ===
    editable_columns: List[str] = field(default_factory=list)
    """List of column names that should be editable by the user."""
    
    default_visible_columns: Optional[List[str]] = None
    """List of column names to show by default. If None, shows all columns."""
    
    save_column_state: bool = True
    """Whether to save and restore user column width/visibility changes."""
    
    # === Toolbar Configuration ===
    show_toolbar: bool = True
    """Whether to display the toolbar with filter and export controls."""
    
    toolbar_groups: List[str] = field(default_factory=lambda: ['filter', 'column', 'export'])
    """Which toolbar groups to display. Available: 'filter', 'column', 'export'."""
    
    # === Column Name Mapping ===
    column_display_mapping: Optional[Dict[str, str]] = None
    """Mapping from database column names to user-friendly display names."""
    
    # === Appearance ===
    alternating_rows: bool = True
    """Whether to use alternating row colors for better readability."""
    
    show_grid: bool = True
    """Whether to show grid lines between cells."""
    
    selection_mode: str = 'rows'
    """Selection mode: 'rows', 'cells', or 'extended'."""
    
    # === Scrolling Behavior ===
    stretch_last_section: bool = False
    """Whether to stretch the last column to fill available space.
    Set to False to allow horizontal scrolling when content exceeds view."""

    # === Filter Persistence ===
    save_filter_state: bool = True
    """Whether to save and restore filter state between sessions."""

    default_filter_column: str = "details"
    """Default column to filter on when no previous state exists."""

    last_filter_column: Optional[str] = None
    """Last used filter column (set at runtime)."""

    last_filter_pattern: Optional[str] = None
    """Last used filter pattern (set at runtime)."""

    def validate(self):
        """Validate configuration values and raise ValueError for invalid options."""
        if self.max_column_width < self.min_column_width:
            raise ValueError(f"max_column_width ({self.max_column_width}) must be >= min_column_width ({self.min_column_width})")
        
        if self.selection_mode not in ['rows', 'cells', 'extended']:
            raise ValueError(f"selection_mode must be 'rows', 'cells', or 'extended', got '{self.selection_mode}'")
        
        valid_toolbar_groups = {'filter', 'column', 'export'}
        invalid_groups = set(self.toolbar_groups) - valid_toolbar_groups
        if invalid_groups:
            raise ValueError(f"Invalid toolbar groups: {invalid_groups}. Valid groups: {valid_toolbar_groups}")
    
    def get_effective_column_width(self, column_name: str, content_width: int) -> int:
        """Get the effective width for a column considering all configuration.
        
        Args:
            column_name: Name of the column
            content_width: Width needed to fit the column content
            
        Returns:
            Effective width in characters
        """
        # Explicit width overrides everything
        if column_name in self.column_widths:
            return min(self.column_widths[column_name], self.max_column_width)
        
        # Auto-sizing with limits
        if self.auto_size_columns:
            return max(self.min_column_width, min(content_width, self.max_column_width))
        
        # Default fallback
        return min(content_width, self.max_column_width)
    
    def is_column_visible(self, column_name: str, all_columns: List[str]) -> bool:
        """Determine if a column should be visible based on configuration.
        
        Args:
            column_name: Name of the column to check
            all_columns: List of all available column names
            
        Returns:
            True if column should be visible
        """
        if self.default_visible_columns is None:
            return True  # Show all columns by default
        
        return column_name in self.default_visible_columns
    
    def get_display_name(self, column_name: str) -> str:
        """Get the display name for a column.
        
        Args:
            column_name: Database/internal column name
            
        Returns:
            User-friendly display name
        """
        if self.column_display_mapping and column_name in self.column_display_mapping:
            return self.column_display_mapping[column_name]
        
        return column_name
    
    def set_default_visible_columns_for_module(self, module_name: str) -> 'TableConfig':
        """
        Set default visible columns using the centralized Columns registry.

        This is a convenience method to avoid hard-coding column names.

        Args:
            module_name: Name of the module ('categorize', 'reports', etc.)

        Returns:
            self for method chaining
        """
        from fm.core.data_services.standards.columns import Columns
        self.default_visible_columns = Columns.get_default_visible_columns(module_name)
        return self

    def copy(self) -> 'TableConfig':
        """Create a copy of this configuration."""
        return TableConfig(
            auto_size_columns=self.auto_size_columns,
            max_column_width=self.max_column_width,
            min_column_width=self.min_column_width,
            column_widths=self.column_widths.copy(),
            editable_columns=self.editable_columns.copy(),
            default_visible_columns=self.default_visible_columns.copy() if self.default_visible_columns else None,
            save_column_state=self.save_column_state,
            show_toolbar=self.show_toolbar,
            toolbar_groups=self.toolbar_groups.copy(),
            column_display_mapping=self.column_display_mapping.copy() if self.column_display_mapping else None,
            alternating_rows=self.alternating_rows,
            show_grid=self.show_grid,
            selection_mode=self.selection_mode,
            stretch_last_section=self.stretch_last_section,
            save_filter_state=self.save_filter_state,
            default_filter_column=self.default_filter_column,
            last_filter_column=self.last_filter_column,
            last_filter_pattern=self.last_filter_pattern
        )
