# Config System Migration Strategy

*Created: 2025-06-17*
*Status: PLANNING*

## 🎯 **Migration Goals**

Migrate from the current `BaseLocalConfig` + enum system to the new practical config system:
1. **Preserving critical features** from the existing system (override hierarchy)
2. **Eliminating complex enum systems** - use simple strings where needed
3. **Improving developer experience** with usage-based discoverability
4. **Eliminating speculative config** keys entirely
5. **Making config self-documenting** through usage patterns

## 📊 **Current System Analysis**

### **BaseLocalConfig Features to Evaluate**

#### ✅ **Features to RETAIN**

1. **Override Hierarchy** (CRITICAL)
   ```
   Priority Order:
   1. User preferences (~/.flatmate/preferences.yaml)
   2. Component-specific defaults (defaults.yaml)  
   3. Hardcoded defaults (get_defaults())
   ```

2. **YAML File Loading**
   - Component-specific `defaults.yaml` files
   - User preferences from `~/.flatmate/preferences.yaml`
   - Error handling for missing/corrupt files

3. **Simple String Keys**
   - `get_value(key)` with simple string keys
   - No enum complexity - keys defined where used

4. **Core Config Integration**
   - Access to global `core_config` singleton
   - `get_pref()` and `set_pref()` methods

#### ❌ **Features to REPLACE/IMPROVE**

1. **Speculative Defaults**
   - Current: All defaults loaded upfront via `get_defaults()`
   - New: Only keys that are actually `ensure_defaults()` exist

2. **Complex Enum Systems**
   - Current: Separate enum files with speculative key definitions
   - New: Simple string keys defined where they're actually used

3. **Event Bus Direct Access**
   - Current: Direct `global_event_bus` usage
   - New: Use `core.services.logger` instead

## 🚀 **Migration Strategy**

### **Phase 1: Enhanced Base Class (Week 1)**

Create new `EnhancedLocalConfig` base class that combines best of both:

```python
class EnhancedLocalConfig(ABC):
    """Enhanced base config with override hierarchy + source tracking."""
    
    # Subclasses MUST define this
    MODULE_NAME = None  # e.g., "categorize", "reports"
    
    def __init__(self):
        # Runtime enforcement of MODULE_NAME
        if not self.MODULE_NAME:
            raise RuntimeError(f"{self.__class__.__name__} must define MODULE_NAME")
        
        # Initialize tracking
        self._config_values = {}
        self._key_origins = {}
        self._user_prefs = {}
        self._component_defaults = {}
        
        # Load override hierarchy
        self._load_override_hierarchy()
    
    def _load_override_hierarchy(self):
        """Load config hierarchy: hardcoded -> component -> user prefs."""
        # 1. Load component defaults.yaml (if exists)
        # 2. Load user preferences (if exists)  
        # 3. Apply hierarchy when ensure_defaults() is called
    
    def ensure_defaults(self, defaults_dict, source=None):
        """Enhanced ensure_defaults with hierarchy + tracking."""
        # Apply override hierarchy for each key
        # Track source information
        # Only create keys that don't exist in higher priority sources
```

### **Phase 2: Parallel Implementation (Week 2)**

1. **Create Enhanced Base Class**
   - Location: `fm/core/config/enhanced_local_config.py`
   - Implement override hierarchy
   - Add source tracking
   - Maintain backward compatibility

2. **Update Categorize Module**
   - Inherit from `EnhancedLocalConfig`
   - Test all existing functionality
   - Generate comparison YAML files

3. **Create Migration Tests**
   - Test override hierarchy works
   - Test backward compatibility
   - Test new features (source tracking)

### **Phase 3: Gradual Migration (Weeks 3-4)**

1. **Module-by-Module Migration**
   ```
   Priority Order:
   1. categorize (already started)
   2. update_data (uses StandardColumns)
   3. reports
   4. gui components
   ```

2. **Validation at Each Step**
   - Compare old vs new config outputs
   - Test user preference overrides
   - Verify no functionality breaks

### **Phase 4: Cleanup (Week 5)**

1. **Deprecate BaseLocalConfig**
   - Add deprecation warnings
   - Update documentation
   - Plan removal timeline

2. **Update Documentation**
   - New config system guide
   - Migration examples
   - Best practices

## 🔧 **Technical Implementation Details**

### **Override Hierarchy Implementation**

```python
def _apply_override_hierarchy(self, key: str, default_value: Any) -> Any:
    """Apply config override hierarchy for a key."""
    
    # 1. Check user preferences first (highest priority)
    if key in self._user_prefs:
        return self._user_prefs[key]
    
    # 2. Check component defaults.yaml
    if key in self._component_defaults:
        return self._component_defaults[key]
    
    # 3. Use provided default (lowest priority)
    return default_value
```

### **YAML Loading Strategy**

```python
def _load_component_defaults(self) -> Dict[str, Any]:
    """Load component-specific defaults.yaml."""
    defaults_path = self.get_defaults_file_path()
    if defaults_path and defaults_path.exists():
        # Load and return YAML content
        # Handle errors gracefully
        pass
    return {}

def _load_user_preferences(self) -> Dict[str, Any]:
    """Load user preferences from ~/.flatmate/preferences.yaml."""
    prefs_path = Path("~/.flatmate/preferences.yaml").expanduser()
    if prefs_path.exists():
        # Load user prefs
        # Filter for this module's keys
        pass
    return {}
```

### **Backward Compatibility**

```python
# Support both old and new patterns
def get_value(self, key: Union[Enum, str], default: Any = None) -> Any:
    """Backward compatible get_value."""
    key_str = key.value if isinstance(key, Enum) else str(key)
    return self._config_values.get(key_str, default)

def set_value(self, key: Union[Enum, str], value: Any):
    """Backward compatible set_value."""
    key_str = key.value if isinstance(key, Enum) else str(key)
    self._config_values[key_str] = value
```

## ⚠️ **Migration Risks & Mitigation**

### **Risk 1: Breaking Statement Handlers**
- **Risk**: update_data module depends on StandardColumns enum
- **Mitigation**: Migrate update_data carefully, test thoroughly
- **Fallback**: Keep old system running in parallel initially

### **Risk 2: User Preference Overrides**
- **Risk**: Users lose their customizations
- **Mitigation**: Extensive testing of override hierarchy
- **Validation**: Compare before/after config states

### **Risk 3: Performance Impact**
- **Risk**: New system slower than old
- **Mitigation**: Benchmark both systems
- **Optimization**: Cache loaded YAML files

## 📋 **Success Criteria**

- [ ] Override hierarchy works identically to current system
- [ ] All existing config keys continue to work
- [ ] User preferences are preserved and respected
- [ ] Source tracking provides valuable debugging info
- [ ] YAML output is clean and organized
- [ ] No performance regression
- [ ] Statement handlers continue to work
- [ ] Easy migration path for other modules

## 🎯 **Next Steps**

1. **Create EnhancedLocalConfig base class**
2. **Implement override hierarchy**
3. **Test with categorize module**
4. **Create migration validation tests**
5. **Document migration process**

---

*This strategy balances innovation with stability, ensuring we get the benefits of the new system while preserving critical existing functionality.*
