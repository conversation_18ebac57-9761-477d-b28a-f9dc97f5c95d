07:32 PM, 17 Jun 2025

the issue comes when more than one module needs to reference the same config value... and one of hte problems is wading through speculative keys. 
i want to nix that issue.
ideally all modules would simply need import config from their local directory from .config.config import Cat_Config
config = Config
this Config would inherit core/config/base_local_config (as it does I believe) and have access to important items like appp dir  paths by default 

it would aslo inherit the ensure_defaults method

```python
# in the module config class
config.ensure_defaults( {details_column_width:50, other_key: value} )

```

or similar

in the yaml 
we would want a name spaced config value, possibly indented, possibly not 

now, base_local_config already has a default method, we should take a look at that, but we should possibly override it in CatConfig because we will want to test and see what reults we get... 

The point is that the yaml should let us know where the config item was set.

