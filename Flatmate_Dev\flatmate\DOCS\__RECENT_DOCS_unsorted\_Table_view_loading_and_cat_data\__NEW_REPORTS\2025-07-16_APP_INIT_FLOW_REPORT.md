# Application Initialisation and Module Loading Report (Current State)

**Objective**: To document the application's current initialisation sequence, which uses a "lazy loading" or "on-demand" strategy for its UI modules.

**Conclusion**: The application currently implements a **lazy loading** pattern for its modules. While core services and the data cache are prepared at startup, UI modules are created only when they are first navigated to. This results in a fast initial application launch but introduces significant delays during inter-module navigation, such as the 2.3s freeze when opening the "Categorize" module.

---

## Initialisation Flow Analysis

The startup sequence can be broken down into four distinct phases:

### 1. Application Entry Point (`src/fm/main.py`)

The sequence begins when `main.py` is executed.

1.  **`main()` -> `initialize_application()`**: The `main()` function immediately calls `initialize_application()`, which acts as the primary orchestrator for the startup process.
2.  **Core Services**: `initialize_application()` sets up essential services:
    *   **File System**: `AppPaths.ensure_directories()` verifies that all necessary application folders exist.
    *   **Logging**: The custom `log` object is imported, which self-configures upon first use.
    *   **GUI**: A `QApplication` instance is created, styles are applied, and the `MainWindow` is instantiated and shown.
3.  **Coordinator Instantiation**: A `ModuleCoordinator` object is created. This is the central component for managing the application's modules.

### 2. Module Factory Registration (`src/fm/module_coordinator.py`)

This phase sets up the lazy loading mechanism.

1.  **`coordinator.initialize_modules()`**: This method is called from `main.py`.
2.  **Factory Creation**: The method populates the `self.module_factories` dictionary with `lambda` functions (e.g., `'home': lambda: HomePresenter(...)`).
3.  **No Modules Created**: Crucially, **no presenter objects are instantiated at this stage**. The system only stores the *recipes* for creating them later.

### 3. Data Eager Loading (`src/fm/main.py`)

The application eagerly loads its primary dataset to ensure fast data access during operation.

1.  **`DBIOService().initialize_cache()`**: This method is called from `initialize_application()`.
2.  **In-Memory Cache**: It reads the entire transaction database into an in-memory pandas DataFrame. This ensures that modules have high-speed access to data without incurring database read delays.

### 4. Application Start and First Module Creation

This is where the "lazy load" happens.

1.  **`coordinator.start()`**: This method is called, which executes `transition_to('home')`.
2.  **On-Demand Creation**: The `transition_to()` method finds the 'home' factory in the dictionary and **executes the lambda function**. This creates the `HomePresenter` instance for the very first time.
3.  **Initialization**: It then calls the `.initialize()` method on the newly created presenter, which builds and displays the UI.
4.  **Navigation Delay**: When the user navigates to another module (e.g., 'categorize'), this entire creation and initialization process is repeated for that module, causing the noticeable UI freeze.

---

## Key Insight: The Presenter-View Lifecycle and UI Instability

A deeper analysis of the `Presenter` and `View` classes reveals the root cause of the UI instability encountered during previous eager loading attempts. The interaction is as follows:

1.  **Creation (`__init__`)**: When a presenter (e.g., `HomePresenter`) is instantiated, it immediately creates its corresponding view (`HomeView`). The view's `setup_ui()` method is called, which builds all necessary UI widgets (panels, buttons, etc.) and holds them in memory.

2.  **Integration (`initialize`)**: The presenter's `initialize()` method calls the view's `setup_in_main_window()`. This method passes the view's pre-built widgets to the `MainWindow`, which takes ownership of them and displays them in the appropriate panels.

### The Root Cause of Crashes

The instability arises from how Qt manages widget lifecycles.

*   **The Problem**: When transitioning away from a module, the `MainWindow`'s content panels are cleared and repopulated with the new module's view. This "orphans" the widgets from the previous view. Qt's memory management is then free to delete these orphaned C++ objects.
*   **The Crash**: When navigating back to the original module, our code attempts to re-use the same view instance. However, its underlying C++ widgets have already been destroyed, leading to a `RuntimeError: Internal C++ object already deleted`.

### The Path Forward: A New Lifecycle for Eager Loading

A successful eager loading implementation must respect this lifecycle by ensuring widgets are never destroyed. This requires separating one-time setup from per-navigation actions:

*   **One-Time Setup (at startup)**: Create all presenters and their views, build all UI widgets, and connect all signals. This happens only once.
*   **Per-Navigation Action (`transition_to`)**: This method should only be responsible for view *switching*. It will retrieve the pre-built view for the target module and tell the `MainWindow` to display its widgets. The widgets of the previous view will be hidden but not destroyed.
