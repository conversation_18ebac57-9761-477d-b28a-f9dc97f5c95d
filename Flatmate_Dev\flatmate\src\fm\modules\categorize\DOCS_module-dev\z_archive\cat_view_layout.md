# CatView - GUI Layout & Components

*Document Version: 0.1.0*  
*Last Updated: 2025-06-16*

## 1. Overview
`CatView` is the main view component for the Categorise module, responsible for displaying and managing transaction data. It follows the MVP pattern and inherits from `BaseModuleView`.

## 2. File Structure
```
categorize/
├── _view/
│   ├── __init__.py
│   ├── cat_view.py           # Main view class
│   ├── components/           # Reusable UI components
│   │   ├── __init__.py
│   │   ├── filter_bar.py     # Filter controls
│   │   ├── transaction_table.py  # Main table widget
│   │   └── action_buttons.py # Action button panel
│   └── styles/               # QSS stylesheets
```

## 3. Main Layout Structure

### 3.1 Top Bar
```
+----------------------------------------------------------+
| [Date Range Picker] [Account Dropdown] [🔍 Search...]   |
+----------------------------------------------------------+
```
- **Date Range Picker**: Select date range for transactions
- **Account Selector**: Filter by account
- **Search Box**: Global search across all columns

### 3.2 Main Content Area
```
+-------------------+--------------------------------------+
|                   |                                      |
|                   |  +--------------------------------+  |
|                   |  | Date       | Amount | Desc  |...|  |
|                   |  +--------------------------------+  |
|   Left Panel     |  | 2025-06-16 | 42.50  | Store |...|  |
|   (Collapsible)  |  | 2025-06-15 | 15.99  | Cafe  |...|  |
|                   |  | ...        | ...    | ...   |...|  |
|                   |  +--------------------------------+  |
|                   |                                      |
+-------------------+--------------------------------------+
```

### 3.3 Bottom Status Bar
```
+----------------------------------------------------------+
| Showing 1-50 of 1,243 transactions | 5 selected |  ✓ Saved |
+----------------------------------------------------------+
```

## 4. Key Components

### 4.1 Transaction Table (`QTableView`)
- **Model**: Custom `PandasTableModel` extending `QAbstractTableModel`
- **Features**:
  - Sortable columns
  - Resizable columns with persistence
  - Custom delegates for different column types
  - Multi-select with keyboard support

### 4.2 Left Panel
- **Category Tree**: Hierarchical view of categories
- **Saved Filters**: Quick access to common filters
- **Tags Cloud**: Visual representation of most used tags

### 4.3 Context Menus
- **Row Context Menu**: Right-click on transaction row
  - Assign Category
  - Add/Remove Tags
  - Split Transaction
  - Add Note
  - Flag for Review

## 5. Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| Ctrl+F   | Focus search box |
| Ctrl+S   | Save changes |
| Del      | Delete selected transactions |
| Space    | Toggle selection |
| F2       | Edit selected cell |
| Esc      | Cancel edit/Deselect all |

## 6. State Management

### 6.1 View State
- Current sort column/order
- Column widths
- Expanded/collapsed sections
- Active filters

### 6.2 Data State
- Dirty flag for unsaved changes
- Selected items
- Scroll position

## 7. Responsive Behavior
- Left panel collapses to icons on window resize
- Columns can be shown/hidden via view menu
- Toolbar actions adapt to available space

## 8. Styling
- Uses Qt StyleSheets (QSS) for theming
- Follows system color scheme
- Custom styling for different transaction types

## 9. Integration Points
- **Presenter**: `CategorizePresenter` for business logic
- **Models**: `TransactionModel`, `CategoryModel`
- **Services**: `DataService`, `CacheService`

## 10. Future Enhancements
- Split view for comparing transactions
- Bulk edit mode
- Custom column layouts/profiles
- Advanced filtering/sorting presets

---
*Document generated by Flatmate Development Team*
