# DEV NOTES - Categorize Module
---- AI notes ----- 
## 🔄 Latest Session Notes (2025-06-17)

### ✅ Major Progress: Default Columns System Working
- Fixed column display mapping between database names and display names
- Config V2 system now properly integrated with column manager
- Default columns now showing correctly: Date, Details, Amount, Account, Tags

### 🔍 Key Architectural Insights
- **Column Naming Systems**: Found 3 different systems (simple enum, complex dataclass, column manager)
- **Simple vs Complex**: `fm_standard_columns.py` (simple enum) vs `enhanced_columns.py` (over-engineered dataclass)
- **Database vs Display**: Database names (`'date'`, `'details'`) vs Display names (`'Date'`, `'Details'`)
- **Logger System**: Must use bespoke logger (`fm.core.services.logger.Logger`) not Python's logging

### 🐛 Current Issues for Next Session
1. **"DEscription" vs "Details"** - Need to debug display name source
2. **Missing columns** in dropdown - Not all database schema columns appearing
3. **No debug output** when selecting/deselecting columns
4. **No config saving** when column selections change

### 📁 Key Files Modified
- `transaction_view_panel.py` - Added default column logic and config integration
- `column_preferences.py` - Added config system integration
- Enhanced table system working with proper column mapping

--------
my notes , last reviewed 03:18 AM, 18 Jun 2025
## cat_trans module (Original Notes)

- (trans_cat?)

- need reizable columns that open at sensible defaults and remember last setting (module config)

- need side panels to be collapsible

- need to be able to add new tags to transactions

- can manually edit categories

- should be a drop dwon list in the cell with -- add new category - when clicked makes the cell editable, with active cursor in cell, this becomes the new category -saves context changing

- Cell rows should be highlighted when clicked

- Instead of two buttons - left panel should say 
label: load transactions 
option menu - from file
            - from database

After loading there should be an option to 
 label: filter by:
 Option menu: by date range
            - by category
            - by account
            - by tag
            - by amount range
            - by description
            - by notes

 Alternatively or as well, - there could be an option to query the data base for a specific date range in the first place - and then the filters would be applied to that subset

 ### I note there is a "transaction ID"

 which is simply numbered 1 to n in the order they appear in the database.

 How does this work when new transaztions are added ? simply reassigned ?


#### *important* - some fomrats have a unique transaction ID - in this format, this is the only means of being one hundred percent sure a transaction is a duplicate - because tthere is no balance 
- I note that in the current rendering no balance is present - must check if it is in the actual db data
-We need a show all option 
- and or a select collumsn to view option -(check boxes-dynamic -based on current database schema)
- perhaps an option to add custom columns 

### We need an option to export the current view to csv or xlsx (excell format) 

### we need to consider the default column order - its supposed to be based on the order in standards - meaning changing the order their should chage the default order in the data base and here 

### currently no ability to select an entire row and copy it 

## we may need to build a custom view table to be used throughout the app 
pref with a light and dark mode 
ultimately should be customisable or have themes 
For now, we will need to break this out of cat_view.py 
and into a view_components folder (naming convention) 






