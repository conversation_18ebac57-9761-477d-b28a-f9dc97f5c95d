#!/usr/bin/env python3
"""
Test script to verify DataFrame column reordering logic.
"""

import pandas as pd

def test_dataframe_reorder():
    """Test the DataFrame column reordering logic."""
    print("=== DataFrame Reorder Test ===\n")
    
    try:
        from fm.core.data_services.standards.columns import Columns
        
        # Create a sample DataFrame with columns in alphabetical order (simulating the problem)
        alphabetical_data = {
            'account': ['Bank1', 'Bank2', 'Bank3'],
            'amount': [100.0, 200.0, 300.0],
            'balance': [1000.0, 1200.0, 1500.0],
            'category': ['Food', 'Transport', 'Shopping'],
            'date': ['2025-01-01', '2025-01-02', '2025-01-03'],
            'details': ['Purchase 1', 'Purchase 2', 'Purchase 3'],
            'notes': ['Note 1', 'Note 2', 'Note 3'],
            'tags': ['tag1', 'tag2', 'tag3']
        }
        
        df = pd.DataFrame(alphabetical_data)
        print("1. Original DataFrame (alphabetical order):")
        print(f"   Columns: {list(df.columns)}")
        print()
        
        # Get our ordered columns
        ordered_columns = Columns.get_ordered_display_columns('categorize')
        ordered_db_names = [col.db_name for col in ordered_columns]
        print("2. Our desired order:")
        print(f"   Ordered: {ordered_db_names}")
        print()
        
        # Apply the reordering logic from transaction_view_panel.py
        available_ordered_cols = [col for col in ordered_db_names if col in df.columns]
        remaining_cols = [col for col in df.columns if col not in available_ordered_cols]
        final_column_order = available_ordered_cols + remaining_cols
        
        print("3. Reordering logic:")
        print(f"   Available ordered: {available_ordered_cols}")
        print(f"   Remaining cols: {remaining_cols}")
        print(f"   Final order: {final_column_order}")
        print()
        
        # Reorder the DataFrame
        df_reordered = df[final_column_order]
        print("4. Reordered DataFrame:")
        print(f"   Columns: {list(df_reordered.columns)}")
        print()
        
        # Apply display names
        df_with_display_names = Columns.apply_display_names_to_df(df_reordered)
        print("5. After applying display names:")
        print(f"   Columns: {list(df_with_display_names.columns)}")
        print()
        
        # Check if it matches our expected order
        expected_display_order = [col.display_name for col in ordered_columns if col.db_name in df.columns]
        actual_display_order = list(df_with_display_names.columns)
        
        print("6. Verification:")
        print(f"   Expected: {expected_display_order}")
        print(f"   Actual:   {actual_display_order}")
        print(f"   ✅ Match: {expected_display_order == actual_display_order}")
        
        if expected_display_order == actual_display_order:
            print("\n🎉 SUCCESS! DataFrame reordering is working correctly!")
        else:
            print("\n❌ ISSUE: Column order doesn't match expected order")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_dataframe_reorder()
