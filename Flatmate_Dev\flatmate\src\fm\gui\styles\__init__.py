"""
Style management for the application.
Provides functions to load and apply styles, with hooks for future theme/font size support.
"""

from pathlib import Path
from typing import Optional
from PySide6.QtWidgets import QApplication
from ...core.config import config
from ...core.config.keys import Config<PERSON>eys

def load_styles() -> str:
    """
    Load and combine application styles.
    Currently loads theme.qss and style.qss, with support for font size configuration.
    
    Returns:
        str: Combined stylesheet content
        
    Raises:
        FileNotFoundError: If style files are not found
    """
    styles_dir = Path(__file__).parent
    
    # Load base theme and styles
    with open(styles_dir / "theme.qss", 'r') as f:
        theme = f.read()
    with open(styles_dir / "style.qss", 'r') as f:
        style = f.read()
        
    # Apply any dynamic values
    font_size = config.get_value(ConfigKeys.App.BASE_FONT_SIZE, 14)
    combined = theme + "\n" + style
    combined = combined.replace('{{FONT_SIZE}}', str(font_size))
    
    return combined

def apply_styles(app: QApplication) -> None:
    """
    Apply styles to the application.
    
    Args:
        app: The QApplication instance
        
    Raises:
        FileNotFoundError: If style files are not found
    """
    app.setStyleSheet(load_styles())

# Future hooks for dynamic updates
def update_font_size(app: QApplication, size: int) -> None:
    """
    Update application font size.
    Currently a placeholder for future implementation.
    
    Args:
        app: The QApplication instance
        size: New base font size
    """
    # TODO: Implement when needed
    pass

def switch_theme(app: QApplication, theme: str) -> None:
    """
    Switch application theme.
    Currently a placeholder for future implementation.
    
    Args:
        app: The QApplication instance
        theme: Theme name (e.g., 'light', 'dark')
    """
    # TODO: Implement when needed
    pass
