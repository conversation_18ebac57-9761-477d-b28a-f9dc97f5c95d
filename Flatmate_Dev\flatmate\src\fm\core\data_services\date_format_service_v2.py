"""
Centralized date handling service.

This service provides comprehensive date handling including:
- Flexible date parsing from various formats
- Standardization to ISO format (YYYY-MM-DD)
- Conversion between display formats
- Excel date number support
"""

import re
from datetime import datetime, date
from enum import Enum
from typing import Union, Optional, Any, List

import pandas as pd
from dateutil import parser as date_parser

from fm.core.services.logger import log


class DateDisplayFormat(Enum):
    """Standard date display formats for different locales."""
    
    # New Zealand / UK / Australia style
    DD_MM_YYYY = "%d/%m/%Y"
    DD_MM_YY = "%d/%m/%y"
    
    # US style
    MM_DD_YYYY = "%m/%d/%Y"
    MM_DD_YY = "%m/%d/%y"
    
    # ISO style (same as database)
    YYYY_MM_DD = "%Y-%m-%d"
    
    # European style
    DD_MM_YYYY_DOTS = "%d.%m.%Y"
    
    # Verbose styles
    DD_MMM_YYYY = "%d %b %Y"  # 25 Dec 2023
    MMM_DD_YYYY = "%b %d, %Y"  # Dec 25, 2023


class DateFormatService:
    """Service for handling date parsing, formatting, and standardization."""
    
    # Common date formats to try when parsing
    COMMON_FORMATS = [
        "%Y-%m-%d", "%d/%m/%Y", "%m/%d/%Y", "%d-%m-%Y", "%m-%d-%Y",
        "%d.%m.%Y", "%Y/%m/%d", "%d %b %Y", "%b %d, %Y"
    ]
    
    @staticmethod
    def is_excel_date(value: Any) -> bool:
        """Check if the value is an Excel date number."""
        return isinstance(value, (int, float)) and 0 < value < 100000
    
    @classmethod
    def parse_date(
        cls, 
        date_input: Any, 
        format_hint: Optional[str] = None,
        dayfirst: bool = True,
        yearfirst: bool = False
    ) -> Optional[date]:
        """
        Parse a date from various input types with flexible format detection.
        
        Args:
            date_input: Date to parse (str, int, float, date, datetime, pd.Timestamp)
            format_hint: Optional format string to try first (e.g., "%d/%m/%Y")
            dayfirst: Whether to interpret the first value as day (default: True for NZ dates)
            yearfirst: Whether to interpret the first value as year
            
        Returns:
            date object or None if parsing fails
        """
        # 1. Main case: String input (most common case first)
        if isinstance(date_input, str):
            date_str = date_input.strip()
            if not date_str:
                return None
                
            # 1.1 First try with format_hint if provided
            if format_hint:
                try:
                    return datetime.strptime(date_str, format_hint).date()
                except ValueError:
                    log.debug(f"Format hint '{format_hint}' failed. Falling back to flexible parsing.")
            
            ##  1.2 Try flexible parsing using hint as a guide 
            try:
                if format_hint:
                    # If 'd' is in first 2 chars, it's day-first, otherwise year-first
                    df = 'd' in format_hint.lower()[:2]
                    yf = not df
                else:
                    df, yf = dayfirst, yearfirst
                    
                return date_parser.parse(date_str, dayfirst=df, yearfirst=yf).date()
            except (ValueError, OverflowError):
                log.debug("Flexible parser failed. Falling back to common formats.")
                
                # 1.3 Try common formats as last resort
                for fmt in cls.COMMON_FORMATS:
                    try:
                        return datetime.strptime(date_str, fmt).date()
                    except ValueError:
                        continue
                        
                log.error(f"All parsing methods failed for date string: '{date_str}'")
                return None
        
        # 2. Handle non-string types and edge cases
        if pd.isna(date_input) or not date_input:
            return None
        if isinstance(date_input, (datetime, pd.Timestamp)):
            return date_input.date()
        if isinstance(date_input, date):
            return date_input
        if isinstance(date_input, (int, float)) and cls.is_excel_date(date_input):
            try:
                return (datetime(1899, 12, 30) + pd.Timedelta(days=float(date_input))).date()
            except (ValueError, OverflowError):
                log.error(f"Failed to convert Excel date: {date_input}")
        
        # If we get here, it's an unsupported type
        log.error(f"Unsupported date input type: {type(date_input)}")
        return None
    
    @classmethod
    def standardize_date(
        cls, 
        date_input: Any, 
        output_format: str = "%Y-%m-%d",
        format_hint: Optional[str] = None,
        **parse_kwargs
    ) -> Optional[str]:
        """
        Standardize a date to the specified format.
        
        Args:
            date_input: Date to standardize
            output_format: Format string for output (default: ISO format)
            format_hint: Optional format string to try first (e.g., "%d/%m/%Y")
            **parse_kwargs: Additional arguments for parse_date()
            
        Returns:
            Formatted date string or None if parsing fails
        """
        parsed = cls.parse_date(date_input, format_hint=format_hint, **parse_kwargs)
        if parsed:
            return parsed.strftime(output_format)
        return None
    
    @classmethod
    def standardize_date_column(
        cls,
        series: pd.Series,
        output_format: str = "%Y-%m-%d",
        format_hint: Optional[str] = None,
        **parse_kwargs
    ) -> pd.Series:
        """
        Standardize a pandas Series containing dates.
        
        Args:
            series: Input Series with dates
            output_format: Desired output format (default: ISO format)
            format_hint: Optional format string to try first (e.g., "%d/%m/%Y")
            **parse_kwargs: Additional arguments for standardize_date()
            
        Returns:
            Series with standardized date strings
        """
        return series.apply(
            lambda x: cls.standardize_date(x, output_format, format_hint=format_hint, **parse_kwargs)
        )
    
    @classmethod
    def format_date_for_display(
        cls, 
        date_input: Any, 
        display_format: Optional[Union[str, DateDisplayFormat]] = DEFAULT_DISPLAY_FORMAT,
        **parse_kwargs
    ) -> str:
        """
        Format a date for display, using either the specified format or the user's preferred format.
        
        Args:
            date_input: Date to format (str, date, datetime, or pd.Timestamp)
            display_format: Optional target format. If None, uses user's preferred format
            **parse_kwargs: Additional arguments for parse_date()
            
        Returns:
            Formatted date string or empty string if parsing fails
            
        Examples:
            # Using user's preferred format
            formatted = DateFormatService.format_date_for_display("2023-01-15")
            
            # Using specific format
            formatted = DateFormatService.format_date_for_display(
                "2023-01-15", 
                DateDisplayFormat.DD_MM_YYYY
            )
        """
        # Get format - use provided or fall back to user's preference
        if display_format is None:
            fmt = cls.get_user_date_format() or cls.DEFAULT_DISPLAY_FORMAT
        
        if isinstance(display_format, DateDisplayFormat):
            fmt = display_format.value
        else:
            fmt = display_format or cls.DEFAULT_DISPLAY_FORMAT
            
        parsed_date = cls.parse_date(date_input, **parse_kwargs)
        if parsed_date:
            return parsed_date.strftime(fmt)
        return ""



    # Default format for backward compatibility
    DEFAULT_DISPLAY_FORMAT = DateDisplayFormat.DD_MM_YYYY


    def _show_deprecation_warning(old_name: str, new_name: str) -> None:
        """Helper to show deprecation warning using custom logger."""
        message = f"'{old_name}' is deprecated and will be removed in a future version. Use '{new_name}' instead."
        
        if Logger is not None:
            # Use our custom logger if available
            log.warning(
                message=message,
                module="date_format_service"
            )
        else:
            # Fallback to standard warnings
            warnings.warn(
                message,
                DeprecationWarning,
                stacklevel=3
            )

   

    def format_dataframe_dates(
        df: pd.DataFrame, 
        date_columns: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """Legacy function for backward compatibility.
        
        .. deprecated:: 2.0.0
        Use :class:`DateFormatService.standardize_date_column` for individual columns
        or :class:`DateFormatService.format_date` with DataFrame.apply() for multiple columns.
        
        Formats date columns in a DataFrame for display.
        """
        _show_deprecation_warning(
            'format_dataframe_dates',
            'DateFormatService.standardize_date_column() or DateFormatService.format_date() with DataFrame.apply()'
        )
        if df.empty:
            return df.copy()
            
        display_df = df.copy()
        
        # Auto-detect date columns if not specified
        if date_columns is None:
            date_columns = []
            for col in df.columns:
                col_lower = str(col).lower()
                if any(d in col_lower for d in ['date', 'time', 'created', 'modified']):
                    date_columns.append(col)
        
        # Format each date column
        for col in date_columns:
            if col in display_df.columns:
                display_df[col] = display_df[col].apply(
                    lambda x: DateFormatService.format_date(x, DEFAULT_DISPLAY_FORMAT)
                )
        
        return display_df

    def parse_display_date_to_database(
        date_str: str, 
        input_format: Optional[DateDisplayFormat] = None
    ) -> Optional[str]:
        """Legacy function for backward compatibility.
        
        .. deprecated:: 2.0.0
        Use :class:`DateFormatService.standardize_date` instead.
        
        Parses a user-entered date string to ISO format.
        """
        # ! should not be neccesary date columns are not editable 
        _show_deprecation_warning(
            'parse_display_date_to_database',
            'DateFormatService.standardize_date()'
        )
        if not date_str or pd.isna(date_str):
            return None
            
        date_str = str(date_str).strip()
        if not date_str:
            return None
            
        # The new service uses flexible parsing. We assume day-first for legacy calls.
        # The original input_format is no longer used directly.
        return DateFormatService.standardize_date(date_str, dayfirst=True)

    def get_user_date_format() -> DateDisplayFormat:
        """Legacy function for backward compatibility.
        
        .. deprecated:: 2.0.0
        Directly use :class:`DateDisplayFormat` constants instead.
        """
        _show_deprecation_warning(
            'get_user_date_format',
            'DateDisplayFormat constants directly'
        )
        # Try to get from config, fallback to default
        try:
            from fm.core.config.config import config
            format_str = config.get_value('display.date_format', DEFAULT_DISPLAY_FORMAT.value)
            for fmt in DateDisplayFormat:
                if fmt.value == format_str:
                    return fmt
        except ImportError:
            pass
        return DEFAULT_DISPLAY_FORMAT

    def set_user_date_format(date_format: DateDisplayFormat) -> None:
        """Legacy function for backward compatibility.
        
        .. deprecated:: 2.0.0
        This function will be removed in a future version as date format
        should be handled at the application level.
        """
        _show_deprecation_warning(
            'set_user_date_format',
            'Application-level date format configuration'
        )
        try:
            from fm.core.config.config import config
            config.set_value('display.date_format', date_format.value)
        except ImportError:
            pass  # Silently fail if config not available

