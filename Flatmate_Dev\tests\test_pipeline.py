import os
import sys
from pprint import pprint

# Add the project src to the Python path to allow for absolute imports
project_root = r'c:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src'
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from fm.modules.update_data.utils.dw_director import dw_director

def test_run_pipeline():
    base_path = r'c:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate'
    
    # Define the job sheet for the test run
    job_sheet = {
        "filepaths": [
            os.path.join(base_path, r'test_CSVs\New_Bank_CSV_Formats\asb_bank_csv\Export20240325231257.csv')
        ],
        "save_folder": os.path.join(base_path, r'test_CSVs\output'),
        "update_database": True
    }

    # Ensure the save folder exists
    if not os.path.exists(job_sheet["save_folder"]):
        os.makedirs(job_sheet["save_folder"])

    print("--- Running End-to-End Pipeline Test ---")
    pprint(job_sheet)
    
    # Execute the director
    result = dw_director(job_sheet)
    
    print("\n--- Test Result ---")
    pprint(result)
    print("--- Test Complete ---")

if __name__ == "__main__":
    test_run_pipeline()
