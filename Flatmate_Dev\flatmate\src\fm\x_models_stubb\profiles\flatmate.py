from typing import Dict, Any
from .person import PersonProfile

class FlatmateProfile(PersonProfile):
    """Profile for flatmates with rental and preference information."""
    
    def __init__(self, profile_id: str = None):
        super().__init__(profile_id)
        self.property_id: str = ""  # ID of the property they're renting
        self.room_number: str = ""
        self.rent_amount: float = 0.0
        self.lease_start_date: str = ""
        self.lease_end_date: str = ""
        self.deposit_amount: float = 0.0
        self.rent_payment_day: int = 1  # Day of month rent is due
        self.preferences = {
            'quiet_hours': [],  # List of time ranges
            'cleaning_schedule': "",
            'guest_policy': "",
            'shared_items': [],  # List of items shared with others
            'allergies': [],
            'dietary_restrictions': []
        }
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert the flatmate profile to a dictionary."""
        data = super().to_dict()
        data.update({
            'property_id': self.property_id,
            'room_number': self.room_number,
            'rent_amount': self.rent_amount,
            'lease_start_date': self.lease_start_date,
            'lease_end_date': self.lease_end_date,
            'deposit_amount': self.deposit_amount,
            'rent_payment_day': self.rent_payment_day,
            'preferences': self.preferences
        })
        return data
    
    def from_dict(self, data: Dict[str, Any]) -> None:
        """Load flatmate profile data from a dictionary."""
        super().from_dict(data)
        self.property_id = data.get('property_id', '')
        self.room_number = data.get('room_number', '')
        self.rent_amount = data.get('rent_amount', 0.0)
        self.lease_start_date = data.get('lease_start_date', '')
        self.lease_end_date = data.get('lease_end_date', '')
        self.deposit_amount = data.get('deposit_amount', 0.0)
        self.rent_payment_day = data.get('rent_payment_day', 1)
        self.preferences = data.get('preferences', {
            'quiet_hours': [],
            'cleaning_schedule': "",
            'guest_policy': "",
            'shared_items': [],
            'allergies': [],
            'dietary_restrictions': []
        })
