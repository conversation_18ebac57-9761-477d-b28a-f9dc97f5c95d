# Categorize Module Configuration
# Auto-generated from actual usage
# Organized by source file and function/class

# === modules/categorize/config/test_new_config.py ===

# __init__:
display:
  header_height: 30
  row_height: 25
  show_grid_lines: True
  table_margin: 10

# configure_sorting:
table:
  allow_multi_sort: True
  default_sort_column: 'date'
  sort_ascending: False

# initialize:
display:
  show_filter_panel: True
filters:
  default_days: 30
  remember_last_filter: True
  show_advanced: False

# initialize_keyboard_shortcuts:
shortcuts:
  delete_key: 'Delete'
  filter_key: 'Ctrl+F'
  refresh_key: 'F5'
  save_key: 'Ctrl+S'

# setup_categorize_presenter:
data:
  auto_refresh_seconds: 30
  page_size: 100
performance:
  lazy_load: True

# setup_columns:
display:
  amount_width: 120
  category_width: 150
  date_width: 100
  description_width: 40

# setup_layout:
display:
  border_width: 1
  scroll_bar_width: 15

