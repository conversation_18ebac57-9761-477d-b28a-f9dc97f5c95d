#!/usr/bin/env python3
"""
Test bank statement handlers with real CSV files.
"""

import os
import sys
import traceback
import warnings
import datetime
from pathlib import Path
from typing import List, Dict, Optional, Any

# Set console output to UTF-8
if sys.stdout.encoding != 'utf-8':
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

import pandas as pd

# Global flag to control pandas warnings
SHOW_PANDAS_WARNINGS = False

# Configure pandas warnings
if not SHOW_PANDAS_WARNINGS:
    warnings.filterwarnings('ignore', category=pd.errors.SettingWithCopyWarning)
    warnings.filterwarnings('ignore', category=pd.errors.PerformanceWarning)

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import project logger and handlers
from fm.core.services.logger import log
from fm.modules.update_data.utils.statement_handlers._handler_registry import STATEMENT_HANDLERS

# Log file will be created automatically in ~/.flatmate/logs/
log.info("Starting test session")

class TestResult:
    def __init__(self, handler_name: str, filepath: Path):
        self.handler_name = handler_name
        self.filepath = filepath
        self.status = "❌ ERROR"
        self.error = ""
        self.row_count = 0
        self.columns = []
        self.missing_required = []
        self.sample_data = None
        self.debug_logs = []  # Store debug logs for this test
        
    def add_debug(self, message: str):
        """Add a debug message to be shown only if the test fails"""
        self.debug_logs.append(message)
        log.debug(message)
    
    def to_dict(self) -> dict:
        return {
            'Handler': self.handler_name,
            'File': self.filepath.name,
            'Status': self.status,
            'Error': self.error or ''
        }

def get_required_columns() -> List[str]:
    """Return list of required columns that must be present in the output."""
    return []  # Added empty list to fix syntax error

def get_csv_files() -> List[Path]:
    """Get all CSV files from the test directory, ensuring no duplicates."""
    test_dir = Path(__file__).parent / "test_CSVs" / "test_csvs_ALL_TYPES_~50rows_data"
    

    
    # Find all CSV files (case-insensitive)
    csv_files = []
    seen = set()
    
    # Recursively find all files ending in .csv (case-insensitive)
    for file in test_dir.rglob('*.[cC][sS][vV]'):
        if file.name.lower() not in seen:
            seen.add(file.name.lower())
            csv_files.append(file)
    
    log.info(f"Found {len(csv_files)} CSV files to test.")
    
    return sorted(csv_files, key=lambda x: x.name.lower())



def _execute_handler_test(handler_class, filepath: Path) -> 'TestResult':
    """Test a single handler against a file."""
    handler_name = handler_class.__name__
    result = TestResult(handler_name, filepath)
        
    try:
        # Create handler instance once and reuse it
        handler = handler_class()
        

        
        # Process the file using the new API
        try:
            processed_df = handler.process_file(str(filepath))
            if processed_df is None:
                result.status = "❌ PROCESS ERROR"
                result.error = "Processing returned None"
                return result

            result.row_count = len(processed_df)
            result.columns = processed_df.columns.tolist()
            
            # Save the processed output
            output_dir = Path(__file__).parent / 'output'
            output_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = output_dir / f"{timestamp}_{handler_name}_{filepath.stem}_processed.csv"
            processed_df.to_csv(output_file, index=False)
            result.add_debug(f"Saved processed output to {output_file}")
            
            # Check required columns
            required = {'date', 'amount', 'details'}
            missing = required - set(str(col).lower() for col in processed_df.columns)
            if missing:
                result.status = f"❌ MISSING: {', '.join(missing)}"
            else:
                result.status = "✅ SUCCESS"
                result.sample_data = processed_df.head().to_dict(orient='records')
                
        except Exception as e:
            result.status = "❌ ERROR"
            result.error = str(e)
    
    except Exception as e:
        error_msg = str(e).split('\n')[0]
        result.status = f"❌ ERROR: {error_msg[:50] + '...' if len(error_msg) > 50 else error_msg}"
        log.error(f"Error processing {filepath.name} with {handler_class.__name__}: {e}")
        log.debug(traceback.format_exc())
        result.error = str(e)
    
    return result

def print_section(title: str, char: str = "="):
    """Print a section header"""
    log.info(f"\n{char * 10} {title} {char * 10}")

def run_tests(show_pandas_warnings: bool = True):
    """
    Run all tests.
    
    Args:
        show_pandas_warnings: If True, shows pandas warnings. Defaults to True.
    """
    global SHOW_PANDAS_WARNINGS
    SHOW_PANDAS_WARNINGS = show_pandas_warnings
    
    csv_files = get_csv_files()
    if not csv_files:
        return

    results: List[TestResult] = []
    
    print_section(f"TESTING {len(csv_files)} FILES", "=")
    
    for filepath in csv_files:
        try:
            # Identify all handlers that can handle the file
            matching_handlers = []
            for handler_class in STATEMENT_HANDLERS:
                if handler_class.can_handle_file(str(filepath)):
                    matching_handlers.append(handler_class)
            
            handler_names = [h.__name__ for h in matching_handlers]

            # Case 1: No handler found
            if not matching_handlers:
                result = TestResult("None", filepath)
                result.status = "⚠️ NO HANDLER"
                log.warning(f"No handler found for {filepath.name}")
                results.append(result)
                log.info(f"⚠️  NO HANDLER for '{filepath.name}'")

            # Case 2: Exactly one handler found (Ideal case)
            elif len(matching_handlers) == 1:
                handler_class = matching_handlers[0]
                log.info(f"Testing {handler_class.__name__} on '{filepath.name}'")
                result = _execute_handler_test(handler_class, filepath)
                results.append(result)
                log.info(f"{result.status} - {handler_class.__name__} on '{filepath.name}'")

            # Case 3: Multiple handlers found (Ambiguous)
            else:
                result = TestResult("Multiple", filepath)
                result.status = "❌ AMBIGUOUS"
                result.error = f"Multiple handlers match: {', '.join(handler_names)}"
                results.append(result)
                log.error(f"❌ AMBIGUOUS: '{filepath.name}' matches handlers: {', '.join(handler_names)}")
        except Exception as e:
            log.error(f"\n❌ CRITICAL ERROR processing file: {filepath.name}")
            log.debug(traceback.format_exc())
            # Create a result object to mark the failure
            result = TestResult("CRASH", filepath)
            result.error = str(e)
            results.append(result)

    
    # Print summary
    print_section("TEST SUMMARY", "=")
    
    # Group results by handler
    handler_groups = {}
    for result in results:
        if result and result.handler_name not in handler_groups:
            handler_groups[result.handler_name] = []
        if result:
            handler_groups[result.handler_name].append(result)
    
    # Log the results
    log.info("\n" + "=" * 80)
    log.info("TEST RESULTS".center(80))
    log.info("=" * 80)
    
    any_results = False
    
    for handler_name, handler_results in sorted(handler_groups.items()):
        # Only show handlers with relevant results
        relevant_results = [r for r in handler_results if r and not r.status.startswith("⏩")]
        if not relevant_results:
            continue
            
        any_results = True
        success = [r for r in relevant_results if "SUCCESS" in r.status]
        warning = [r for r in relevant_results if r.status.startswith("⚠️")]
        failed = [r for r in relevant_results if r not in success and r not in warning]
        
        log.info(f"\n🔹 {handler_name}:")
        
        if success:
            log.info(f"   ✅ {len(success)} file{'s' if len(success) > 1 else ''} processed successfully")
            for result in success:
                log.info(f"      • {result.filepath.name} ({result.row_count} rows)")
        
        if warning:
            log.info(f"\n   ⚠️  {len(warning)} issue{'s' if len(warning) > 1 else ''}:")
            for result in warning:
                status = result.status.replace("⚠️", "").strip()
                if result.error:
                    status += f": {result.error}"
                log.info(f"  • {result.handler_name} - {result.filepath.name}: {status}")
                if result.error:
                    log.info(f"        Error: {result.error}")
                
        if failed:
            log.info(f"\n   ❌ {len(failed)} failure{'s' if len(failed) > 1 else ''}:")
            for result in failed:
                log.info(f"      • {result.filepath.name}")
                if result.error:
                    log.info(f"        Error: {result.error}")
    
    if not any_results:
        log.info("\nNo test results to display. Check your test files and handlers.")
        return
    
    # Print overall stats
    all_expected = [r for r in results if r and not r.status.startswith("⏩")]
    if all_expected:
        success_count = sum(1 for r in all_expected if "SUCCESS" in r.status)
        success_pct = (success_count / len(all_expected)) * 100 if all_expected else 0
        
        log.info("\n" + "-"*80)
        log.info(f"\n{' ' * 4}🎯 OVERALL: {success_count}/{len(all_expected)} tests passed ({success_pct:.0f}%)")
        
        if success_pct < 100:
            failed = [r for r in all_expected if "SUCCESS" not in r.status]
            log.warning("\nIssues to investigate:")
            for result in failed:
                status = result.status.replace("⚠️", "").strip()
                if result.error:
                    status += f": {result.error}"
                log.warning(f"  • {result.handler_name} - {result.filepath.name}: {status}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Test bank statement handlers with real CSV files.')
    parser.add_argument('--show-pandas-warnings', action='store_true',
                      help='Show pandas warnings (default: False)')
    args = parser.parse_args()
    
    try:
        run_tests(show_pandas_warnings=args.show_pandas_warnings)
    except Exception as e:
        print(f"\n❌ Error running tests: {e}", file=sys.stderr)
        sys.exit(1)
