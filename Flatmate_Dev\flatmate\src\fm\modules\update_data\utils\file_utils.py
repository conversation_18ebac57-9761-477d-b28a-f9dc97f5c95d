"""
Utility functions for file operations in the Update Data module.
"""

import os
from typing import Optional

import pandas as pd

from fm.core.services.logger import log


import csv

def load_csv_to_df(filepath: str) -> Optional[pd.DataFrame]:
    """
    Load a CSV file as a raw DataFrame, handling jagged files robustly.

    This function pre-scans the CSV to find the maximum number of columns
    and instructs pandas to load the file with that column count, padding
    shorter rows with NaN. This ensures that files with inconsistent metadata
    and data rows can be loaded without a ParserError.

    Args:
        filepath: Path to the CSV file

    Returns:
        Raw DataFrame with integer columns, or None if loading fails.
    """
    if not os.path.exists(filepath) or not os.path.isfile(filepath):
        log.error(f"File does not exist: {filepath}")
        return None

    try:
        # Pre-scan to find the max number of columns for robust parsing
        with open(filepath, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            max_cols = max(len(row) for row in reader)

        # Read with pandas, providing column names to handle jaggedness
        df = pd.read_csv(
            filepath,
            header=None,
            names=range(max_cols),
            engine='python',
            sep=',',
            skip_blank_lines=False,
            quotechar='"',
            escapechar='\\'
        )

        # Store metadata in attrs
        df.attrs['filepath'] = filepath
        df.attrs['filename'] = os.path.basename(filepath)

        # For backward compatibility (these should be deprecated)
        df.filepath = filepath
        df.filename = os.path.basename(filepath)

        return df

    except Exception as e:
        log.error(f"Error loading raw CSV from {filepath}: {str(e)}")
        return None
