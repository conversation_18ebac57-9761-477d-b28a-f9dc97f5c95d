# Custom Title-Bar Maximise / Restore Bug – Analysis & Fix

> Flatmate ‑ June 2025

## Symptoms

| State | User Observation |
|-------|------------------|
| Click 1 (maximise) | Window maximises, **icon does not switch** to restore-down. |
| Click 2 | Icon finally changes, but **nothing else happens**. |
| Click 3 | Window restores. |
| Maximised geometry | Bottom of central table (horizontal scroll-bar) falls off-screen; window not resizable in restored state. |

## Root Causes

1. **Early state check**  
   `MainWindow.changeEvent()` read `self.windowState()` *before* Qt completed the transition.  
   ```python
   if event.type() == QEvent.WindowStateChange:
       maximized = bool(self.windowState() & Qt.WindowMaximized)  # too early
   ```
   Result: the button was updated with the **old** state → two clicks needed.

2. **Forced geometry**  
   The handler also overrode <PERSON>t’s maximised rectangle:
   ```python
   if maximized:
       self.setGeometry(self.screen().availableGeometry())
   ```
   • Disabled manual resizing afterwards (window fixed to that rect).  
   • Double-counted title-bar height, so the central widget slipped down – hiding the horizontal scroll-bar.

## The Fix

### 1  Let Qt finish first
Call `super().changeEvent(event)` **before** querying the state:
```python
if event.type() == QEvent.WindowStateChange:
    # let Qt apply the new state
    super().changeEvent(event)
    maximized = self.isMaximized()
```

### 2  Remove geometry override
We trust the window manager to supply the correct maximised rectangle – no manual `setGeometry()` needed.

### 3  Synchronise the button
Inform the title-bar controls immediately after we know the *final* state:
```python
self.title_bar.window_controls.update_for_state(maximized)
```

### 4  Simpler toggle logic
`WindowControlsGroup._toggle_maximize()` now only delegates to Qt:
```python
if self.parent.isMaximized():
    self.parent.showNormal()
else:
    self.parent.showMaximized()
```
All visual updates are centralised in the `changeEvent` flow above.

## Result
* One-click maximise / restore with correct icons.
* Normal state is freely resizable again.
* Maximised window fits the available screen area ⇒ scroll-bar remains visible.

---
*Prepared automatically by the AI pair-programmer.*
