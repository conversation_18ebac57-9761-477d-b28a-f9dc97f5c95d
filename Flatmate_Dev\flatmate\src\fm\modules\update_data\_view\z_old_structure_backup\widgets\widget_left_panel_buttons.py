"""
Left Panel Buttons Widget for Update Data Module.

Provides all buttons and controls for the left panel.
"""

from PySide6.QtCore import Signal
from PySide6.QtWidgets import (QComboBox, QLabel, QPushButton, QVBoxLayout,
                               QWidget)

from ...ud_types import SaveOptions, SourceOptions


class LeftPanelButtonsWidget(QWidget):
    """
    Widget containing all controls for the Update Data left panel.
    
    Signals:
    - publish_source_select_requested: Publishes source selection type
    - publish_save_select_requested: Publishes when save location requested
    - publish_process_clicked: Publishes when process button clicked
    - publish_cancel_clicked: Publishes when cancel button clicked
    - publish_source_option_changed: Publishes source option changes
    - publish_save_option_changed: Publishes save option changes
    """
    
    publish_source_select_requested = Signal(str)
    publish_save_select_requested = Signal()
    publish_process_clicked = Signal()
    publish_cancel_clicked = Signal()
    publish_source_option_changed = Signal(str)
    publish_save_option_changed = Signal(str)
    
    def __init__(self, parent=None):
        """Initialize the left panel buttons widget."""
        super().__init__(parent)
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Title
        self.title = QLabel("Update Data")
        self.title.setObjectName("heading")
        layout.addWidget(self.title)
        
        # Source Files Section
        self.source_label = QLabel("1. Source Files")
        self.source_label.setObjectName("subheading")
        layout.addWidget(self.source_label)
        
        self.source_combo = QComboBox()
        self.source_combo.addItems([e.value for e in SourceOptions])
        self.source_combo.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)
        layout.addWidget(self.source_combo)
        
        self.source_select_btn = QPushButton("Select...")
        self.source_select_btn.setProperty("type", "select_btn")
        layout.addWidget(self.source_select_btn)
        
        # Save Location Section
        self.save_label = QLabel("2. Save Location")
        self.save_label.setObjectName("subheading")
        layout.addWidget(self.save_label)
        
        self.save_combo = QComboBox()
        self.save_combo.addItems([e.value for e in SaveOptions])
        self.save_combo.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)
        layout.addWidget(self.save_combo)
        
        self.save_select_btn = QPushButton("Select...")
        self.save_select_btn.setProperty("type", "select_btn")
        layout.addWidget(self.save_select_btn)
        
        # Process Section
        self.process_label = QLabel("3. Process")
        self.process_label.setObjectName("subheading")
        layout.addWidget(self.process_label)
        
        self.process_btn = QPushButton("Process")
        self.process_btn.setProperty("type", "action_btn")
        layout.addWidget(self.process_btn)
        
        # Add spacer
        layout.addStretch()
        
        # Cancel button at bottom
        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.setProperty("type", "exit_btn")
        layout.addWidget(self.cancel_btn)
    
    def _connect_signals(self):
        """Connect widget signals."""
        # Source selection
        self.source_combo.currentTextChanged.connect(self.publish_source_option_changed.emit)
        self.source_select_btn.clicked.connect(
            lambda: self.publish_source_select_requested.emit(self.source_combo.currentText())
        )
        
        # Save location
        self.save_combo.currentTextChanged.connect(self.publish_save_option_changed.emit)
        self.save_select_btn.clicked.connect(self.publish_save_select_requested.emit)
        
        # Process and cancel
        self.process_btn.clicked.connect(self.publish_process_clicked.emit)
        self.cancel_btn.clicked.connect(self.publish_cancel_clicked.emit)
    
    def set_save_select_enabled(self, enabled: bool):
        """Enable/disable save select button."""
        self.save_select_btn.setEnabled(enabled)
    
    def set_exit_mode(self):
        """Set UI to exit mode (after displaying data)."""
        # Should the process button be changed to view?
        self.cancel_btn.setVisible(True)
    
    def set_process_mode(self):
        """Set UI to process mode (initial state)."""
        self.process_btn.setText("Process Files")
        self.process_btn.setProperty("context", "process")
        self.cancel_btn.setVisible(True)
        self.cancel_btn.setText("Exit")
    
    def get_save_option(self) -> str:
        """Get the current save location option."""
        return self.save_combo.currentText()
