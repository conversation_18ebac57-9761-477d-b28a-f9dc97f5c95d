#!/usr/bin/env python3
"""
File Date Renamer

A utility script to rename files by adding a date prefix from their modification or creation time.
Accepts files/folders via a GUI, command-line arguments, or drag-and-drop.
"""

import sys
import argparse
import tkinter as tk
from tkinter import filedialog, messagebox
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Tuple, Optional, Literal

# --- Configuration Management ---

CONFIG_FILE = Path(__file__).parent / "config.yml"

def _check_dependencies():
    """Internal check for PyYAML, only called when GUI needs it."""
    try:
        import yaml
    except ImportError:
        messagebox.showerror(
            "Dependency Missing",
            "The PyYAML package is required for this script to remember the last used folder.\n\n"
            "Please install it by running:\n'pip install pyyaml'\n\n"
            "If you are using a virtual environment, make sure it is activated first."
        )
        return False
    return True

def load_config() -> Dict:
    """Load configuration from the YAML file."""
    import yaml
    if CONFIG_FILE.exists():
        with open(CONFIG_FILE, 'r') as f:
            try:
                return yaml.safe_load(f) or {}
            except yaml.YAMLError:
                return {}
    return {}

def save_config(config: Dict):
    """Save configuration to the YAML file."""
    import yaml
    with open(CONFIG_FILE, 'w') as f:
        yaml.dump(config, f, default_flow_style=False)

def get_last_path() -> Optional[str]:
    """Get the last used path from the config."""
    return load_config().get("last_path")

def set_last_path(path: str):
    """Set the last used path in the config."""
    config = load_config()
    config["last_path"] = str(Path(path).parent if Path(path).is_file() else path)
    save_config(config)

# --- Core Logic ---

def get_files_from_folder(folder_path: Path) -> List[Path]:
    """Recursively get all files from a folder."""
    return [p for p in folder_path.rglob('*') if p.is_file()]

def get_file_date(file_path: Path, date_type: str) -> str:
    """Get the specified date of a file in YYYY-MM-DD format."""
    ts = file_path.stat().st_ctime if date_type == 'created' else file_path.stat().st_mtime
    return datetime.fromtimestamp(ts).strftime('%Y-%m-%d')

def rename_file_with_date(
    file_path: Path, date_type: str, date_position: str, dry_run: bool
) -> Tuple[bool, str, str]:
    """Renames a single file, returning status and messages."""
    try:
        if not file_path.is_file():
            return False, str(file_path), "Not a file"

        date_str = get_file_date(file_path, date_type)
        
        # Prevent re-renaming
        if date_str in file_path.stem:
            return True, str(file_path), "Already renamed, skipping"

        new_name = f"{date_str}_{file_path.name}" if date_position == 'prefix' else f"{file_path.stem}_{date_str}{file_path.suffix}"
        new_path = file_path.with_name(new_name)

        if new_path.exists():
            return False, str(file_path), f"Error: Target '{new_path.name}' already exists"

        if not dry_run:
            file_path.rename(new_path)
        
        return True, str(file_path), str(new_path)
    except Exception as e:
        return False, str(file_path), f"Error: {e}"

def process_files(file_paths: List[Path], date_type: str, date_position: str, dry_run: bool):
    """Processes a list of files and prints a summary report."""
    print(f"\n{'DRY RUN' if dry_run else 'APPLYING CHANGES'}")
    print(f"Mode: {date_type} date, {date_position}\n")
    
    results = [rename_file_with_date(p, date_type, date_position, dry_run) for p in file_paths]
    
    for success, orig, new in sorted(results, key=lambda x: x[1]):
        if success and "skipping" not in new:
            status = "OK" if not dry_run else "OK (Dry Run)"
            print(f'[ {status:^12} ] {Path(orig).name} -> {Path(new).name}')
        elif "skipping" in new:
            print(f'[ {"SKIPPED":^12} ] {Path(orig).name}')
        else:
            print(f'[ {"ERROR":^12} ] {Path(orig).name} | {new}')

# --- GUI --- 

def show_gui():
    """Display the main GUI for file and folder selection."""
    if not _check_dependencies():
        return

    root = tk.Tk()
    root.title("File Date Renamer")
    root.geometry("600x500")

    # State variables
    selected_paths = []
    date_type = tk.StringVar(value="modified")
    date_position = tk.StringVar(value="prefix")
    apply_changes = tk.BooleanVar(value=False)

    def update_listbox():
        listbox.delete(0, tk.END)
        for path in selected_paths:
            listbox.insert(tk.END, str(path))
        file_count_var.set(f"Selected files/folders: {len(selected_paths)}")

    def select_files():
        initial_dir = get_last_path()
        files = filedialog.askopenfilenames(title="Select Files", initialdir=initial_dir)
        if files:
            selected_paths.extend(Path(f) for f in files)
            set_last_path(files[0])
            update_listbox()

    def select_folder():
        initial_dir = get_last_path()
        folder = filedialog.askdirectory(title="Select Folder", initialdir=initial_dir)
        if folder:
            selected_paths.append(Path(folder))
            set_last_path(folder)
            update_listbox()

    def run_processing():
        all_files = []
        for path in selected_paths:
            if path.is_dir():
                all_files.extend(get_files_from_folder(path))
            elif path.is_file():
                all_files.append(path)
        
        if not all_files:
            messagebox.showwarning("No Files", "No files were found in the selected paths.")
            return

        process_files(all_files, date_type.get(), date_position.get(), not apply_changes.get())
        messagebox.showinfo("Complete", "Processing complete. Check the console for details.")

    # UI Layout
    top_frame = tk.Frame(root)
    top_frame.pack(fill=tk.X, padx=10, pady=5)
    tk.Button(top_frame, text="Select Files...", command=select_files).pack(side=tk.LEFT, padx=5)
    tk.Button(top_frame, text="Select Folder...", command=select_folder).pack(side=tk.LEFT)

    list_frame = tk.Frame(root)
    list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
    listbox = tk.Listbox(list_frame)
    listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar = tk.Scrollbar(list_frame, orient=tk.VERTICAL, command=listbox.yview)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    listbox.config(yscrollcommand=scrollbar.set)
    file_count_var = tk.StringVar(value="Selected files/folders: 0")
    tk.Label(root, textvariable=file_count_var, anchor='w').pack(fill=tk.X, padx=10)

    options_frame = tk.LabelFrame(root, text="Options", padx=10, pady=10)
    options_frame.pack(fill=tk.X, padx=10, pady=5)
    tk.Radiobutton(options_frame, text="Use Modified Date", variable=date_type, value="modified").pack(anchor='w')
    tk.Radiobutton(options_frame, text="Use Created Date", variable=date_type, value="created").pack(anchor='w')
    tk.Radiobutton(options_frame, text="Prefix Date", variable=date_position, value="prefix").pack(anchor='w', pady=(5,0))
    tk.Radiobutton(options_frame, text="Suffix Date", variable=date_position, value="suffix").pack(anchor='w')
    tk.Checkbutton(options_frame, text="Apply Changes (Default: Dry Run)", variable=apply_changes).pack(anchor='w', pady=5)

    tk.Button(root, text="Process Files", command=run_processing, font=("Arial", 10, "bold")).pack(pady=10)

    root.mainloop()

# --- Main Execution ---

def main():
    """Main entry point for command-line execution."""
    parser = argparse.ArgumentParser(description="Rename files by adding a date prefix from their modification or creation time.")
    parser.add_argument("paths", nargs="*", help="Files or folders to process.")
    parser.add_argument("--apply", action="store_true", help="Actually rename files (default: dry run).")
    parser.add_argument("--date", choices=["modified", "created"], default="modified", help="Which date to use.")
    parser.add_argument("--position", choices=["prefix", "suffix"], default="prefix", help="Where to place the date.")
    args = parser.parse_args()

    if not args.paths and sys.stdin.isatty():
        show_gui()
    elif args.paths:
        all_files = []
        for path_str in args.paths:
            path = Path(path_str)
            if path.is_dir():
                all_files.extend(get_files_from_folder(path))
            elif path.is_file():
                all_files.append(path)
        
        if not all_files:
            print("No valid files found to process.")
            return
        process_files(all_files, args.date, args.position, not args.apply)

if __name__ == "__main__":
    # Find and add local venv to path if it exists
    current_dir = Path(__file__).parent.resolve()
    while current_dir != current_dir.parent:
        venv_dir = current_dir / ".venv"
        if venv_dir.is_dir():
            site_packages = venv_dir / "Lib" / "site-packages"
            if site_packages.is_dir():
                sys.path.insert(0, str(site_packages))
                break
        current_dir = current_dir.parent
    
    try:
        main()
    except Exception as e:
        print(f"An error occurred: {e}", file=sys.stderr)
