"""Compatibility layer for transitioning to new config system."""

from typing import Any, Dict, Optional
from pathlib import Path

from .new_config import config as new_config
from .keys import ConfigKeys


class ConfigCompat:
    """Compatibility wrapper for old config interface"""
    
    def __init__(self):
        self._new_config = new_config
    
    @property
    def paths(self) -> Dict[str, Path]:
        """Get paths in old format"""
        return {
            'project_root': self._new_config.get_path(ConfigKeys.Paths.PROJECT_ROOT),
            'logs': self._new_config.get_path(ConfigKeys.Paths.LOGS),
            'config': self._new_config.get_path(ConfigKeys.Paths.CONFIG),
            'resources': self._new_config.get_path(ConfigKeys.Paths.RESOURCES),
            'user_home': self._new_config.get_path(ConfigKeys.Paths.USER_HOME),
            'data': self._new_config.get_path(ConfigKeys.Paths.DATA),
            'profiles': self._new_config.get_path(ConfigKeys.Paths.PROFILES),
            'reports': self._new_config.get_path(ConfigKeys.Paths.REPORTS),
            'master': self._new_config.get_path(ConfigKeys.Paths.MASTER),
            'backup': self._new_config.get_path(ConfigKeys.Paths.BACKUP)
        }
    
    @property
    def env(self) -> Dict[str, Any]:
        """Get environment settings in old format"""
        return {
            'mode': self._new_config.get_env(ConfigKeys.Env.MODE),
            'debug': self._new_config.get_env(ConfigKeys.Env.DEBUG),
            'os_type': self._new_config.get_env(ConfigKeys.Env.OS_TYPE)
        }
    
    def get_window_settings(self) -> Dict[str, Any]:
        """Get window settings in old format"""
        return {
            'left_panel_width': self._new_config.get_pref(ConfigKeys.Window.LEFT_PANEL_WIDTH),
            'right_panel_width': self._new_config.get_pref(ConfigKeys.Window.RIGHT_PANEL_WIDTH),
            'column_widths': self._new_config.get_pref(ConfigKeys.Window.COLUMN_WIDTHS),
            'recent_files': self._new_config.get_pref(ConfigKeys.Window.RECENT_FILES)
        }
    
    def get_logging_settings(self) -> Dict[str, bool]:
        """Get logging settings in old format"""
        return {
            'show_info': self._new_config.get_pref(ConfigKeys.Logging.SHOW_INFO),
            'show_warnings': self._new_config.get_pref(ConfigKeys.Logging.SHOW_WARNINGS)
        }
    
    def get_reports_settings(self) -> Dict[str, Any]:
        """Get report settings in old format"""
        return {
            'last_output_path': self._new_config.get_pref(ConfigKeys.Reports.LAST_OUTPUT_PATH),
            'recent_outputs': self._new_config.get_pref(ConfigKeys.Reports.RECENT_OUTPUTS)
        }
    
    def get_update_data_settings(self) -> Dict[str, Any]:
        """Get update data settings in old format"""
        return {
            'recent_masters': self._new_config.get_pref(ConfigKeys.UpdateData.RECENT_MASTERS),
            'master_history': self._new_config.get_pref(ConfigKeys.UpdateData.MASTER_HISTORY)
        }


# Create compatibility instance
compat_config = ConfigCompat()
