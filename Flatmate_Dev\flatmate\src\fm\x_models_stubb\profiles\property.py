from typing import Dict, Any
from .base_profile import BaseProfile

class PropertyProfile(BaseProfile):
    """Profile for managing property/house details."""
    
    def __init__(self, profile_id: str = None):
        super().__init__(profile_id)
        # Basic property information
        self.address: Dict[str, str] = {
            'street': '',
            'city': '',
            'state': '',
            'postal_code': '',
            'country': ''
        }
        self.property_type: str = ""  # house, apartment, etc.
        self.total_rooms: int = 0
        self.bathrooms: float = 0.0
        self.total_area: float = 0.0  # in square meters/feet
        
        # Rental information
        self.manager_id: str = ""  # ID of the property manager
        self.total_rent: float = 0.0
        self.deposit_required: float = 0.0
        self.available_rooms: list[str] = []  # List of available room IDs
        self.occupied_rooms: Dict[str, str] = {}  # room_id: flatmate_id mapping
        
        # Amenities and features
        self.amenities: list[str] = []
        self.parking_spots: int = 0
        self.furnished: bool = False
        self.pets_allowed: bool = False
        
        # Utilities and bills
        self.included_utilities: list[str] = []
        self.additional_costs: Dict[str, float] = {}  # name: amount mapping
        
        # House rules and policies
        self.house_rules: list[str] = []
        self.maintenance_contact: Dict[str, str] = {
            'name': '',
            'phone': '',
            'email': ''
        }
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert the property profile to a dictionary."""
        data = super().to_dict()
        data.update({
            'address': self.address,
            'property_type': self.property_type,
            'total_rooms': self.total_rooms,
            'bathrooms': self.bathrooms,
            'total_area': self.total_area,
            'manager_id': self.manager_id,
            'total_rent': self.total_rent,
            'deposit_required': self.deposit_required,
            'available_rooms': self.available_rooms,
            'occupied_rooms': self.occupied_rooms,
            'amenities': self.amenities,
            'parking_spots': self.parking_spots,
            'furnished': self.furnished,
            'pets_allowed': self.pets_allowed,
            'included_utilities': self.included_utilities,
            'additional_costs': self.additional_costs,
            'house_rules': self.house_rules,
            'maintenance_contact': self.maintenance_contact
        })
        return data
    
    def from_dict(self, data: Dict[str, Any]) -> None:
        """Load property profile data from a dictionary."""
        super().from_dict(data)
        self.address = data.get('address', {
            'street': '',
            'city': '',
            'state': '',
            'postal_code': '',
            'country': ''
        })
        self.property_type = data.get('property_type', '')
        self.total_rooms = data.get('total_rooms', 0)
        self.bathrooms = data.get('bathrooms', 0.0)
        self.total_area = data.get('total_area', 0.0)
        self.manager_id = data.get('manager_id', '')
        self.total_rent = data.get('total_rent', 0.0)
        self.deposit_required = data.get('deposit_required', 0.0)
        self.available_rooms = data.get('available_rooms', [])
        self.occupied_rooms = data.get('occupied_rooms', {})
        self.amenities = data.get('amenities', [])
        self.parking_spots = data.get('parking_spots', 0)
        self.furnished = data.get('furnished', False)
        self.pets_allowed = data.get('pets_allowed', False)
        self.included_utilities = data.get('included_utilities', [])
        self.additional_costs = data.get('additional_costs', {})
        self.house_rules = data.get('house_rules', [])
        self.maintenance_contact = data.get('maintenance_contact', {
            'name': '',
            'phone': '',
            'email': ''
        })
