"""Configuration package for the Categorize module.

This package provides the new V2 config system:
- Usage-based config definition (no speculation)
- Source tracking for debugging
- Override hierarchy support
- Simple string keys (no complex enums)

Usage:
    from fm.modules.categorize.config import config

    # Define defaults where you use them
    config.ensure_defaults({
        'categorize.display.width': 200
    })

    # Use the values
    width = config.get_value('categorize.display.width')
"""

from .config import config

# Legacy support for transition period
# TODO: Remove these once all code is migrated
try:
    from .local_config import CatConfig
    cat_config = config  # Redirect old usage to new system
    legacy_available = True
except ImportError:
    legacy_available = False

__all__ = ['config']

# Add legacy exports if available (for transition period)
if legacy_available:
    __all__.extend(['CatConfig', 'cat_config'])