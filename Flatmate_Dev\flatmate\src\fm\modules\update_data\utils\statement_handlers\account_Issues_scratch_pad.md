

```python
    @classmethod
    def can_handle_file(cls, filepath: str) -> bool:
        """Check if this handler can process a file using an early-exit scoring system.

        This check is designed for efficiency, returning True as soon as the
        confidence score reaches the minimum threshold of 15.

        Scoring:
        - Account pattern found (any source): 10
        - Column names match: 10
        - Number of columns match: 5

        Args:
            filepath: Path to the file to check.

        Returns:
            bool: True if this handler can process the file.
        """
        handler = cls()
        score = 0
        min_score = 15
        log_msg = f"Handler {cls.__name__} score for '{Path(filepath).name}':"

        try:
            # Read a preview for analysis
            rows_to_read = handler.columns.data_start_row + 5
            df_preview = handler._read_csv(filepath, nrows=rows_to_read)

            if df_preview is None or df_preview.empty:
                log.debug(f"[{cls.__name__}] Could not read preview or file is empty: {filepath}")
                return False

            # 1. Check for account number (high confidence)
            account_number, source = handler._extract_account_number(
                df_preview, filepath=filepath, return_source=True
            )
            if account_number:
                score += 10  # Account match is a strong signal

            # 2. Check column name match
            columns_match = False
            if handler.columns.has_col_names and handler.columns.source_col_names:
                try:
                    header_row = df_preview.iloc[handler.columns.col_names_row].astype(str)
                    expected_headers = set(handler.columns.source_col_names)
                    actual_headers = set(header_row.str.strip().tolist())
                    if expected_headers.issubset(actual_headers):
                        score += 10
                        columns_match = True
                except IndexError:
                    log.debug(f"[{cls.__name__}] Not enough rows for header at row {handler.columns.col_names_row}")
                except Exception as e:
                    log.warning(f"[{cls.__name__}] Error checking columns for {filepath}: {e}")
            
            if score >= min_score:
                log.debug(f"{log_msg} {score} (Account + Columns)")
                return True

            # 3. Check number of columns match
            n_cols_match = len(df_preview.columns) == handler.columns.n_source_cols
            if n_cols_match:
                score += 5

            # Final decision
            log.debug(
                f"{log_msg} {score} (Account: {account_number is not None}, "
                f"Cols: {columns_match}, NumCols: {n_cols_match}) -> {'Pass' if score >= min_score else 'Fail'}"
            )
            
            if score >= min_score:
                return True
            else:
                return False

        except Exception as e:
            log.error(f"Error in {cls.__name__}.can_handle_file for {filepath}: {e}", exc_info=True)
            return False

  def _extract_account_number(self, df: pd.DataFrame, filepath: str | None = None, return_source: bool = False) -> str | tuple[str, AccountNumberSource | None]:
        """Extract account number using handler configuration.
        
        Returns:
            Tuple of (account_number, source) if return_source is True, else just account_number.
            Returns empty string (and None for source) if no match found.
        """
        if not self.account.pattern:
            return ("", None) if return_source else ""

        # 1. Check metadata if configured
        if self.account.in_metadata:
            try:
                row, col = self.account.location
                value = str(df.iloc[row, col]).strip()
                if match := re.search(self.account.pattern, value):
                    account = match.group(1) if match.groups() else match.group(0)
                    return (account, AccountNumberSource.FROM_METADATA) if return_source else account
            except Exception:
                pass

        # 2. Check data column if configured and has account column
        if self.account.in_data and getattr(self.columns, 'has_account_column', False):
            try:
                col = self.account.location[1]
                for value in df[col].dropna():
                    value = str(value).strip()
                    if match := re.search(self.account.pattern, value):
                        account = match.group(1) if match.groups() else match.group(0)
                        return (account, AccountNumberSource.FROM_DATA) if return_source else account
            except Exception:
                pass

        # 3. Check filename if configured
        if self.account.in_file_name and filepath:
            try:
                if match := re.search(self.account.pattern, Path(filepath).name):
                    return (match.group(0), AccountNumberSource.FROM_FILENAME) if return_source else match.group(0)
            except Exception:
                pass

        return ("", None) if return_source else ""

    def _standardise_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Assigns the standardised target column names to the DataFrame."""
        target_names = [str(col) for col in self.columns.target_col_names]

        df.columns = target_names
        return df




        ``` 