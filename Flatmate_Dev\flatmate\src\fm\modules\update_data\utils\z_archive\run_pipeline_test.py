import os
import sys
import pprint

# Add the 'src' directory to the Python path to allow for absolute imports
project_root = r'c:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src'
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from fm.modules.update_data.utils.dw_director import dw_director
from fm.core.services.logger import setup_logging

def run_test():
    """
    Runs the data processing pipeline on a set of test files to verify the merge logic.
    """
    # Configure logging to see output
    setup_logging()

    test_files_dir = r'c:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\utils\test_csvs'
    
    # List of test files, one for each bank format
    filepaths = [
        os.path.join(test_files_dir, '02-1245-0452229-002_01Aug2023_15Dec2024_co-op_bank.Csv'),
        os.path.join(test_files_dir, '38-9004-0646977-00_01Oct_kbank_basic.CSV'),
        os.path.join(test_files_dir, '38-9004-0646977-00_17Oct_kbank_fullCSV.CSV'),
        os.path.join(test_files_dir, 'Export20240325231257_asb.csv'),
    ]

    # The job sheet for the director
    job_sheet = {
        'filepaths': filepaths,
        'save_folder': test_files_dir,
        'update_database': False, # We only want to test the file generation
    }

    print("--- Starting Data Processing Test ---")
    print(f"Processing {len(filepaths)} files...")
    
    # Run the director
    result = dw_director(job_sheet)

    print("\n--- Processing Complete ---")
    print("Director Result:")
    pprint.pprint(result)

    # Check if the master file was created
    output_path = result.get('output_path')
    if output_path and os.path.exists(output_path):
        print(f"\nSuccessfully created master file at: {output_path}")
    else:
        print("\nError: Master file was not created.")

if __name__ == "__main__":
    run_test()
