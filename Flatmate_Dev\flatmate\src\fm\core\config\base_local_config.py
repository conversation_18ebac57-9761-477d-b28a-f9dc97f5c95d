"""Base local configuration manager for components."""

from abc import ABC, abstractmethod
from enum import Enum
from pathlib import Path
from typing import Any, Generic, Type, TypeVar, Union, cast, Optional, Dict

import yaml

from .config import config as core_config
from .keys import ConfigKeys
from ..services.event_bus import global_event_bus, Events

# Generic type for component-specific config keys
LocalKeys = TypeVar("LocalKeys")


class BaseLocalConfig(ABC, Generic[LocalKeys]):
    """Base local configuration manager for components.

    Provides:
    1. Component-specific config access
    2. Type-safe key management through component-specific keys
    3. Default value initialization from component_defaults.yaml
    4. Access to global event bus

    Each component should:
    1. Inherit from this class with their specific key type
    2. Implement get_defaults() to provide component-specific defaults
    3. Optionally override methods if needed
    """

    def __init__(self):
        """Initialize local config."""
        self.events = global_event_bus
        self._setup_defaults()

    @abstractmethod
    def get_defaults(self) -> dict:
        """Get default values for component.

        Each component must implement this to provide its defaults.
        These defaults are used if no value exists in the config files.
        """
        pass

    def _setup_defaults(self):
        """Set up default values for component.

        Config hierarchy (in order of precedence):
        1. User preferences (~/.flatmate/preferences.yaml)
        2. Component-specific defaults (from get_defaults_file_path())
        3. Hardcoded defaults (from get_defaults())
        """
        # Get hardcoded defaults as base
        defaults = self.get_defaults()

        # Try to load component-specific defaults
        component_defaults = {}
        defaults_path = self.get_defaults_file_path()
        
        if defaults_path and defaults_path.exists():
            try:
                with open(defaults_path, "r", encoding="utf-8") as f:
                    component_defaults = yaml.safe_load(f) or {}
            except Exception as e:
                print(f"Error loading component defaults from {defaults_path}: {e}")

        # Try to load user preferences
        user_prefs = {}
        prefs_path = Path("~/.flatmate/preferences.yaml").expanduser()
        if prefs_path.exists():
            try:
                with open(prefs_path, "r", encoding="utf-8") as f:
                    user_prefs = yaml.safe_load(f) or {}
            except Exception as e:
                print(f"Error loading user preferences from {prefs_path}: {e}")

        # Ensure user_prefs is a dictionary
        user_prefs = user_prefs or {}

        # Apply in order: hardcoded -> component -> user prefs
        for key, value in {**defaults, **component_defaults, **user_prefs}.items():
            try:
                if core_config.get_value(key) is None:
                    core_config.set_value(key, value)
            except Exception as e:
                print(f"Error processing config key {key}: {e}")

    def get_defaults_file_path(self) -> Optional[Path]:
        """Get the path to the component's defaults.yaml file.
        
        Override this in subclasses to specify the correct path.
        
        Returns:
            Path to defaults.yaml file, or None if not applicable
        """
        return None

    def get_path(self, key: Union[LocalKeys, str]) -> str:
        """Get path from config."""
        # Convert enum to string if needed
        key_str = key.value if isinstance(key, Enum) else str(key)
        return str(core_config.get_path(key_str))

    def get_value(self, key: Union[LocalKeys, str], default: Any = None) -> Any:
        """Get value from config."""
        # Convert enum to string if needed
        key_str = key.value if isinstance(key, Enum) else str(key)
        return core_config.get_value(key_str, default)

    def set_value(self, key: Union[LocalKeys, str], value: Any):
        """Set value in config."""
        # Convert enum to string if needed
        key_str = key.value if isinstance(key, Enum) else str(key)
        core_config.set_value(key_str, value)

    def get_pref(self, key: Union[LocalKeys, str], default: Any = None) -> Any:
        """Get preference from config."""
        # Convert enum to string if needed
        key_str = key.value if isinstance(key, Enum) else str(key)
        # Map component key to appropriate ConfigKeys type
        if key_str.startswith("gui.window"):
            config_key = ConfigKeys.Window(key_str)
        elif key_str.startswith("logging"):
            config_key = ConfigKeys.Logging(key_str)
        elif key_str.startswith("reports"):
            config_key = ConfigKeys.Reports(key_str)
        elif key_str.startswith("update_data"):
            config_key = ConfigKeys.UpdateData(key_str)
        else:
            # For unknown prefixes, use get_value as fallback
            return self.get_value(key, default)
        return core_config.get_pref(config_key, default)

    def set_pref(self, key: Union[LocalKeys, str], value: Any):
        """Set preference in config."""
        # Convert enum to string if needed
        key_str = key.value if isinstance(key, Enum) else str(key)
        # Map component key to appropriate ConfigKeys type
        if key_str.startswith("gui.window"):
            config_key = ConfigKeys.Window(key_str)
        elif key_str.startswith("logging"):
            config_key = ConfigKeys.Logging(key_str)
        elif key_str.startswith("reports"):
            config_key = ConfigKeys.Reports(key_str)
        elif key_str.startswith("update_data"):
            config_key = ConfigKeys.UpdateData(key_str)
        else:
            # For unknown prefixes, use set_value as fallback
            self.set_value(key, value)
            return
        core_config.set_pref(config_key, value)

    def ensure_defaults(self, defaults_dict: Dict[str, Any]) -> None:
        """Ensure default values exist in configuration.
        
        For each key in defaults_dict:
        - If key doesn't exist in config, add it with the default value
        - If key exists, keep the existing value
        
        This allows components to define their required keys while preserving
        user customizations.
        
        Args:
            defaults_dict: Dictionary of default key-value pairs to ensure
        """
        for key, default_value in defaults_dict.items():
            # Only set if the key doesn't already exist
            if self.get_value(key) is None:
                self.set_value(key, default_value)
                self.events.publish(
                    Events.LOG_EVENT,
                    {
                        "level": "DEBUG",
                        "module": "config",
                        "message": f"Created missing config key: {key} = {default_value}",
                    },
                )






