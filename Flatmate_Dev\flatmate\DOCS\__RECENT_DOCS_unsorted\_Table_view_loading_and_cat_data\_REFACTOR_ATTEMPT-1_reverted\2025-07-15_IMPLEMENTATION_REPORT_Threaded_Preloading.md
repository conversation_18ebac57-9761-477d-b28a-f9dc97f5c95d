# Implementation Report: Threaded Pre-loading Solution

**Generated:** 2025-07-15

---

## 1. Objective

This report documents the implementation of the threaded pre-loading solution, as outlined in the corresponding `ACTION_PLAN`. The primary goal was to eliminate the significant UI freeze (~2.7 seconds) that occurred when navigating to the **Categorize module** by moving all expensive data processing to a background thread.

---

## 2. Summary of Changes

A new, asynchronous data preparation pipeline was created and integrated into the application. The implementation involved creating a dedicated background service, initializing it at application startup, and refactoring the UI-facing presenter to consume the pre-prepared data. This decouples the UI from heavy data processing, ensuring the application remains responsive.

---

## 3. Detailed Changes by File

### A. `fm.core.services.data_preparation_service.py` (New File)

-   **Purpose:** This file contains the core logic for the new background processing service.
-   **`DataPreparationService` (Singleton Class):**
    -   Manages the lifecycle of the background task.
    -   Acts as the central point for the UI to request data and check status (`is_ready` flag).
    -   Holds the prepared DataFrame (`categorize_df`) once it's ready.
-   **`DataPreparationWorker` (QRunnable Class):**
    -   Contains the actual data processing logic that runs on a background thread via `QThreadPool`.
    -   Its `run()` method performs the following sequence:
        1.  Fetches the raw transaction data from the `DBCachingService`.
        2.  Applies the `TransactionCategorizer` logic to each row.
        3.  Sorts the resulting DataFrame by date.
        4.  Emits a `finished` signal with the prepared data or an `error` signal if an exception occurs.
-   **Error Handling:** The service was designed to be robust. If an error occurs during background processing, the `is_ready` flag is still set to `True`, but the data is `None`. This prevents the UI from waiting indefinitely for a process that has failed.

### B. `fm.core.services.__init__.py` (Modified)

-   **Change:** The singleton instance, `data_preparation_service`, was imported and added to the `__all__` list.
-   **Purpose:** This makes the service easily and consistently importable from anywhere in the application via `from fm.core.services import data_preparation_service`.

### C. `fm.main.py` (Modified)

-   **Change:** A call to `data_preparation_service.start_preparation()` was added within the `initialize_application` function.
-   **Purpose:** This call is strategically placed to execute immediately after the primary `DBCachingService` reports a successful load. This ensures the background processing begins at the earliest possible moment, maximizing the time it has to complete before the user navigates to the Categorize module.

### D. `fm.modules.categorize.cat_presenter.py` (Modified)

-   **Change:** The `_handle_load_db` method was completely refactored.
-   **Old Logic:** The method contained a long, synchronous sequence of operations: fetching data from the database, applying categorization, sorting, and finally setting the data in the `TableView`. This entire sequence blocked the UI thread, causing the freeze.
-   **New Logic:** The new implementation is asynchronous and non-blocking:
    1.  It first checks `data_preparation_service.is_ready`.
    2.  If `False`, it displays a "Preparing data..." message in the `InfoBar` and uses a `QTimer.singleShot` to schedule a re-check in 200ms. This prevents the UI from freezing while waiting.
    3.  If `True`, it immediately fetches the (already prepared) DataFrame by calling `get_categorize_dataframe()`.
    4.  The presenter then sets the data in the view, which is now an extremely fast operation.
    5.  It correctly handles the case where the returned data is `None` (due to an error or no transactions) by displaying an empty table and an error message, ensuring a robust user experience.

---

## 4. Expected Outcome

These changes are expected to have completely resolved the UI freeze when navigating to the Categorize module. The user experience should now be seamless, with navigation feeling instantaneous. The application's main window should remain fully interactive and responsive during the entire startup and data preparation process.

## 5. Next Steps

The implementation is complete. The next step is to run the application and perform testing to:

1.  Verify that the navigation freeze is eliminated.
2.  Confirm that all existing functionality in the Categorize module (filtering, sorting, etc.) remains operational.
3.  Ensure the `InfoBar` provides the correct feedback during the brief data preparation phase.

# ---

---

## Current Status & Action Plan

**Date:** 2025-07-16

**Summary:** The project is in a **non-functional, transitional state**. The refactoring process was halted midway, resulting in duplicate files and incomplete code.

### File State Analysis

*   `.../cache/_background_preparer.py`: **Exists.** This is the original worker file and is now obsolete.
*   `.../cache/_table_view_builder.py`: **Exists.** This is the intended replacement, but it is currently an un-refactored copy of the original.
*   `.../data_services/prepared_views.py`: **Exists.** This new file has been created according to the plan.
*   `.../data_services/db_io_service.py`: **Exists, but not yet updated** to use the new generic worker and registry.

### Corrected Action Plan

The following steps will be executed in order to resolve the current issues and complete the refactoring.

1.  **File System Cleanup:**
    *   **Action:** Delete the obsolete worker: `.../cache/_background_preparer.py`.
    *   **Action:** Delete the obsolete service: `.../core/services/data_preparation_service.py`.

2.  **Genericize the Worker:**
    *   **File:** `.../cache/_table_view_builder.py`
    *   **Action:** Refactor the `BackgroundDataPreparer` class into the generic `TableViewBuilder` class. It will be modified to accept a builder function in its constructor and execute it in the `run` method.

3.  **Update the Orchestrator (`DBIOService`):**
    *   **File:** `.../data_services/db_io_service.py`
    *   **Action:** Modify the `start_background_preparation` method. It will now import the `PREPARED_VIEW_REGISTRY` from `prepared_views.py`, look up the correct builder function using the provided `key`, and pass it to the new `TableViewBuilder` worker.

4.  **Final Test:**
    *   **Action:** Run the application to ensure the `Categorize` module loads its data correctly via the new, fully-implemented background process.

---

## Phase 2: Architectural Refinement

Following the initial implementation, a review of the architecture was conducted to ensure long-term scalability and maintainability. The initial solution, while functional, was too specific to the Categorize module and did not align perfectly with the established data service patterns.

### Final Design Decisions

The following refined architecture was agreed upon:

1.  **Generic, Task-Based Worker:** The background worker will be made completely generic. Instead of containing logic for specific modules, it will become a simple task executor.

2.  **View Builder Registry:** A new registry will be created at `fm/core/data_services/prepared_views.py`. This file will contain a dictionary that maps a view `key` (e.g., `'categorize'`) to a specific function responsible for building the required DataFrame. This provides a centralized, scalable way to define and manage pre-prepared datasets.

3.  **`DBIOService` as Orchestrator:** `DBIOService` remains the sole public API for data. Its role is enhanced to orchestrate the background process. When `start_background_preparation(key)` is called, it will look up the appropriate builder function from the registry and pass it to the worker for execution.

4.  **Naming and Location:**
    *   The worker class will be named `TableViewBuilder`.
    *   It will reside in a new, dedicated file named `_table_view_builder.py` within the `fm/core/data_services/cache/` directory. The name reflects its purpose, and the leading underscore marks it as an internal component of the caching system.

