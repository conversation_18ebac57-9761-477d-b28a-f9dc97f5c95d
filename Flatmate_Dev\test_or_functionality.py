#!/usr/bin/env python3
"""
Test script for OR functionality in filter logic.
"""

import sys
import os

# Add the flatmate source to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'flatmate', 'src'))

def test_or_parsing():
    """Test the enhanced OR pattern parsing."""
    print("=== Testing OR Pattern Parsing ===")
    
    try:
        from fm.gui._shared_components.table_view_v2.components.table_utils.enhanced_filter_proxy_model import EnhancedFilterProxyModel
        
        proxy = EnhancedFilterProxyModel()
        
        test_cases = [
            # (pattern, expected_type, description)
            ("coffee|tea", "or_expression", "Simple OR"),
            ("coffee tea", "and_exclude", "Simple AND (fallback)"),
            ("coffee|tea -decaf", "or_expression", "OR with exclude"),
            ("restaurant -mcdonalds|kfc", "or_expression", "Complex OR with exclude"),
            ("", "empty", "Empty pattern"),
            ("coffee", "and_exclude", "Single term (fallback)"),
            ("coffee|tea|hot", "or_expression", "Multiple OR terms"),
        ]
        
        for pattern, expected_type, description in test_cases:
            try:
                result = proxy._parse_filter_pattern_v2(pattern)
                actual_type = result["type"]
                status = "✅ PASS" if actual_type == expected_type else "❌ FAIL"
                print(f"{status} {description}: '{pattern}' → {actual_type} (expected {expected_type})")
                
                if actual_type == "or_expression":
                    print(f"    OR groups: {result.get('or_groups', [])}")
                    print(f"    Exclude terms: {result.get('exclude_terms', [])}")
                elif actual_type == "and_exclude":
                    print(f"    AND terms: {result.get('and_terms', [])}")
                    print(f"    Exclude terms: {result.get('exclude_terms', [])}")
                    
            except Exception as e:
                print(f"❌ ERROR {description}: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_or_matching():
    """Test the OR matching logic."""
    print("\n=== Testing OR Matching Logic ===")
    
    try:
        from fm.gui._shared_components.table_view_v2.components.table_utils.enhanced_filter_proxy_model import EnhancedFilterProxyModel
        
        proxy = EnhancedFilterProxyModel()
        
        test_cases = [
            # (data, pattern, expected_result, description)
            ("Coffee Shop Purchase", "coffee|tea", True, "OR: coffee matches"),
            ("Tea House Visit", "coffee|tea", True, "OR: tea matches"),
            ("Restaurant Bill", "coffee|tea", False, "OR: neither matches"),
            ("Coffee Shop Purchase", "coffee|tea -shop", False, "OR with exclude: exclude matches"),
            ("Tea House Visit", "coffee|tea -shop", True, "OR with exclude: exclude doesn't match"),
            ("Starbucks Coffee", "starbucks|dunkin", True, "OR: first term matches"),
            ("Dunkin Donuts", "starbucks|dunkin", True, "OR: second term matches"),
            ("Local Cafe", "starbucks|dunkin", False, "OR: neither matches"),
            ("Coffee Tea Shop", "coffee|tea hot", True, "Mixed: OR group and AND term both match"),
            ("Cold Coffee", "coffee|tea hot", False, "Mixed: OR matches but AND term doesn't"),
        ]
        
        for data, pattern, expected, description in test_cases:
            try:
                result = proxy._check_pattern_match(data, pattern)
                status = "✅ PASS" if result == expected else "❌ FAIL"
                print(f"{status} {description}")
                print(f"    Data: '{data}', Pattern: '{pattern}' → {result} (expected {expected})")
                
            except Exception as e:
                print(f"❌ ERROR {description}: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_backward_compatibility():
    """Test that existing AND/exclude logic still works."""
    print("\n=== Testing Backward Compatibility ===")
    
    try:
        from fm.gui._shared_components.table_view_v2.components.table_utils.enhanced_filter_proxy_model import EnhancedFilterProxyModel
        
        proxy = EnhancedFilterProxyModel()
        
        # These should work exactly as before
        test_cases = [
            ("Coffee Shop Purchase", "coffee shop", True, "AND: both terms present"),
            ("Coffee Shop Purchase", "coffee tea", False, "AND: missing term"),
            ("Coffee Shop Purchase", "coffee -shop", False, "EXCLUDE: excluded term present"),
            ("Coffee Shop Purchase", "purchase -tea", True, "EXCLUDE: excluded term not present"),
            ("Coffee Shop Purchase", "-tea", True, "EXCLUDE only: excluded term not present"),
            ("Coffee Shop Purchase", "-coffee", False, "EXCLUDE only: excluded term present"),
        ]
        
        for data, pattern, expected, description in test_cases:
            try:
                result = proxy._check_pattern_match(data, pattern)
                status = "✅ PASS" if result == expected else "❌ FAIL"
                print(f"{status} {description}")
                print(f"    Data: '{data}', Pattern: '{pattern}' → {result} (expected {expected})")
                
            except Exception as e:
                print(f"❌ ERROR {description}: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all OR functionality tests."""
    print("=== OR Functionality Tests ===\n")
    
    tests = [
        ("OR Pattern Parsing", test_or_parsing),
        ("OR Matching Logic", test_or_matching),
        ("Backward Compatibility", test_backward_compatibility)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"Running {test_name}...")
        result = test_func()
        results.append((test_name, result))
        print()
    
    # Summary
    print("=== Test Summary ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! OR functionality is working correctly.")
        print("\n✨ New features available:")
        print("  • OR logic: coffee|tea (either term matches)")
        print("  • Mixed logic: coffee|tea -decaf (OR with exclude)")
        print("  • Backward compatible: existing AND/exclude syntax still works")
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
