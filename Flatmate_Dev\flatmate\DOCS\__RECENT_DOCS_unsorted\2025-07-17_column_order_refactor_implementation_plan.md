# Column Order Refactor - Actionable Implementation Plan

## Executive Summary

**Problem:** Column ordering currently defaults to alphabetical/dict order instead of FM standards. The archived StandardColumns enum defined the correct order, but the current Columns dataclass system lost this ordering logic.

**Solution:** Implement a canonical column order system with user preference overrides, following the hybrid config approach outlined in the discussion document.

---

## Implementation Phases

### Phase 1: Establish Canonical Column Order (2-3 hours)

#### Step 1.1: Create StandardColumnsOrder Enum
- **File:** `flatmate/src/fm/core/data_services/standards/column_order.py`
- **Action:** Create enum defining FM-standard column order based on archived enum
- **Implementation:**
  ```python
  from enum import Enum
  
  class StandardColumnsOrder(Enum):
      """Canonical FM-standard column order for consistent display"""
      DATE = 1
      DETAILS = 2  
      AMOUNT = 3
      BALANCE = 4
      ACCOUNT = 5
      # ... continue with logical order
  ```

#### Step 1.2: Update Column Dataclass
- **File:** `flatmate/src/fm/core/data_services/standards/column_definition.py`
- **Action:** Add `order` field referencing the enum
- **Implementation:**
  ```python
  @dataclass(frozen=True)
  class Column:
      db_name: str
      display_name: str
      dtype: Any
      groups: List[str] = field(default_factory=list)
      width: int = 10
      order: int = 999  # Default for unordered columns
  ```

#### Step 1.3: Update Columns Registry
- **File:** `flatmate/src/fm/core/data_services/standards/columns.py`
- **Action:** Add order values to all column definitions
- **Implementation:** Update each column definition to include `order=StandardColumnsOrder.COLUMN_NAME.value`

### Phase 2: Implement Column Order Service (3-4 hours)

#### Step 2.1: Create ColumnOrderService
- **File:** `flatmate/src/fm/core/data_services/standards/column_order_service.py`
- **Action:** Implement service for order resolution with preference hierarchy
- **Key Methods:**
  - `get_column_order(module_name: str) -> List[str]`
  - `set_column_order(module_name: str, order: List[str])`
  - `get_default_order() -> List[str]`
  - `reset_to_default(module_name: str)`

#### Step 2.2: Implement Order Resolution Logic
- **Priority:** per-module user preference → global user preference → FM-standard default
- **Integration:** Use existing config system (`base_local_config_v2.py` pattern)
- **Storage:** Leverage existing `~/.flatmate/preferences.yaml` structure

#### Step 2.3: Add Helper Methods to Columns Class
- **File:** `flatmate/src/fm/core/data_services/standards/columns.py`
- **Action:** Add convenience methods:
  ```python
  @classmethod
  def get_ordered_columns(cls, group_name: str = None, module_name: str = None) -> List[Column]:
      """Get columns in proper order with user preferences applied"""
  
  @classmethod  
  def get_default_order(cls) -> List[Column]:
      """Get columns in FM-standard order"""
  ```

### Phase 3: Update UI Components (2-3 hours)

#### Step 3.1: Update Table View Components
- **Files:** 
  - `flatmate/src/fm/gui/_shared_components/table_view_v2/fm_table_view.py`
  - `flatmate/src/fm/gui/_shared_components/table_view_v2/components/table_view_core.py`
- **Action:** Replace alphabetical ordering with ColumnOrderService calls
- **Key Changes:**
  - Update `set_display_columns()` to use ordered columns
  - Modify column header setup to respect order

#### Step 3.2: Update Categorize Module
- **File:** `flatmate/src/fm/modules/categorize/_view/components/center_panel/transaction_view_panel.py`
- **Action:** Use ColumnOrderService for column ordering
- **Implementation:**
  ```python
  from fm.core.data_services.standards.column_order_service import ColumnOrderService
  
  order_service = ColumnOrderService()
  ordered_columns = order_service.get_column_order('categorize')
  ```

#### Step 3.3: Update Dropdown Components
- **Action:** Ensure any column selection dropdowns use the same ordering
- **Files:** Search for dropdown/combobox components that list columns

### Phase 4: Configuration Integration (1-2 hours)

#### Step 4.1: Add Column Order Config Keys
- **File:** `flatmate/src/fm/modules/categorize/config/cat_keys.py`
- **Action:** Add column order configuration keys
- **Implementation:**
  ```python
  class Display(str, Enum):
      COLUMN_ORDER = 'categorize.display.column_order'
      COLUMN_ORDER_GLOBAL = 'display.column_order_global'
  ```

#### Step 4.2: Update Module Config Classes
- **Action:** Ensure each module can store/retrieve column order preferences
- **Integration:** Use existing `base_local_config_v2` pattern for consistency

### Phase 5: Testing & Validation (2-3 hours)

#### Step 5.1: Unit Tests
- **File:** `flatmate/src/fm/core/data_services/standards/tests/test_column_order_service.py`
- **Coverage:**
  - Order resolution logic
  - Preference hierarchy
  - Default fallback behavior
  - User preference persistence

#### Step 5.2: Integration Tests
- **Action:** Test UI components with new ordering
- **Scenarios:**
  - Fresh install (should use FM defaults)
  - User with existing preferences
  - Module-specific overrides
  - Global preference changes

#### Step 5.3: Manual Validation
- **Test Cases:**
  - Categorize module displays correct order
  - User can change order and it persists
  - Reset to defaults works
  - Multiple modules maintain separate preferences

---

## Implementation Notes

### Current System Analysis
- **Active Files:** `columns.py`, `column_definition.py` (current system)
- **Archived Files:** Multiple archived enum systems show previous ordering attempts
- **Config System:** Uses `base_local_config_v2.py` with YAML preference storage
- **UI Integration:** Table views use `Columns.get()` for column groups

### Key Design Decisions
1. **Enum-based ordering:** Provides type safety and explicit control
2. **Service pattern:** Encapsulates order logic in dedicated service
3. **Hybrid preferences:** Global defaults with per-module overrides
4. **Existing config integration:** Leverage current preference system

### Risk Mitigation
- **Backwards compatibility:** Graceful fallback to alphabetical if order missing
- **Migration path:** Existing systems continue working during transition
- **Testing strategy:** Comprehensive coverage of preference scenarios

---

## Success Criteria
- [ ] Columns display in FM-standard order by default
- [ ] Users can customize order per module
- [ ] Preferences persist between sessions
- [ ] No regression in existing functionality
- [ ] Clean, maintainable code architecture

---

*Implementation plan created: 2025-07-17*
*Estimated total effort: 10-15 hours*
