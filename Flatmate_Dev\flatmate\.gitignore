# Python bytecode
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
# dist/
# build/
# *.egg-info/

# Virtual environments
 *.venv*
# env/
# ENV/

# IDE files
.idea/
.vscode/
*.swp
*.swo

# OS specific files
.DS_Store
Thumbs.db

# Application specific
logs/
*.log
# user_settings/
.pytest_cache/
.coverage
htmlcov/

# Local development
# .env
# .python-version
# .ropeproject/

# Data files that shouldn't be in version control
# *.db
# *.sqlite
# data/*.csv

*.egg-info

*.log
src/fm/modules/update_data/utils/statement_handlers/z_example_formats/asb_bank_csv/Export20240325231257_asb.csv
src/fm/modules/update_data/utils/statement_handlers/z_example_formats/co-op_bank_csv/02-1245-0452229-002_01Aug2023_15Dec2024_co-op_bank.Csv
src/fm/modules/update_data/utils/statement_handlers/z_example_formats/kiwibank/basic_csv/38-9004-0646977-00_01Oct_kbank_basic.CSV
src/fm/modules/update_data/utils/statement_handlers/z_example_formats/kiwibank/full_csv/38-9004-0646977-00_17Oct_kbank_fullCSV.CSV
src/fm/modules/update_data/utils/test_csvs/02-1245-0452229-002_01Aug2023_15Dec2024_co-op_bank.Csv
src/fm/modules/update_data/utils/test_csvs/38-9004-0646977-00_01Oct_kbank_basic.CSV
src/fm/modules/update_data/utils/test_csvs/38-9004-0646977-00_17Oct_kbank_fullCSV.CSV
src/fm/modules/update_data/utils/test_csvs/Export20240325231257_asb.csv
src/test_csvs/02-1245-0452229-002_01Aug2023_15Dec2024_co-op_bank.Csv
src/test_csvs/38-9004-0646977-00_01Oct_kbank_basic.CSV
src/test_csvs/38-9004-0646977-00_17Oct_kbank_fullCSV.CSV
src/test_csvs/Export20240325231257_asb.csv
src/test_csvs/fmMaster.csv
tests/test_db_io_service.py
test_pipeline.py
src/fm/modules/update_data/utils/test_statement_processing.py
src/fm/modules/update_data/utils/test_statement_validation.py
