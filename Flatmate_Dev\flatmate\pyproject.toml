[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "flatmate"
version = "0.1.0"
authors = [
  { name="Flatmate Team" },
]
description = "Flatmate application"
requires-python = ">=3.11"
dependencies = [
    "PySide6",
    "pandas",
    "pyyaml",
]

[project.optional-dependencies]
test = [
    "pytest>=7.0.0",
    "pytest-cov>=3.0.0",
    "pytest-mock>=3.10.0",
]

[tool.setuptools]
packages = ["fm", "fm.cli"]
package-dir = {"" = "src"}

[project.scripts]
flatmate = "fm.main:main"
dbq = "fm.cli.db_cli:main"
dbquery = "fm.cli.db_cli:main"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"
python_classes = "Test*"
addopts = "-v --cov=src --cov-report=term-missing"
