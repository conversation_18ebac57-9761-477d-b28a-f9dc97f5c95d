# Flatmate Core Architecture

## Overview
The core module provides fundamental architectural components for the Flatmate application, enabling modular, event-driven design.

## Core Systems

### 1. Configuration System
- **Location**: `src/fm/core/config/`
- **Purpose**: Manage application and module settings
- **Components**:
  - Core config manager
  - Module config wrappers
  - YAML-based settings files

### 2. Services System
- **Location**: `src/fm/core/services/`
- **Purpose**: Provide shared functionality across modules
- **Components**:
  - Core services (e.g., master file tracking)
  - Module service wrappers
  - Event integration

### 3. Event Bus
- **Location**: `src/fm/core/event_bus/`
- **Purpose**: Enable module communication
- **Components**:
  - Global event bus
  - Event types
  - Event handlers

### 4. Logging
- **Location**: `src/fm/core/services/logger.py`
- **Purpose**: Application-wide logging with accurate module detection
- **Components**:
  - LogLevel enum (DEBUG, INFO, WARNING, ERROR, CRITICAL)
  - Direct console and file output
  - Custom module detection via stack inspection
  - Event bus integration for log events

## Components

### EventBus
A lightweight publish-subscribe event system for inter-module communication.
- Decouples modules
- Supports dynamic event subscription
- Enables loose coupling between components

#### Key Methods
- `subscribe(event_type, listener)`: Register a listener for a specific event
- `unsubscribe(event_type, listener)`: Remove a listener
- `publish(event_type, data)`: Broadcast an event to all listeners

#### Example
```python
from flatmate.core import global_event_bus, Events

def handle_app_start(data):
    print("App started!")

global_event_bus.subscribe(Events.APP_STARTED, handle_app_start)
```

### BaseModule
An abstract base class for all modules in the application.
- Enforces a standard module initialization pattern
- Provides a consistent interface for module lifecycle management

#### Key Methods
- `initialize()`: Set up the module
- `shutdown()`: Clean up module resources

#### Example
```python
from flatmate.core import BaseModule

class UpdateDataModule(BaseModule):
    def __init__(self):
        super().__init__("update_data")
    
    def initialize(self):
        # Module-specific initialization
        self.is_initialized = True
    
    def shutdown(self):
        # Cleanup resources
        pass
```

### ModuleManager # TODO replaced by module_coordinator.py
Handles dynamic module discovery and initialization.
- Automatically discovers modules
- Manages module lifecycle
- Supports runtime module loading

#### Key Methods
- `discover_modules()`: Find and import all modules
- `initialize_modules()`: Initialize discovered modules
- `get_module(name)`: Retrieve a specific module

#### Example
```python
from flatmate.core import ModuleManager

module_manager = ModuleManager()
module_manager.discover_modules()
module_manager.initialize_modules()
```

### ConfigManager
Centralized configuration management.
- Loads application-wide configurations
- Provides a simple key-value configuration interface
- Supports TOML configuration files

#### Key Methods
- `get(key, default)`: Retrieve a configuration value
- `set(key, value)`: Update a configuration value

#### Example
```python
from flatmate.core import ConfigManager

config = ConfigManager('/path/to/config/dir')
database_url = config.get('database.url')
```

## Module Integration

### Configuration Access
```python
# Module uses local config wrapper
from .config import module_config

value = module_config.get_value('some.key')
```

### Service Access
```python
# Module uses local service wrapper
from .services import module_services

module_services.some_service_method()
```

### Event Communication
```python
# Module uses event bus directly
from ...core.event_bus import global_event_bus

global_event_bus.emit('event_type', data)
```

## Directory Structure
```
src/fm/
├── core/
│   ├── config/         # Core configuration
│   ├── services/       # Core services (includes logger.py)
│   └── event_bus/      # Event system
└── modules/
    └── module_name/
        ├── config/     # Module config wrapper
        └── services/   # Module service wrapper
```

## Design Principles
1. **Modularity**: Each component has a single, well-defined responsibility
2. **Extensibility**: Easy to add new modules and features
3. **Loose Coupling**: Modules communicate via events, not direct dependencies
4. **Configuration Flexibility**: Centralized, file-based configuration management

## Typical Application Lifecycle
1. Create ConfigManager
2. Initialize ModuleManager
3. Discover Modules
4. Initialize Modules
5. Publish APP_STARTED event
6. Run application
7. Publish APP_SHUTDOWN event when closing

## Recommended Configuration File (app_config.toml)
```toml
[database]
url = "sqlite:///flatmate.db"
max_connections = 5

[logging]
level = "INFO"
file = "/var/log/flatmate.log"

[modules]
enabled = ["update_data", "expense_tracker"]
```

## Future Improvements
- Add comprehensive logging capabilities
- Implement more robust error handling
- Create plugin system for external module integration
- Add configuration validation

## Dependencies
- `tomli` for TOML configuration parsing
- Python 3.8+ ! # NOTE: (we use Yaml for config files)

## Best Practices
1. Always access core systems through module wrappers
2. Keep module-specific logic in wrappers
3. Document wrapper functionality in module README.md
4. Use events for cross-module communication
5. Log important operations and errors
