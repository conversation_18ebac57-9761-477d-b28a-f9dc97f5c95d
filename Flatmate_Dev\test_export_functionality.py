#!/usr/bin/env python3
"""
Test script for the new export functionality.
Tests the get_visible_dataframe() method to ensure it works correctly.
"""

import sys
import os
import pandas as pd
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
from PySide6.QtCore import Qt

# Add the flatmate source to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'flatmate', 'src'))

from fm.gui._shared_components.table_view_v2.fm_table_view import CustomTableView_v2


class TestWindow(QMainWindow):
    """Test window for export functionality."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Export Functionality Test")
        self.setGeometry(100, 100, 800, 600)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Create test data
        self.test_data = pd.DataFrame({
            'Date': ['2025-01-01', '2025-01-02', '2025-01-03', '2025-01-04', '2025-01-05'],
            'Description': ['Test Transaction 1', 'Test Transaction 2', 'Test Transaction 3', 'Test Transaction 4', 'Test Transaction 5'],
            'Amount': [100.50, -25.00, 200.75, -50.25, 150.00],
            'Account': ['Checking', 'Savings', 'Checking', 'Credit Card', 'Savings'],
            'Category': ['Income', 'Expense', 'Income', 'Expense', 'Income']
        })
        
        # Create table view
        self.table_view = CustomTableView_v2()
        self.table_view.configure(
            auto_size_columns=True,
            max_column_width=40,
            show_toolbar=True
        )
        
        # Set the test data
        self.table_view.set_dataframe(self.test_data)
        layout.addWidget(self.table_view)
        
        # Add test buttons
        test_button = QPushButton("Test get_visible_dataframe()")
        test_button.clicked.connect(self.test_visible_dataframe)
        layout.addWidget(test_button)
        
        filter_button = QPushButton("Apply Filter (Amount > 0)")
        filter_button.clicked.connect(self.apply_filter)
        layout.addWidget(filter_button)
        
        clear_button = QPushButton("Clear Filters")
        clear_button.clicked.connect(self.clear_filters)
        layout.addWidget(clear_button)
        
    def test_visible_dataframe(self):
        """Test the get_visible_dataframe method."""
        try:
            # Get the visible dataframe
            visible_df = self.table_view.table_view.get_visible_dataframe()
            
            print("=== Test Results ===")
            print(f"Original data shape: {self.test_data.shape}")
            print(f"Visible data shape: {visible_df.shape}")
            print("\nVisible data:")
            print(visible_df)
            print("\nColumns in visible data:", list(visible_df.columns))
            
            # Test if method exists and works
            if hasattr(self.table_view.table_view, 'get_visible_dataframe'):
                print("✅ get_visible_dataframe() method exists and works!")
            else:
                print("❌ get_visible_dataframe() method not found!")
                
        except Exception as e:
            print(f"❌ Error testing get_visible_dataframe(): {e}")
            import traceback
            traceback.print_exc()
    
    def apply_filter(self):
        """Apply a simple filter to test filtering."""
        try:
            # This is a simplified filter - in real usage, filters would be applied through the toolbar
            print("Filter applied (conceptually - Amount > 0)")
            print("In real usage, filters would be applied through the table toolbar")
            
            # For testing, we can manually filter the source data
            filtered_data = self.test_data[self.test_data['Amount'] > 0]
            print(f"Filtered data would have {len(filtered_data)} rows")
            
        except Exception as e:
            print(f"Error applying filter: {e}")
    
    def clear_filters(self):
        """Clear all filters."""
        try:
            print("Filters cleared (conceptually)")
            print("In real usage, filters would be cleared through the table toolbar")
        except Exception as e:
            print(f"Error clearing filters: {e}")


def main():
    """Main test function."""
    app = QApplication(sys.argv)
    
    # Create and show test window
    window = TestWindow()
    window.show()
    
    print("=== Export Functionality Test ===")
    print("1. Click 'Test get_visible_dataframe()' to test the new method")
    print("2. Try using the Export button in the table toolbar")
    print("3. Check that export works correctly")
    
    # Run the application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
