"""
Defines logical groupings of columns for different application contexts.

This module imports the canonical StandardColumns enum and organizes its members
into context-specific lists that other modules can use for their specific needs,
without needing to know about columns that are not relevant to them.
"""

from enum import Enum
from .fm_standard_columns import StandardColumns


class DatabaseSystemColumns(str, Enum):
    """Columns managed exclusively by the database repository."""
    ID = 'id'
    IMPORT_DATE = 'import_date'
    MODIFIED_DATE = 'modified_date'
    HASH = 'hash'

# Columns that are expected to be parsed from source statement files.
# Statement handlers should aim to produce a DataFrame with these columns.
STATEMENT_HANDLER_COLUMNS = [
    # Core required columns
    StandardColumns.DATE,
    StandardColumns.DETAILS,
    StandardColumns.AMOUNT,
    StandardColumns.BALANCE,
    StandardColumns.ACCOUNT,
    StandardColumns.UNIQUE_ID, #if present and no BALANCE col

    #used as a place holder in some formats for redundant or empty columns
    StandardColumns.EMPTY_COLUMN,

    #Extended_Statement_Columns (from some statement files)
    StandardColumns.CREDIT_AMOUNT,
    StandardColumns.DEBIT_AMOUNT,
    StandardColumns.PAYMENT_TYPE,
    StandardColumns.TP_REF,
    StandardColumns.TP_PART,
    StandardColumns.TP_CODE,
    StandardColumns.OP_REF,
    StandardColumns.OP_PART,
    StandardColumns.OP_CODE,
    StandardColumns.OP_NAME,
    StandardColumns.OP_ACCOUNT,

    #Handle_Added_Columns (not in source statement files)
    StandardColumns.SOURCE_FILENAME,
    StandardColumns.SOURCE_BANK,
    StandardColumns.SOURCE_TYPE,
    StandardColumns.STATEMENT_DATE, #date created - gleaned from filename 
]

# Columns that are generated and managed exclusively by the database repository.
# These should not be present in dataframes sent for import.
DATABASE_SYSTEM_COLUMNS = list(DatabaseSystemColumns)

# Columns that are added by the user or application logic after import.
USER_EDITABLE_COLUMNS = [
    StandardColumns.CATEGORY,
    StandardColumns.TAGS,
    StandardColumns.NOTES,
]
