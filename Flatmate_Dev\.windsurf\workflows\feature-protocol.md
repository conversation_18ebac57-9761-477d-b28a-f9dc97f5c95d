---
description:  protocol for implementing new features, combining stepwise execution with .collaborative md-mediated discussion and traceability.
---

# Flatmate Feature Task Protocol v2 (Discussion-Centered)

A structured, collaborative protocol for implementing new features, combining stepwise execution with .md-mediated discussion and traceability.

---

## Protocol Overview

**Roles:**
- The user is CEO: The PM and Lead Dev, and will add high-level direction or feedback using `>>`.

For each new feature, create a folder in `flatmate/DOCS/_FEATURES/<feature_name>/` containing:

- `requirements.md` — Clear, testable requirements
- `design.md` — Architecture and technical design
- `tasks.md` — Actionable implementation steps
- `discussion.md` — Stepwise commentary, decisions, and stakeholder input

---

## Stepwise Feature Implementation Flow

1. **Clarify the Feature**
    - What is the desired behaviour?
    - What is the motivation or problem being solved?
    - Document this in `discussion.md` and update `requirements.md`.

2. **Collaborative Requirements Discussion**
    - The (single) product manager and lead developer add comments in `discussion.md`, each using `>>` to denote their input.
    - Finalise requirements in `requirements.md`.

3. **Design Discussion**
    - Discuss architecture, data flow, edge cases in `discussion.md`.
    - Update `design.md` as consensus emerges.

4. **Task Breakdown**
    - List actionable steps in `tasks.md`.
    - Discuss dependencies, blockers, and assignments in `discussion.md`.

5. **Implementation & Traceability**
    - At each key milestone, document decisions, blockers, and progress in `discussion.md`.
    - Link PRs, commits, and test results for traceability.

6. **Review & Close**
    - Summarise outcomes, lessons learned, and any follow-ups in `discussion.md`.
    - Ensure all .mds are up to date and linked in the main tracking doc.

---

## Key Principles
- Every major step is discussed and documented in `discussion.md`.
- Cascade (AI) inhabits the roles of product manager and lead developer, using `>>` for all such comments.
- The user, as PM and Lead Dev, may add comments or direction using `>>>` for clarity and traceability.
- Requirements, design, and tasks are living documents, updated as the feature evolves.

---

**This protocol ensures clarity, accountability, and collaborative traceability for all new feature work.**
