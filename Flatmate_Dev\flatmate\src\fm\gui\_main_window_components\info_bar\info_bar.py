"""
InfoBar widget for displaying status messages and loading statistics across the application.
"""

import time
from datetime import <PERSON><PERSON><PERSON>
from PySide6.QtCore import Qt, QTimer, Signal, QObject
from PySide6.QtWidgets import QLabel, QStatusBar, QHBoxLayout, QWidget

from fm.core.services.event_bus import global_event_bus, Events


class InfoBar(QStatusBar):
    """Status bar for displaying application status messages and loading statistics."""
    
    def __init__(self, parent=None):
        """Initialize the info bar widget."""
        super().__init__(parent)
        self._init_ui()
        self._connect_events()
        
    def _connect_events(self):
        """Connect to the global event bus."""
        global_event_bus.subscribe(Events.INFO_MESSAGE, self._handle_info_message)
        global_event_bus.subscribe(Events.INFO_CLEAR, self._handle_info_clear)
        global_event_bus.subscribe(Events.INFO_SHOW, self._handle_info_show)
        global_event_bus.subscribe(Events.INFO_HIDE, self._handle_info_hide)
        
    def _init_ui(self):
        """Initialize the UI components."""
        # Set up the status bar appearance
        self.setStyleSheet("""
            QStatusBar {
                background-color: #2a2a2a;
                border-top: 1px solid #333;
                color: #aaa;
                font-style: italic;
                padding: 2px 8px;
            }
            QStatusBar::item {
                border: none;
                padding: 0 4px;
            }
        """)
        
        # Create a container widget for our custom layout
        self.container = QWidget()
        layout = QHBoxLayout(self.container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)
        
        # Message label (left-aligned)
        self.message_label = QLabel()
        self.message_label.setStyleSheet("color: #aaa;")
        layout.addWidget(self.message_label, 1)
        
        # Stats label (right-aligned)
        self.stats_label = QLabel()
        self.stats_label.setStyleSheet("color: #888;")
        self.stats_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        layout.addWidget(self.stats_label)
        
        # Add the container to the status bar
        self.addPermanentWidget(self.container, 1)
        
    def start_loading(self, message="Loading..."):
        """Start a loading operation with the given message.

        Args:
            message: The message to display during loading
        """
        # Clean up any existing timer first
        if hasattr(self, 'timer') and self.timer is not None:
            self.timer.stop()
            self.timer.deleteLater()

        # Initialize loading state
        self.load_start_time = time.time()
        self.records_loaded = 0
        self.total_records = None  # Explicitly initialize
        self.message_label.setText(message)
        self._update_stats()
        self.setVisible(True)

        # Start a timer to update the elapsed time (reduced frequency for smoother display)
        self.timer = QTimer(self)
        self.timer.timeout.connect(self._update_stats)
        self.timer.start(500)  # Update every 500ms for smoother display
    
    def update_progress(self, records_loaded, total_records=None):
        """Update the loading progress.
        
        Args:
            records_loaded: Number of records loaded so far
            total_records: Total records to load (optional)
        """
        self.records_loaded = records_loaded
        self.total_records = total_records
        self._update_stats()
    
    def _update_stats(self):
        """Update the statistics display."""
        stats = []

        # Show record count if we have any records
        if hasattr(self, 'records_loaded') and self.records_loaded is not None:
            if hasattr(self, 'total_records') and self.total_records is not None and self.total_records > 0:
                stats.append(f"{self.records_loaded:,}/{self.total_records:,} records")
            else:
                stats.append(f"{self.records_loaded:,} records")

        # Show elapsed time if we have a start time
        if hasattr(self, 'load_start_time') and self.load_start_time is not None:
            elapsed = time.time() - self.load_start_time
            # Format elapsed time nicely
            if elapsed < 60:
                stats.append(f"{elapsed:.1f}s")
            else:
                stats.append(f"{timedelta(seconds=int(elapsed))}")

        # Show records per second if we can calculate it (with better edge case handling)
        if (hasattr(self, 'load_start_time') and self.load_start_time is not None and
            hasattr(self, 'records_loaded') and self.records_loaded is not None and self.records_loaded > 0):
            elapsed = time.time() - self.load_start_time
            # Only show rec/s after at least 1 second to avoid erratic values
            if elapsed >= 1.0:
                rps = self.records_loaded / elapsed
                if rps >= 1000:
                    stats.append(f"{rps/1000:.1f}k rec/s")
                else:
                    stats.append(f"{rps:,.0f} rec/s")

        self.stats_label.setText(" • ".join(stats) if stats else "")
    
    def finish_loading(self, message="Ready"):
        """Finish the loading operation and show a completion message.

        Args:
            message: The message to display after loading completes
        """
        # Stop and clean up the timer
        if hasattr(self, 'timer') and self.timer is not None:
            self.timer.stop()
            self.timer.deleteLater()
            self.timer = None

        # Update final stats one last time before stopping
        self._update_stats()

        # Set the completion message
        self.message_label.setText(message)
    
    def set_message(self, message):
        """Set the message text to display.
        
        Args:
            message: The message text to display
        """
        if not message:
            self.clear()
            return
            
        self.message_label.setText(str(message))
        self.stats_label.clear()
        self.setVisible(bool(message))
        
        # Update status bar message
        super().showMessage(str(message))
        
    def _handle_info_message(self, message):
        """Handle info messages from the event bus.
        
        Args:
            message: The message text or dict containing message details
        """
        if isinstance(message, dict):
            # Handle structured message
            if 'text' in message:
                if message.get('is_loading', False):
                    self.start_loading(message['text'])
                else:
                    self.set_message(message['text'])
                
                if 'progress' in message and 'total' in message:
                    self.update_progress(message['progress'], message['total'])
        else:
            # Handle simple string message
            self.set_message(str(message))
    
    def _handle_info_clear(self, _=None):
        """Handle clear events from the event bus."""
        self.clear()

    def _handle_info_show(self, _=None):
        """Handle show events from the event bus."""
        self.setVisible(True)

    def _handle_info_hide(self, _=None):
        """Handle hide events from the event bus."""
        self.setVisible(False)

    def clear(self):
        """Clear the current message and statistics."""
        # Stop and clean up the timer
        if hasattr(self, 'timer') and self.timer is not None:
            self.timer.stop()
            self.timer.deleteLater()
            self.timer = None

        # Clear all loading state
        if hasattr(self, 'load_start_time'):
            delattr(self, 'load_start_time')
        if hasattr(self, 'records_loaded'):
            delattr(self, 'records_loaded')
        if hasattr(self, 'total_records'):
            delattr(self, 'total_records')

        # Clear UI elements
        self.message_label.clear()
        self.stats_label.clear()
        super().clearMessage()
    
    def __del__(self):
        """Clean up event subscriptions and timers."""
        # Clean up timer
        if hasattr(self, 'timer') and self.timer is not None:
            try:
                self.timer.stop()
                self.timer.deleteLater()
            except (RuntimeError, ReferenceError):
                pass

        # Unsubscribe from events
        try:
            global_event_bus.unsubscribe(Events.INFO_MESSAGE, self._handle_info_message)
            global_event_bus.unsubscribe(Events.INFO_CLEAR, self._handle_info_clear)
            global_event_bus.unsubscribe(Events.INFO_SHOW, self._handle_info_show)
            global_event_bus.unsubscribe(Events.INFO_HIDE, self._handle_info_hide)
        except (RuntimeError, ReferenceError):
            # Widget is being destroyed, ignore any errors
            pass
