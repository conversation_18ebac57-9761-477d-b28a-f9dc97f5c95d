# Categorize Module Enhancement Project - Implementation Plan

*Document Version: 1.0.0*
*Created: 2025-07-14*
*Status: Ready for Implementation*

## Overview

This document outlines a comprehensive 5-phase plan to address all current issues in the categorize module, including database loading, column management, configuration persistence, and user experience improvements.

## Current Issues Identified

### Configuration System Problems
- YAML defaults not generating properly
- Config system may not be working correctly
- Naming convention unclear (settings.yaml vs local_settings.yaml)
- BaseLocalConfig inheritance issues

### Database Loading Issues
- No persistence of last loaded query/filters
- Missing default behavior on module open
- No cached query restoration

### Column Management Problems
- No logical column ordering system
- Column visibility/width not remembered between sessions
- Auto-sizing appears disabled
- Details column not expanding to fill available space

### Table View Issues
- Dropdown column ordering inconsistent with display
- Missing "All columns" option and proper grouping
- Table view widget configuration not fully utilized

### Default Sort Problems
- Default sort order not applied consistently
- Used to be set by fm_standard_columns enum
- Sort persistence not working

# Development Plan ------------------

## 5-Phase Development Plan

### Phase 1: Configuration System Foundation 🔧
**Goal**: Establish robust configuration system with proper defaults and persistence

#### Sprint 1.1: Config System Audit & Repair
- Audit current `BaseLocalConfigV2` implementation
- Fix YAML generation issues (defaults.yaml not being created)
- Verify config key tracking and source attribution
- Test ensure_defaults() functionality
>> dev: check local config files, then _base_local_config_v2.py

#### Sprint 1.2: Default Values Definition
- Define comprehensive defaults for:
  - Default visible columns (date, details, amount, account, tags)
  - Column widths (details: 40, date: 12, amount: 12, etc.)
  - Default sort (date, descending)
  - Filter defaults (30 days back)
- Implement proper config key structure

#### Sprint 1.3: User Preferences Persistence
- Implement user preference storage (.flatmate directory)
- Create override hierarchy (user prefs > component defaults > runtime defaults)
- Add preference reset/restore functionality

### Phase 2: Database Loading & Filtering 🗄️
**Goal**: Implement proper database loading with query persistence

#### Sprint 2.1: Database Query Enhancement
- Implement last query persistence (filters, date ranges)
- Add query state restoration on module load
- Improve error handling and empty result scenarios
- Cache frequently used queries

#### Sprint 2.2: Filter UI Improvements
- Enhance left panel filter controls
- Add "remember last filter" functionality
- Improve date range defaults and persistence
- Add account selection memory

#### Sprint 2.3: Default Sort Implementation
- Implement configurable default sort column/order
- Ensure consistent sorting across table view dropdown
- Add sort state persistence
- Fix sort order based on StandardColumns enum logic

### Phase 3: Column Management System 📊
**Goal**: Create comprehensive column ordering, visibility, and width management

#### Sprint 3.1: Column Ordering System
- Implement logical column ordering based on usage likelihood
- Use StandardColumns enum for consistent ordering
- Apply same ordering to table view dropdown
- Make ordering configurable and persistent

#### Sprint 3.2: Column Visibility Management
- Create robust column visibility system
- Remember last selected columns (override defaults)
- Implement "available columns" vs "visible columns" logic
- Add column selection persistence

#### Sprint 3.3: Column Width Persistence
- Implement column width memory system
- Add intelligent auto-sizing with configurable limits
- Implement details column expansion to fill available space
- Add width reset functionality

### Phase 4: Table View Enhancements 🎯
**Goal**: Improve table view behavior and user experience

#### Sprint 4.1: Table View Auto-sizing
- Fix disabled column auto-sizing
- Implement details column expansion logic
- Add configurable maximum column widths
- Handle window resize events properly

#### Sprint 4.2: Dropdown Column Ordering
- Ensure table view dropdown uses same ordering as display
- Implement consistent column grouping
- Add "All columns" option at top with divider
- Apply shortened column names for better UX

#### Sprint 4.3: Table Behavior Improvements
- Enhance table sorting behavior
- Improve row selection and highlighting
- Add keyboard shortcuts for common actions
- Optimize table performance for large datasets

### Phase 5: Integration & Testing ✅
**Goal**: Integrate all components and ensure production readiness

#### Sprint 5.1: Component Integration
- Integrate all enhanced components
- Ensure proper signal/slot communication
- Test component interaction and data flow
- Resolve any integration conflicts

#### Sprint 5.2: Comprehensive Testing
- Create unit tests for config system
- Test database loading and filtering
- Test column management functionality
- Create integration tests for full workflow

#### Sprint 5.3: Documentation & Cleanup
- Update module documentation
- Clean up deprecated code and z_archive files
- Update README files and usage examples
- Prepare release notes

## Key Technical Considerations

### Configuration System
- Current `BaseLocalConfigV2` needs debugging - YAML generation appears broken
- Use proper naming convention for config files (defaults.yaml)
- Implement proper override hierarchy

### Column Standards
- Use existing `Columns` class from `fm.core.data_services.standards.columns` as single source of truth
- Maintain consistency with StandardColumns enum logic
- Implement proper column grouping and ordering

### Table View Integration
- Leverage existing `CustomTableView_v2` but enhance configuration capabilities
- Ensure proper integration with categorize module requirements
- Maintain backward compatibility

### Database Layer
- Use existing `DBIOService` but improve query persistence
- Implement proper caching and state management
- Handle edge cases and error scenarios

### Backward Compatibility
- Maintain existing API while adding new functionality
- Provide migration path for existing configurations
- Ensure no breaking changes to existing workflows

## Success Criteria

- ✅ Module opens with last used database query and filters
- ✅ Default columns, widths, and sort order applied consistently
- ✅ Column visibility, order, and widths remembered between sessions
- ✅ Details column expands to fill available space
- ✅ Table view dropdown shows columns in logical order
- ✅ Configuration system generates proper YAML defaults
- ✅ All preferences persist correctly

## Implementation Notes

### Current Architecture
- **Config System**: `fm/modules/categorize/config/config.py` using `BaseLocalConfigV2`
- **Table View**: `gui.shared_components.table_view_v2` with `CustomTableView_v2`
- **Column Management**: `fm.core.data_services.standards.columns.Columns`
- **Database**: `fm.core.data_services.db_io_service.DBIOService`

### Key Files to Modify
- `categorize/config/config.py` - Configuration system
- `categorize/_view/components/center_panel/transaction_view_panel.py` - Table integration
- `categorize/cat_presenter.py` - Database loading logic
- `gui/_shared_components/table_view_v2/` - Table view enhancements

### Development Approach
1. **Incremental Development**: Each sprint builds on previous work
2. **Testing First**: Create tests before implementing features
3. **Configuration Driven**: Make all behavior configurable
4. **User Experience Focus**: Prioritize usability and consistency

## Current Status

**Phase 1, Sprint 1.1**: Config System Audit & Repair - Ready to begin

## Next Steps

1. Begin Sprint 1.1 with config system audit
2. Fix YAML generation issues
3. Establish proper default values
4. Implement user preference persistence
5. Continue through phases systematically

---

*This plan addresses all issues identified in the original hit list and provides a structured approach to enhancing the categorize module.*
