"""
Central column management system for the Flatmate application.

This module provides a unified interface for all column-related operations,
integrating the enhanced column definitions with user preferences.
"""

import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

from .enhanced_columns import EnhancedStandardColumns
from .column_preferences import ColumnPreferences, get_column_preferences


@dataclass
class ColumnDisplayInfo:
    """Information needed to display a column in a table."""
    db_name: str           # Database column name
    display_name: str      # Name to show in header
    width: int            # Column width in characters
    is_editable: bool     # Whether user can edit
    sort_order: int       # Display order


class ColumnManager:
    """
    Central manager for all column-related operations.
    
    This class provides a unified interface for:
    - Converting between different column naming conventions
    - Managing user preferences for column display
    - Providing column configuration for UI components
    - Handling DataFrame transformations
    """
    
    def __init__(self, preferences: Optional[ColumnPreferences] = None):
        """
        Initialize the column manager.

        Args:
            preferences: Column preferences instance. If None, uses global instance.
        """
        self.preferences = preferences or get_column_preferences()
        self._db_repository = None  # Lazy-loaded database repository

    def _get_db_repository(self):
        """Lazy-load the database repository to avoid circular imports."""
        if self._db_repository is None:
            from fm.database_service.repository.sqlite_repository import SQLiteTransactionRepository
            self._db_repository = SQLiteTransactionRepository()
        return self._db_repository

    def get_available_columns_from_database(self) -> List[str]:
        """
        Get all available columns from the actual database schema.

        Returns:
            List of database column names available in the transactions table
        """
        try:
            repository = self._get_db_repository()
            return repository.get_available_columns()
        except Exception as e:
            # Fallback to enhanced columns if database is not available
            from fm.core.services.logger import log
            log(f"Could not get columns from database: {e}. Using enhanced columns fallback.", level="warning")
            return [col.db_name for col in EnhancedStandardColumns]
    
    # Core mapping methods
    def get_field_to_db_mapping(self) -> Dict[str, str]:
        """Get mapping from Transaction field names to database column names."""
        return EnhancedStandardColumns.get_field_to_db_mapping()
    
    def get_db_to_field_mapping(self) -> Dict[str, str]:
        """Get mapping from database column names to Transaction field names."""
        return EnhancedStandardColumns.get_db_to_field_mapping()
    
    def convert_transaction_dict_to_db_dict(self, transaction_dict: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert a dictionary with Transaction field names to database column names.
        
        Args:
            transaction_dict: Dictionary with Transaction field names as keys
            
        Returns:
            Dictionary with database column names as keys
        """
        field_to_db = self.get_field_to_db_mapping()
        result = {}
        
        for field_name, value in transaction_dict.items():
            db_name = field_to_db.get(field_name, field_name)  # Fallback to original name
            result[db_name] = value
        
        return result
    
    def convert_transactions_to_dataframe(self, transactions: List[Any]) -> pd.DataFrame:
        """
        Convert a list of Transaction objects to a DataFrame with database column names.
        
        Args:
            transactions: List of Transaction objects
            
        Returns:
            DataFrame with database column names
        """
        if not transactions:
            return pd.DataFrame()
        
        # Convert each transaction to a dict with database column names
        data = []
        for transaction in transactions:
            if hasattr(transaction, '__dict__'):
                # Convert Transaction object to dict, then map field names to db names
                transaction_dict = transaction.__dict__
                db_dict = self.convert_transaction_dict_to_db_dict(transaction_dict)
                data.append(db_dict)
            else:
                # Assume it's already a dict
                db_dict = self.convert_transaction_dict_to_db_dict(transaction)
                data.append(db_dict)
        
        return pd.DataFrame(data)
    
    # Module-specific column configuration
    def get_display_columns_for_module(self, module_name: str) -> List[str]:
        """
        Get list of database column names that should be displayed in a module.
        
        Args:
            module_name: Name of the module (e.g., 'categorize', 'update_data')
            
        Returns:
            List of database column names in display order
        """
        visible_columns = self.preferences.get_visible_columns(module_name)
        column_order = self.preferences.get_column_order(module_name)
        
        # Return columns in the specified order, including only visible ones
        ordered_columns = []
        
        # First, add columns in the specified order
        for db_name in column_order:
            if db_name in visible_columns:
                ordered_columns.append(db_name)
        
        # Then add any visible columns not in the order list
        for db_name in visible_columns:
            if db_name not in ordered_columns:
                ordered_columns.append(db_name)
        
        return ordered_columns
    
    def get_column_display_mapping(self, module_name: str) -> Dict[str, str]:
        """
        Get mapping from database column names to display names for a module.
        
        Args:
            module_name: Name of the module
            
        Returns:
            Dictionary mapping db_name -> display_name
        """
        visible_columns = self.get_display_columns_for_module(module_name)
        mapping = {}
        
        for db_name in visible_columns:
            # Check for custom display name first
            custom_name = self.preferences.get_custom_display_name(module_name, db_name)
            if custom_name:
                mapping[db_name] = custom_name
            else:
                # Use standard display name
                column = EnhancedStandardColumns.find_by_db_name(db_name)
                if column:
                    mapping[db_name] = column.display_name
                else:
                    # Fallback for unknown columns
                    mapping[db_name] = db_name.replace('_', ' ').title()
        
        return mapping
    
    def get_column_widths(self, module_name: str) -> Dict[str, int]:
        """
        Get column widths for a module.
        
        Args:
            module_name: Name of the module
            
        Returns:
            Dictionary mapping display_name -> width_in_characters
        """
        return self.preferences.get_column_widths(module_name)
    
    def get_editable_columns(self, module_name: str) -> List[str]:
        """
        Get list of database column names that are editable in a module.
        
        Args:
            module_name: Name of the module
            
        Returns:
            List of database column names that can be edited
        """
        visible_columns = self.get_display_columns_for_module(module_name)
        editable_columns = []
        
        for db_name in visible_columns:
            column = EnhancedStandardColumns.find_by_db_name(db_name)
            if column and column.is_editable:
                editable_columns.append(db_name)
        
        return editable_columns
    
    def get_column_display_info(self, module_name: str) -> List[ColumnDisplayInfo]:
        """
        Get complete display information for all columns in a module.
        
        Args:
            module_name: Name of the module
            
        Returns:
            List of ColumnDisplayInfo objects with all display details
        """
        display_columns = self.get_display_columns_for_module(module_name)
        display_mapping = self.get_column_display_mapping(module_name)
        width_mapping = self.get_column_widths(module_name)
        editable_columns = set(self.get_editable_columns(module_name))
        
        info_list = []
        for i, db_name in enumerate(display_columns):
            display_name = display_mapping.get(db_name, db_name)
            width = width_mapping.get(display_name, 15)  # Default width
            is_editable = db_name in editable_columns
            
            info_list.append(ColumnDisplayInfo(
                db_name=db_name,
                display_name=display_name,
                width=width,
                is_editable=is_editable,
                sort_order=i
            ))
        
        return info_list
    
    # Utility methods for UI components
    def prepare_dataframe_for_display(self, df: pd.DataFrame, module_name: str) -> Tuple[pd.DataFrame, Dict[str, str], Dict[str, int]]:
        """
        Prepare a DataFrame for display in a module.
        
        Args:
            df: DataFrame with database column names
            module_name: Name of the module
            
        Returns:
            Tuple of (filtered_df, column_name_mapping, column_widths)
        """
        # Get columns to display
        display_columns = self.get_display_columns_for_module(module_name)
        
        # Filter DataFrame to only include visible columns that exist
        available_columns = [col for col in display_columns if col in df.columns]
        filtered_df = df[available_columns].copy()
        
        # Get display mappings
        column_mapping = self.get_column_display_mapping(module_name)
        column_widths = self.get_column_widths(module_name)
        
        return filtered_df, column_mapping, column_widths
    
    def validate_dataframe_columns(self, df: pd.DataFrame, module_name: str) -> List[str]:
        """
        Validate that a DataFrame has the required columns for a module.
        
        Args:
            df: DataFrame to validate
            module_name: Name of the module
            
        Returns:
            List of missing required column names
        """
        required_columns = [col.db_name for col in EnhancedStandardColumns.get_required_columns()]
        missing_columns = []
        
        for col_name in required_columns:
            if col_name not in df.columns:
                missing_columns.append(col_name)
        
        return missing_columns


# Global instance for easy access
_global_column_manager: Optional[ColumnManager] = None

def get_column_manager() -> ColumnManager:
    """Get the global column manager instance."""
    global _global_column_manager
    if _global_column_manager is None:
        _global_column_manager = ColumnManager()
    return _global_column_manager
