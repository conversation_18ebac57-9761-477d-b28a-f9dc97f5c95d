"""
Utility for tracking processing state across the data pipeline.
"""
from typing import List, Set


class Tracker:
    """
    Base class for tracking unique items.
    """
    def __init__(self):
        self._items: Set[str] = set()

    def add(self, item: str):
        """Add a single item."""
        self._items.add(item)

    def extend(self, items: List[str]):
        """Add multiple items."""
        self._items.update(items)

    def get(self) -> List[str]:
        """Retrieve tracked items."""
        return list(self._items)

    def clear(self):
        """Clear all tracked items."""
        self._items.clear()


class MetadataColumnTracker(Tracker):
    """
    Singleton tracker for metadata columns.
    """
    _instance = None

    def __new__(cls):
        if not cls._instance:
            cls._instance = super().__new__(cls)
        return cls._instance


class ProcessedFilesTracker(Tracker):
    """
    Singleton tracker for processed files.
    """
    _instance = None

    def __new__(cls):
        if not cls._instance:
            cls._instance = super().__new__(cls)
        return cls._instance


class UnrecognisedFilesTracker(Tracker):
    """
    Singleton tracker for unrecognised files.
    """
    _instance = None

    def __new__(cls):
        if not cls._instance:
            cls._instance = super().__new__(cls)
        return cls._instance


# Global singleton instances
metadata_column_tracker = MetadataColumnTracker()
processed_files_tracker = ProcessedFilesTracker()
unrecognised_files_tracker = UnrecognisedFilesTracker()

clear_all_trackers = lambda: (
    metadata_column_tracker.clear(),
    processed_files_tracker.clear(),
    unrecognised_files_tracker.clear()
)