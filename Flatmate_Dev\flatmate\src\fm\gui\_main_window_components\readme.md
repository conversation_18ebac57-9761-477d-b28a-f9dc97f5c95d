this folder is for main_window components that can be addressed by feature modules but are not owned by their views, they are owned by main_window
they should have their own methods 
# ? should modules address these directly or addres main window?
I'm unsure, this is part of an effort to sort out the clutter in fm/gui and main_window.py amd make it easier to navigate, and create new features.
At the moment it's a mare and breaks everything, everytime.
