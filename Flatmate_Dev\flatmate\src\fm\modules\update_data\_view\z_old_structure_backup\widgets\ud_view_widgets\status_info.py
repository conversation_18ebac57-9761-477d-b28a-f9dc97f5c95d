"""
Status Information Widget for Update Data Module.

Provides a dynamic status and information display area.
"""

from PySide6.QtCore import Qt
from PySide6.QtGui import QFont
from PySide6.QtWidgets import QFrame, QLabel, QVBoxLayout, QWidget


class StatusInfoWidget(QWidget):
    """
    Widget to display current module status, 
    warnings, and contextual information.
    """
    
    def __init__(self, parent=None):
        """Initialize the status info widget."""
        super().__init__(parent)
        
        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Module title
        self.module_title = QLabel("Update Data")
        self.module_title.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        self.module_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.module_title)
        
        # Status frame
        self.status_frame = QFrame()
        self.status_frame.setFrameShape(QFrame.Shape.Box)
        self.status_frame.setStyleSheet("""
            QFrame {
                border: 2px solid #cccccc;
                border-radius: 5px;
                background-color: #f0f0f0;
            }
        """)
        
        status_layout = QVBoxLayout(self.status_frame)
        
        # Current action
        self.action_label = QLabel("Waiting for file selection")
        self.action_label.setFont(QFont("Arial", 10))
        status_layout.addWidget(self.action_label)
        
        # Warning/Info label
        self.info_label = QLabel()
        self.info_label.setWordWrap(True)
        status_layout.addWidget(self.info_label)
        
        layout.addWidget(self.status_frame)
        layout.addStretch(1)
    
    def set_action(self, action_text):
        """
        Set the current action text.
        
        Args:
            action_text: Description of current action
        """
        self.action_label.setText(action_text)
    
    def show_info(self, message, color='blue'):
        """
        Display an informational message.
        
        Args:
            message: Information message
            color: Color of the message
        """
        self.info_label.setText(message)
        self.info_label.setStyleSheet(f"color: {color};")
    
    def show_warning(self, message):
        """
        Display a warning message.
        
        Args:
            message: Warning message
        """
        self.show_info(f"⚠️ {message}", 'orange')
    
    def show_error(self, message):
        """
        Display an error message.
        
        Args:
            message: Error message
        """
        self.show_info(f"❌ {message}", 'red')
    
    def clear_message(self):
        """Clear the current message."""
        self.info_label.clear()
