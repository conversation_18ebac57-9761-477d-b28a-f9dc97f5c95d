"""
Base pane component for all panes in the Update Data module.
"""

from PySide6.QtCore import Signal
from PySide6.QtWidgets import QWidget


class BasePane(QWidget):
    """Base class for all panes in the Update Data module."""
    
    # Signals for publishing events to subscribers
    publish_activated = Signal()  # Published when pane becomes active
    publish_deactivated = Signal()  # Published when pane becomes inactive
    
    def __init__(self, parent=None):
        """Initialize the base pane."""
        super().__init__(parent)
        self._is_active = False
    
    def activate(self):
        """Activate this pane."""
        self._is_active = True
        self.publish_activated.emit()
    
    def deactivate(self):
        """Deactivate this pane."""
        self._is_active = False
        self.publish_deactivated.emit()
    
    def is_active(self):
        """Return whether this pane is active."""
        return self._is_active
    
    def clear(self):
        """Clear any data or selections in this pane."""
        pass  # To be implemented by subclasses
        
    def show_component(self):
        """Show this component."""
        self.show()
        self.activate()
    
    def hide_component(self):
        """Hide this component."""
        self.hide()
        self.deactivate()
