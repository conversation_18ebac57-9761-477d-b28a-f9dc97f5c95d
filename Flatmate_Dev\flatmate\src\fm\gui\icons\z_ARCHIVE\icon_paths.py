"""
Icon paths organized using dataclasses for better structure and IDE support.
Paths are validated at import time to fail loudly if icons are missing.
"""

from dataclasses import dataclass, field
from pathlib import Path
from typing import ClassVar, Type

# Base directory for icons
ICON_DIR = Path(__file__).parent


def validate_icon_paths(cls: Type, category: str) -> None:
    """
    Validate that all icon paths in a class exist.
    
    Args:
        cls: The class containing icon paths
        category: Category name for error messages
    
    Raises:
        FileNotFoundError: If any icon path doesn't exist
    """
    # Get all class variables that are Path objects
    for name, path in vars(cls).items():
        if isinstance(path, Path) and name.isupper():
            if not path.exists():
                raise FileNotFoundError(
                    f"{category} icon not found: {path} (defined as {name})"
                )


@dataclass
class NavIcons:
    # Class variable to store the base directory
    BASE_DIR: ClassVar[Path] = ICON_DIR / "nav_pane"

    # Navigation icons
    HOME: Path = field(
        default_factory=lambda: NavIcons.BASE_DIR / "home" / "selected" / "home.svg"
    )
    PROFILES: Path = field(
        default_factory=lambda: NavIcons.BASE_DIR
        / "profiles"
        / "selected"
        / "profiles.svg"
    )
    IMPORT_DATA: Path = field(
        default_factory=lambda: NavIcons.BASE_DIR
        / "import_data"
        / "selected"
        / "import_data.svg"
    )
    VIEW_DATA: Path = field(
        default_factory=lambda: NavIcons.BASE_DIR
        / "view_data"
        / "selected"
        / "view_data.svg"
    )


@dataclass
class SettingsIcons:
    # Class variable to store the base directory
    BASE_DIR: ClassVar[Path] = ICON_DIR / "settings"

    # Settings icons
    GEAR: Path = field(
        default_factory=lambda: SettingsIcons.BASE_DIR / "selected" / "gear.svg"
    )


# Validate all paths at import time
validate_icon_paths(NavIcons, "Navigation")
validate_icon_paths(SettingsIcons, "Settings")
