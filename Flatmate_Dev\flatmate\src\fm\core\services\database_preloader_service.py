"""
Database Preloader Service

Loads the entire transaction database into memory at application startup for instant access.
Provides in-memory filtering and querying capabilities for better user experience.
"""

import time
import pandas as pd
from typing import Optional, Dict, Any, List
from datetime import datetime, date

from fm.core.services.cache_service import cache_service
from fm.core.data_services.db_io_service import DBIOService
from fm.core.services.event_bus import global_event_bus, Events
from fm.core.services.logger import log


class DatabasePreloaderService:
    """Service for preloading and managing the entire transaction database in memory."""
    
    def __init__(self):
        """Initialize the database preloader service."""
        self._db_service = DBIOService()
        self._full_dataset: Optional[pd.DataFrame] = None
        self._load_time: Optional[datetime] = None
        self._is_loaded = False
        
    def preload_database(self, show_progress: bool = True) -> bool:
        """Preload the entire transaction database into memory.
        
        Args:
            show_progress: Whether to show loading progress via InfoBar
            
        Returns:
            True if successful, False otherwise
        """
        log("Starting database preload into memory...", level="info")
        start_time = time.time()
        
        try:
            if show_progress:
                global_event_bus.publish(
                    Events.INFO_MESSAGE,
                    {
                        "text": "Loading transaction database into memory...",
                        "is_loading": True,
                        "progress": 0,
                        "total": 1
                    }
                )
            
            # Load all transactions from database (no filters = everything)
            transactions = self._db_service.list_transactions()
            total_count = len(transactions)
            
            log(f"Retrieved {total_count} transactions from database", level="info")
            
            if total_count == 0:
                log("No transactions found in database", level="warning")
                self._full_dataset = pd.DataFrame()
                self._is_loaded = True
                return True
            
            # Convert to DataFrame with progress updates
            if show_progress:
                global_event_bus.publish(
                    Events.INFO_MESSAGE,
                    {
                        "text": f"Processing {total_count} transactions...",
                        "is_loading": True,
                        "progress": 0,
                        "total": total_count
                    }
                )
            
            # Convert Transaction objects to DataFrame
            data_rows = []
            for i, txn in enumerate(transactions):
                # Convert Transaction object to dict/row
                row = {
                    'date': txn.date,
                    'details': txn.details,
                    'amount': txn.amount,
                    'balance': txn.balance,
                    'account': getattr(txn, 'account', ''),  # Fixed: use 'account' not 'account_number'
                    'source_uid': getattr(txn, 'source_uid', ''),
                    'category': getattr(txn, 'category', ''),
                    'tags': getattr(txn, 'tags', ''),
                    'notes': getattr(txn, 'notes', ''),
                    'is_processed': getattr(txn, 'is_processed', False),
                    # Add other fields as needed
                    'db_uid': getattr(txn, 'db_uid', ''),
                    'import_date': getattr(txn, 'import_date', None),
                    'modified_date': getattr(txn, 'modified_date', None),
                }
                data_rows.append(row)
                
                # Update progress periodically
                if show_progress and i % 100 == 0:
                    global_event_bus.publish(
                        Events.INFO_MESSAGE,
                        {
                            "text": f"Processing {i+1}/{total_count} transactions...",
                            "is_loading": True,
                            "progress": i,
                            "total": total_count
                        }
                    )
            
            # Create DataFrame
            self._full_dataset = pd.DataFrame(data_rows)
            self._load_time = datetime.now()
            self._is_loaded = True
            
            # Cache the dataset for other services
            cache_service.put('preloaded_transactions', self._full_dataset)
            cache_service.put('preload_time', self._load_time)
            
            # Calculate performance metrics
            elapsed = time.time() - start_time
            rate = total_count / elapsed if elapsed > 0 else 0
            
            log(f"Successfully preloaded {total_count} transactions in {elapsed:.1f}s ({rate:.1f} txns/s)", 
                level="info")
            
            if show_progress:
                global_event_bus.publish(
                    Events.INFO_MESSAGE,
                    {
                        "text": f"Database loaded: {total_count} transactions in {elapsed:.1f}s",
                        "is_loading": False,
                        "progress": total_count,
                        "total": total_count
                    }
                )
            
            return True
            
        except Exception as e:
            error_msg = f"Error preloading database: {str(e)}"
            log(error_msg, level="error")
            
            if show_progress:
                global_event_bus.publish(
                    Events.INFO_MESSAGE,
                    {
                        "text": error_msg,
                        "is_loading": False,
                        "is_error": True
                    }
                )
            
            return False
    
    def get_filtered_data(self, filters: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """Get filtered data from the preloaded dataset.
        
        Args:
            filters: Dictionary of filters to apply
                - start_date: Filter transactions after this date
                - end_date: Filter transactions before this date
                - account: Filter by account number
                - search: Search in details/notes
                - amount_min: Minimum amount
                - amount_max: Maximum amount
                
        Returns:
            Filtered DataFrame
        """
        if not self._is_loaded or self._full_dataset is None:
            log("Database not preloaded. Call preload_database() first.", level="warning")
            return pd.DataFrame()
        
        df = self._full_dataset.copy()
        
        if not filters:
            return df
        
        # Apply date filters
        if 'start_date' in filters and filters['start_date']:
            start_date = filters['start_date']
            if isinstance(start_date, str):
                start_date = pd.to_datetime(start_date).date()
            df = df[pd.to_datetime(df['date']).dt.date >= start_date]
        
        if 'end_date' in filters and filters['end_date']:
            end_date = filters['end_date']
            if isinstance(end_date, str):
                end_date = pd.to_datetime(end_date).date()
            df = df[pd.to_datetime(df['date']).dt.date <= end_date]
        
        # Apply account filter
        if 'account' in filters and filters['account']:
            df = df[df['account'] == filters['account']]
        
        # Apply search filter
        if 'search' in filters and filters['search']:
            search_term = filters['search'].lower()
            mask = (
                df['details'].str.lower().str.contains(search_term, na=False) |
                df['notes'].str.lower().str.contains(search_term, na=False)
            )
            df = df[mask]
        
        # Apply amount filters
        if 'amount_min' in filters and filters['amount_min'] is not None:
            df = df[df['amount'] >= filters['amount_min']]
        
        if 'amount_max' in filters and filters['amount_max'] is not None:
            df = df[df['amount'] <= filters['amount_max']]
        
        return df
    
    def get_unique_accounts(self) -> List[str]:
        """Get list of unique account numbers from preloaded data.
        
        Returns:
            List of unique account numbers
        """
        if not self._is_loaded or self._full_dataset is None:
            return []
        
        return sorted(self._full_dataset['account'].dropna().unique().tolist())
    
    def is_loaded(self) -> bool:
        """Check if database is preloaded.
        
        Returns:
            True if database is loaded in memory
        """
        return self._is_loaded
    
    def get_load_info(self) -> Dict[str, Any]:
        """Get information about the preloaded dataset.
        
        Returns:
            Dictionary with load information
        """
        if not self._is_loaded:
            return {"loaded": False}
        
        return {
            "loaded": True,
            "total_transactions": len(self._full_dataset) if self._full_dataset is not None else 0,
            "load_time": self._load_time,
            "unique_accounts": len(self.get_unique_accounts()),
            "memory_usage_mb": self._full_dataset.memory_usage(deep=True).sum() / 1024 / 1024 if self._full_dataset is not None else 0
        }
    
    def refresh_data(self) -> bool:
        """Refresh the preloaded data from database.
        
        Returns:
            True if successful
        """
        log("Refreshing preloaded database...", level="info")
        return self.preload_database(show_progress=True)


# Global instance
database_preloader = DatabasePreloaderService()
