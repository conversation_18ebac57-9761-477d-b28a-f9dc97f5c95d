# SUPERSEDED: Flatmate Data System

**This documentation has been superseded by the rationalized data system.**

**Current structure:**
- `core/data_services/` - Enhanced service layer with column management
- `core/database/` - Core database implementation (renamed from database_v2)
- `core/standards/` - Canonical column definitions

---

# Original Documentation (ARCHIVED)

This directory contains the core data management components for the Flatmate application. The data system provides a unified interface for storing, retrieving, and manipulating financial transaction data.

> **Note**: For comprehensive architectural documentation, see the [Central Database Implementation](../../../docs/database/central_database_implementation.md) document.

## Directory Structure

- `__init__.py` - Exports the data service singleton
- `converters.py` - Contains utilities for converting between data formats
- `repository/` - Contains data models and storage implementations
- `example_usage.py` - Examples of how to use the data service

## Key Components

### Data Service

The `data_service` singleton provides a clean interface for all database operations:

```python
from fm.data import data_service

# Import transactions
result = data_service.import_transactions(transactions)

# Query transactions
all_transactions = data_service.get_transactions()
date_transactions = data_service.get_transactions_by_date_range(start_date, end_date)
```

### Transaction Model

The `Transaction` class in `repository/transaction_repository.py` is the core data model:

```python
from fm.data.repository.transaction_repository import Transaction

# Create a transaction
transaction = Transaction(
    date=datetime.now(),
    description="Grocery shopping",
    amount=-45.67,
    account_number="*********"
)
```

## Integration with Other Modules

The data system is designed to integrate seamlessly with other modules:

1. **Update Data Module** - Can save processed transactions directly to the database
2. **Reporting Module** - Can query transactions for analysis and visualization
3. **Settings Module** - Can configure database preferences

## Best Practices

When working with the data system:

1. Always use the data_service interface rather than direct database access
2. Use the Transaction model for all transaction data
3. Leverage the converters for transforming between data formats
4. Maintain separation between UI components and data operations

## Further Documentation

For more detailed information, see:
- `/docs/database/central_database_implementation.md` - Comprehensive documentation
- `example_usage.py` - Code examples for common operations
