# Export Functionality Implementation Plan
**Date**: 2025-07-18  
**Status**: Ready for Implementation  
**Estimated Time**: 50 minutes total

## Executive Summary
The current export functionality exports original data instead of filtered/sorted data visible in the table. This implementation plan provides specific, actionable steps to fix this WYSIWYG violation.

## Root Cause Analysis
- **File**: `flatmate/src/fm/gui/_shared_components/table_view_v2/components/table_view_core.py`
- **Method**: `_export_data()` (line 314)
- **Issue**: Calls `self.get_dataframe()` which returns original data from `EnhancedTableModel._original_data`
- **Problem**: Bypasses `EnhancedFilterProxyModel` that handles filtering and sorting

## Implementation Tasks

### Task 1: Add get_visible_dataframe() Method ⏱️ 20 minutes
**File**: `flatmate/src/fm/gui/_shared_components/table_view_v2/components/table_view_core.py`  
**Action**: Add new method after line 367  
**Dependencies**: None

**Exact Code to Add**:
```python
def get_visible_dataframe(self) -> pd.DataFrame:
    """Get the currently visible data as a pandas DataFrame.
    
    This method respects all active filters and sorting applied through
    the proxy model, returning exactly what the user sees in the table.
    
    Returns:
        pd.DataFrame: The filtered and sorted data as displayed
    """
    import pandas as pd
    from PySide6.QtCore import Qt
    
    proxy_model = self.model()  # EnhancedFilterProxyModel
    source_model = proxy_model.sourceModel()  # EnhancedTableModel
    
    # Handle empty table
    if proxy_model.rowCount() == 0:
        return pd.DataFrame()
    
    # Get visible columns and their names
    visible_columns = []
    column_names = []
    for col in range(source_model.columnCount()):
        if not self.isColumnHidden(col):
            visible_columns.append(col)
            column_names.append(source_model.headerData(col, Qt.Horizontal))
    
    # Extract visible data in display order (respects sorting)
    data_rows = []
    for proxy_row in range(proxy_model.rowCount()):
        row_data = []
        for col in visible_columns:
            # Get data from proxy model (this respects filtering and sorting)
            proxy_index = proxy_model.index(proxy_row, col)
            
            # Get the actual data value
            # Try DisplayRole first, then UserRole for original values
            value = proxy_model.data(proxy_index, Qt.DisplayRole)
            if value is None:
                # Fallback to source model if needed
                source_index = proxy_model.mapToSource(proxy_index)
                value = source_model.data(source_index, Qt.UserRole)
            
            row_data.append(value)
        data_rows.append(row_data)
    
    # Create DataFrame with proper column names
    df = pd.DataFrame(data_rows, columns=column_names)
    return df
```

### Task 2: Update _export_data() Method ⏱️ 15 minutes
**File**: `flatmate/src/fm/gui/_shared_components/table_view_v2/components/table_view_core.py`  
**Action**: Replace existing method (lines 309-325)  
**Dependencies**: Task 1 completed

**Find This Code** (line 314):
```python
df = self.get_dataframe()
```

**Replace With**:
```python
df = self.get_visible_dataframe()
```

**Complete Updated Method**:
```python
def _export_data(self, format_type):
    """Export data to file."""
    from PySide6.QtWidgets import QFileDialog, QMessageBox
    import logging
    
    try:
        # Get visible DataFrame (respects filters and sorting)
        df = self.get_visible_dataframe()
        
        # Check if there's data to export
        if df.empty:
            QMessageBox.information(
                self, 
                "Export", 
                "No data to export. Please check your filters."
            )
            return
        
        # Handle different export formats
        if format_type == "csv":
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Export to CSV", "", "CSV Files (*.csv)")
            if file_path:
                df.to_csv(file_path, index=False)
                QMessageBox.information(
                    self, 
                    "Export Successful", 
                    f"Data exported successfully to {file_path}\n"
                    f"Exported {len(df)} rows and {len(df.columns)} columns."
                )
                
        elif format_type == "excel":
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Export to Excel", "", "Excel Files (*.xlsx)")
            if file_path:
                df.to_excel(file_path, index=False)
                QMessageBox.information(
                    self, 
                    "Export Successful", 
                    f"Data exported successfully to {file_path}\n"
                    f"Exported {len(df)} rows and {len(df.columns)} columns."
                )
                
    except PermissionError:
        QMessageBox.critical(
            self, 
            "Export Error", 
            "Permission denied. Please check that the file is not open in another application."
        )
    except Exception as e:
        QMessageBox.critical(
            self, 
            "Export Error", 
            f"Failed to export data: {str(e)}"
        )
        logging.error(f"Export failed: {e}", exc_info=True)
```

### Task 3: Verify Signal Connections ⏱️ 5 minutes
**File**: `flatmate/src/fm/gui/_shared_components/table_view_v2/fm_table_view.py`  
**Action**: Verify existing connections (no changes needed)  
**Dependencies**: None

**Check These Lines**:
- Line 345-346: Signal connections exist
- Line 524-530: Handler methods exist

**Expected Result**: No changes needed - connections are already correct.

### Task 4: Integration Testing ⏱️ 10 minutes
**Location**: Categorize module  
**Action**: Test the implementation  
**Dependencies**: Tasks 1-3 completed

**Test Steps**:
1. Launch application
2. Navigate to categorize module
3. Load transaction data
4. Apply date filter (e.g., last 30 days)
5. Sort by amount (descending)
6. Hide 'balance' column
7. Click Export → CSV
8. Verify exported CSV contains:
   - Only filtered transactions (last 30 days)
   - Data sorted by amount (descending)
   - No 'balance' column
   - Matches visible table exactly

## Validation Criteria

### Must Pass Tests:
1. **Filter Test**: Apply text filter → export → verify only filtered data exported
2. **Sort Test**: Sort by date → export → verify data exported in sorted order  
3. **Hidden Column Test**: Hide column → export → verify column not in export
4. **Empty Result Test**: Filter to no results → export → verify user-friendly message
5. **Error Test**: Try to export to read-only location → verify error message

### Success Metrics:
- ✅ Export matches visible table 100% of the time
- ✅ No crashes or silent failures
- ✅ Clear error messages for all failure modes
- ✅ Export completes within 5 seconds for 1000+ rows

## Files Modified Summary
1. `flatmate/src/fm/gui/_shared_components/table_view_v2/components/table_view_core.py`
   - Added: `get_visible_dataframe()` method
   - Modified: `_export_data()` method

## Risk Assessment
- **Low Risk**: Changes are isolated to export functionality
- **No Breaking Changes**: Existing functionality remains unchanged
- **Backward Compatible**: All existing interfaces preserved

## Rollback Plan
If issues arise, revert `_export_data()` method to use `self.get_dataframe()` instead of `self.get_visible_dataframe()`.

## Next Steps After Implementation
1. Test with real transaction data
2. Gather user feedback
3. Consider Phase 2 enhancements:
   - Export configuration dialog
   - Additional file formats
   - Export templates

## Implementation Checklist
- [ ] Task 1: Add get_visible_dataframe() method
- [ ] Task 2: Update _export_data() method  
- [ ] Task 3: Verify signal connections
- [ ] Task 4: Integration testing
- [ ] Validation: All test criteria pass
- [ ] Documentation: Update user guide if needed

**Ready to implement!** 🚀
