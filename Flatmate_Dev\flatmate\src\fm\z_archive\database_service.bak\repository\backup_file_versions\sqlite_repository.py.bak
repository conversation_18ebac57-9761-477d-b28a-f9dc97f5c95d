"""
SQLite implementation of the transaction repository.
"""
import os
import sqlite3
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple

from .transaction_repository import ImportR<PERSON>ult, Transaction, TransactionRepository


class SQLiteTransactionRepository(TransactionRepository):
    """SQLite implementation of transaction repository."""
    
    def __init__(self, db_path: str = "~/.flatmate/data/transactions.db"):
        """
        Initialize the SQLite repository.
        
        Args:
            db_path: Path to the SQLite database file
        """
        self.db_path = os.path.expanduser(db_path)
        self._ensure_db_exists()
        
    def _ensure_db_exists(self):
        """Ensure the database file and tables exist."""
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        # Create tables if they don't exist
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY,
                date TEXT NOT NULL,
                description TEXT NOT NULL,
                amount REAL NOT NULL,
                account_number TEXT,
                transaction_type TEXT,
                category TEXT,
                notes TEXT,
                tags TEXT,
                source_bank TEXT,
                source_file TEXT,
                import_date TEXT,
                modified_date TEXT,
                is_deleted INTEGER DEFAULT 0
            )
            ''')
            
            # Create indexes for common queries
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_transactions_account ON transactions(account_number)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_transactions_category ON transactions(category)')
            
            # Create unique index for duplicate detection
            cursor.execute('''
            CREATE UNIQUE INDEX IF NOT EXISTS idx_transactions_unique 
            ON transactions(date, description, amount, account_number)
            WHERE is_deleted = 0
            ''')
            
            conn.commit()
    
    def _get_connection(self) -> sqlite3.Connection:
        """Get a connection to the SQLite database."""
        return sqlite3.connect(self.db_path)
    
    def add_transactions(self, transactions: List[Transaction]) -> ImportResult:
        """
        Add new transactions to the repository.
        
        Args:
            transactions: List of transactions to add
            
        Returns:
            ImportResult with counts of added and duplicate transactions
        """
        result = ImportResult()
        
        if not transactions:
            return result
            
        # Set import date if not already set
        now = datetime.now()
        for transaction in transactions:
            if not transaction.import_date:
                transaction.import_date = now
            if not transaction.modified_date:
                transaction.modified_date = now
        
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            for transaction in transactions:
                try:
                    cursor.execute('''
                    INSERT INTO transactions 
                    (date, description, amount, account_number, transaction_type, 
                     category, notes, tags, source_bank, source_file, import_date, modified_date)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        transaction.date.isoformat(),
                        transaction.description,
                        transaction.amount,
                        transaction.account_number,
                        transaction.transaction_type,
                        transaction.category,
                        transaction.notes,
                        transaction.tags,
                        transaction.source_bank,
                        transaction.source_file,
                        transaction.import_date.isoformat() if transaction.import_date else None,
                        transaction.modified_date.isoformat() if transaction.modified_date else None
                    ))
                    result.added_count += 1
                except sqlite3.IntegrityError:
                    # This is likely a duplicate transaction
                    result.duplicate_count += 1
                except Exception as e:
                    result.error_count += 1
                    result.errors.append(str(e))
            
            conn.commit()
            
        return result
    
    def get_transactions(self, filters: Optional[Dict] = None) -> List[Transaction]:
        """
        Retrieve transactions matching the filters.
        
        Args:
            filters: Dictionary of filter criteria
            
        Returns:
            List of matching transactions
        """
        query = "SELECT * FROM transactions WHERE is_deleted = 0"
        params = []
        
        if filters:
            filter_clauses, filter_params = self._build_filter_clauses(filters)
            if filter_clauses:
                query += " AND " + " AND ".join(filter_clauses)
                params.extend(filter_params)
        
        # Add default sorting
        query += " ORDER BY date DESC"
        
        with self._get_connection() as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            return [self._row_to_transaction(row) for row in rows]
    
    def _build_filter_clauses(self, filters: Dict) -> Tuple[List[str], List[Any]]:
        """
        Build SQL filter clauses from a filters dictionary.
        
        Args:
            filters: Dictionary of filter criteria
            
        Returns:
            Tuple of (list of filter clauses, list of parameters)
        """
        clauses = []
        params = []
        
        # Date range filter
        if 'date_from' in filters:
            clauses.append("date >= ?")
            params.append(filters['date_from'].isoformat() if isinstance(filters['date_from'], datetime) else filters['date_from'])
        
        if 'date_to' in filters:
            clauses.append("date <= ?")
            params.append(filters['date_to'].isoformat() if isinstance(filters['date_to'], datetime) else filters['date_to'])
        
        # Account filter
        if 'account_number' in filters:
            clauses.append("account_number = ?")
            params.append(filters['account_number'])
        
        # Category filter
        if 'category' in filters:
            clauses.append("category = ?")
            params.append(filters['category'])
        
        # Amount range filter
        if 'amount_min' in filters:
            clauses.append("amount >= ?")
            params.append(filters['amount_min'])
        
        if 'amount_max' in filters:
            clauses.append("amount <= ?")
            params.append(filters['amount_max'])
        
        # Text search
        if 'search_text' in filters and filters['search_text']:
            clauses.append("(description LIKE ? OR notes LIKE ?)")
            search_param = f"%{filters['search_text']}%"
            params.append(search_param)
            params.append(search_param)
        
        return clauses, params
    
    def _row_to_transaction(self, row: sqlite3.Row) -> Transaction:
        """Convert a database row to a Transaction object."""
        # Parse dates
        date = datetime.fromisoformat(row['date']) if row['date'] else None
        import_date = datetime.fromisoformat(row['import_date']) if row['import_date'] else None
        modified_date = datetime.fromisoformat(row['modified_date']) if row['modified_date'] else None
        
        return Transaction(
            date=date,
            description=row['description'],
            amount=row['amount'],
            account_number=row['account_number'],
            transaction_type=row['transaction_type'],
            category=row['category'],
            notes=row['notes'],
            tags=row['tags'],
            source_bank=row['source_bank'],
            source_file=row['source_file'],
            import_date=import_date,
            modified_date=modified_date,
            transaction_id=row['id']
        )
    
    def update_transaction(self, transaction_id: int, data: Dict) -> bool:
        """
        Update a specific transaction.
        
        Args:
            transaction_id: ID of the transaction to update
            data: Dictionary of fields to update
            
        Returns:
            True if update was successful
        """
        if not data:
            return False
            
        # Build update query
        fields = []
        params = []
        
        for key, value in data.items():
            if key == 'transaction_id' or key == 'id':
                continue
                
            fields.append(f"{key} = ?")
            
            # Handle datetime objects
            if isinstance(value, datetime):
                params.append(value.isoformat())
            else:
                params.append(value)
        
        if not fields:
            return False
            
        # Add modified date
        fields.append("modified_date = ?")
        params.append(datetime.now().isoformat())
        
        # Add transaction ID
        params.append(transaction_id)
        
        query = f"UPDATE transactions SET {', '.join(fields)} WHERE id = ?"
        
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            conn.commit()
            
            return cursor.rowcount > 0
    
    def delete_transaction(self, transaction_id: int) -> bool:
        """
        Mark a transaction as deleted.
        
        Args:
            transaction_id: ID of the transaction to delete
            
        Returns:
            True if deletion was successful
        """
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "UPDATE transactions SET is_deleted = 1, modified_date = ? WHERE id = ?",
                (datetime.now().isoformat(), transaction_id)
            )
            conn.commit()
            
            return cursor.rowcount > 0
            
    def delete_all_transactions(self) -> int:
        """
        Mark all transactions as deleted.
        
        Returns:
            Number of transactions deleted
        """
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "UPDATE transactions SET is_deleted = 1, modified_date = ? WHERE is_deleted = 0",
                (datetime.now().isoformat(),)
            )
            conn.commit()
            
            return cursor.rowcount
    
    def get_statistics(self) -> Dict:
        """
        Get statistics about the stored transactions.
        
        Returns:
            Dictionary with transaction statistics
        """
        stats = {
            'total_count': 0,
            'earliest_date': None,
            'latest_date': None,
            'total_amount': 0,
            'account_counts': {},
            'category_counts': {}
        }
        
        with self._get_connection() as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # Get total count
            cursor.execute("SELECT COUNT(*) as count FROM transactions WHERE is_deleted = 0")
            stats['total_count'] = cursor.fetchone()['count']
            
            if stats['total_count'] == 0:
                return stats
                
            # Get date range
            cursor.execute("SELECT MIN(date) as min_date, MAX(date) as max_date FROM transactions WHERE is_deleted = 0")
            row = cursor.fetchone()
            stats['earliest_date'] = row['min_date']
            stats['latest_date'] = row['max_date']
            
            # Get total amount
            cursor.execute("SELECT SUM(amount) as total FROM transactions WHERE is_deleted = 0")
            stats['total_amount'] = cursor.fetchone()['total']
            
            # Get account counts
            cursor.execute("""
            SELECT account_number, COUNT(*) as count 
            FROM transactions 
            WHERE is_deleted = 0 AND account_number != ''
            GROUP BY account_number
            """)
            for row in cursor.fetchall():
                stats['account_counts'][row['account_number']] = row['count']
                
            # Get category counts
            cursor.execute("""
            SELECT category, COUNT(*) as count 
            FROM transactions 
            WHERE is_deleted = 0 AND category != ''
            GROUP BY category
            """)
            for row in cursor.fetchall():
                stats['category_counts'][row['category']] = row['count']
                
        return stats
