[tool:pytest]
testpaths = tests
python_files = test_*.py
python_functions = test_*
python_classes = Test*
addopts = -v --cov=src --cov-report=term-missing

[coverage:run]
source = src
omit = 
    */tests/*
    */__pycache__/*
    */.*

[coverage:report]
exclude_lines =
    # Have to re-enable the standard pragma
    pragma: no cover
    # Don't complain about missing debug-only code:
    def __repr__
    if self\.debug
    raise NotImplementedError
    if __name__ == .__main__.:
    pass
    raise ImportWarning

[flake8]
max-line-length = 100
exclude = .git,__pycache__,.venv_fm313,venv,build,dist
per-file-ignores =
    # Allow print statements in tests
    tests/*.py: T201
    # Allow unused arguments in tests
    tests/*.py: F841
