"""
Base view implementation providing consistent styling and structure for module views.
"""

from PySide6.QtWidgets import <PERSON>Widget, QVBoxLayout, QFrame
from PySide6.QtCore import Qt

class BaseView(QWidget):
    """Base class for module views providing consistent styling and structure."""
    
    def __init__(self, main_window=None):
        """Initialize the base view.
        
        Args:
            main_window: Optional MainWindow instance for panel integration
        """
        super().__init__()
        self.main_window = main_window
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the base user interface structure."""
        self._setup_left_panel()
        self._setup_center_panel()
        
        # If we have a main window, set up the panels
        if self.main_window:
            self.main_window.set_left_panel_content(self.left_panel)
            self.main_window.set_center_panel_content(self.content_area)
    
    def _setup_left_panel(self):
        """Set up the left panel base structure.
        
        Override this in subclasses to add specific content.
        """
        self.left_panel = QFrame()
        self.left_panel.setObjectName("moduleLeftPanel")
        self.left_layout = QVBoxLayout(self.left_panel)
        self.left_layout.setContentsMargins(10, 10, 10, 10)
        self.left_layout.setSpacing(10)
        self._setup_left_panel_content()
    
    def _setup_left_panel_content(self):
        """Set up the content of the left panel.
        
        Override this in subclasses to add specific content.
        """
        pass
    
    def _setup_center_panel(self):
        """Set up the center panel base structure.
        
        Override this in subclasses to add specific content.
        """
        self.content_area = QFrame()
        self.content_area.setObjectName("moduleCenterPanel")
        self.content_layout = QVBoxLayout(self.content_area)
        self.content_layout.setContentsMargins(20, 20, 20, 20)
        self.content_layout.setSpacing(10)
        self._setup_center_panel_content()
    
    def _setup_center_panel_content(self):
        """Set up the content of the center panel.
        
        Override this in subclasses to add specific content.
        """
        pass
    
    def cleanup(self):
        """Clean up before switching away."""
        if self.main_window:
            self.main_window.clear_all_panels()
            self.main_window = None
    
    def setup_in_main_window(self, main_window):
        """Set up view in main window."""
        self.main_window = main_window
        self.setup_ui()  # Re-run setup to place widgets in main window panels
    
    # Optional styling methods that subclasses can override
    def get_left_panel_style(self):
        """Get the style for the left panel.
        
        Override this in subclasses to customize left panel style.
        Returns:
            dict: Style properties for the left panel
        """
        return {
            "background": "transparent",
            "border": "none",
            "min-width": "200px",
            "max-width": "300px"
        }
    
    def get_center_panel_style(self):
        """Get the style for the center panel.
        
        Override this in subclasses to customize center panel style.
        Returns:
            dict: Style properties for the center panel
        """
        return {
            "background": "transparent",
            "border": "none",
            "min-width": "400px"
        }
    
    def apply_custom_style(self):
        """Apply custom styles to the view.
        
        Override this in subclasses to add module-specific styling.
        """
        # Convert style dicts to stylesheet strings
        left_style = "; ".join(f"{k}: {v}" for k, v in self.get_left_panel_style().items())
        center_style = "; ".join(f"{k}: {v}" for k, v in self.get_center_panel_style().items())
        
        self.left_panel.setStyleSheet(f"#moduleLeftPanel {{ {left_style} }}")
        self.content_area.setStyleSheet(f"#moduleCenterPanel {{ {center_style} }}")
