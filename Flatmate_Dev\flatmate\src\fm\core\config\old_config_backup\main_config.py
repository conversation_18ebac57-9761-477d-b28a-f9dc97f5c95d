"""Configuration manager for main application."""

from enum import Enum
from typing import Any

from ..core.services.event_bus import global_event_bus
from .new_config import config as core_config


class MainKeys:
    """Configuration keys for main application."""

    class UI(str, Enum):
        """UI settings"""

        THEME = "ui.theme_profile"
        FONT_SIZE = "ui.font_size"
        STYLE_PATH = "paths.theme"

    class Logging(str, Enum):
        """Logging settings"""

        LEVEL = "logging.level"
        PATH = "paths.logs"

    class Paths(str, Enum):
        """Core paths"""

        DATA = "paths.data"
        LOGS = "paths.logs"
        CONFIG = "paths.config"
        CACHE = "paths.cache"

    class App(str, Enum):
        """App settings"""

        DEV_MODE = "app.dev_mode"

    @classmethod
    def get_defaults(cls) -> dict:
        """Get default values for main settings."""
        return {
            cls.UI.FONT_SIZE: 14,
            cls.Logging.LEVEL: "INFO",
            cls.App.DEV_MODE: False,
        }


class MainConfig:
    """Configuration manager for main application."""

    def __init__(self):
        self.events = global_event_bus
        self._setup_defaults()

    def _setup_defaults(self):
        """Set up default values."""
        defaults = MainKeys.get_defaults()
        for key, value in defaults.items():
            if not core_config.has_value(key):
                core_config.set_value(key, value)

    def get_path(self, key: MainKeys.Paths) -> str:
        """Get path from config."""
        return core_config.get_path(key)

    def get_value(self, key: MainKeys, default: Any = None) -> Any:
        """Get value from config."""
        return core_config.get_value(key, default)

    def set_value(self, key: MainKeys, value: Any):
        """Set value in config."""
        core_config.set_value(key, value)

    def is_development_mode(self) -> bool:
        """Check if app is in development mode."""
        return self.get_value(MainKeys.App.DEV_MODE)


# Global instance
main_config = MainConfig()
