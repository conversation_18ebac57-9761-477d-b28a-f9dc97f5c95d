# Database Configuration Discrepancy Report

**Date:** 2025-07-10  
**Status:** Open  
**Priority:** High  
**Affected Modules:** `update_data`, `categorize`

## Issue Description
There is a discrepancy in database access between the `update_data` and `categorize` modules:
- `update_data` successfully updates the database
- `categorize` reports the database as empty when trying to load transactions

## Investigation Findings

### 1. Database Path Configuration
- **Expected Location:** `~/.flatmate/data/transactions.db`
- **Config Source:** `fm.core.config.config`
- **Implementation:** `SQLiteTransactionRepository` in `fm.core.database.sql_repository.sqlite_repository`

### 2. Code Analysis

#### Update Data Module
- Uses `DBIOService` which initializes `SQLiteTransactionRepository`
- Correctly uses config system to determine database path
- Successfully writes to the database

#### Categorize Module
- Also uses `DBIOService`
- Reports database as empty when loading transactions
- May be using a different database instance or path

### 3. Potential Issues
1. **Config Initialization:** The config system might not be properly initialized before database access
2. **Working Directory:** Different working directories could affect path resolution
3. **Database Permissions:** The application might not have write permissions to the expected location
4. **Multiple Instances:** Multiple database instances might be created with different paths

## Verification Steps

### 1. Check Current Database Location
```bash
# From project root
python -c "from fm.core.config.config import config; print('Database path:', config.get_path('data') / 'transactions.db')"
```

### 2. Verify Database File
```bash
# On Windows
ls -la ~/.flatmate/data/transactions.db

# Or using Python
python -c "import os; print('File exists:', os.path.exists(os.path.expanduser('~/.flatmate/data/transactions.db')))"
```

### 3. Check Database Contents
```bash
# Using Python
python -c "
import sqlite3
from fm.core.config.config import config

db_path = config.get_path('data') / 'transactions.db'
conn = sqlite3.connect(str(db_path))
print('Tables:', conn.execute("SELECT name FROM sqlite_master WHERE type='table'").fetchall())
print('Transaction count:', conn.execute("SELECT COUNT(*) FROM transactions").fetchone()[0])
"
```

## Recommended Solutions

### 1. Standardize Database Access
Ensure all modules use the same method to access the database:

```python
# In any module that needs database access
from fm.core.config.config import config
from fm.core.database.sql_repository.sqlite_repository import SQLiteTransactionRepository
from fm.core.data_services.db_io_service import DBIOService

# Always get database path from config
db_path = str(config.get_path('data') / 'transactions.db')
repo = SQLiteTransactionRepository(db_path=db_path)
db_service = DBIOService(repo=repo)
```

### 2. Add Logging
Add logging to track database operations:

```python
# In SQLiteTransactionRepository.__init__
log(f"Initializing database at: {self.db_path}", level="info")
log(f"Database exists: {self.db_path.exists()}", level="info")
```

### 3. Verify Config Initialization
Ensure the config system is properly initialized before any database access:

```python
# At application startup
from fm.core.config.config import config
print(f"Using database: {config.get_path('data') / 'transactions.db'}")
```

## Next Steps
1. [ ] Verify the database file exists in the expected location
2. [ ] Check application logs for any database-related errors
3. [ ] Add debug logging to track database access in both modules
4. [ ] Test with a clean database to rule out corruption issues
5. [ ] Consider implementing a database migration system if schema changes are needed

## Related Files
- `fm/core/config/config.py` - Configuration system
- `fm/core/database/sql_repository/sqlite_repository.py` - Database implementation
- `fm/core/data_services/db_io_service.py` - High-level database access
- `fm/modules/update_data/` - Update data module
- `fm/modules/categorize/` - Categorize module
