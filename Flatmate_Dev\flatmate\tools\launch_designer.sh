#!/bin/bash

# Get the path to PySide6
PYSIDE6_PATH=$(python -c "import PySide6; print(PySide6.__path__[0])")

# Create and set the designer plugins path
DESIGNER_PLUGINS_PATH="${PYSIDE6_PATH}/plugins/designer"
mkdir -p "${DESIGNER_PLUGINS_PATH}"
export PYSIDE_DESIGNER_PLUGINS="${DESIGNER_PLUGINS_PATH}"

# Also try the Qt-specific plugin path
QT_DESIGNER_PLUGINS="${PYSIDE6_PATH}/Qt/plugins/designer"
mkdir -p "${QT_DESIGNER_PLUGINS}"
export QT_DESIGNER_PATH="${QT_DESIGNER_PLUGINS}"

# Set additional Qt paths
export QTWEBENGINEPROCESS_PATH="${PYSIDE6_PATH}/Qt/lib/QtWebEngineCore.framework/Helpers/QtWebEngineProcess.app/Contents/MacOS/QtWebEngineProcess"
export QTWEBENGINE_DICTIONARIES_PATH="${PYSIDE6_PATH}/Qt/lib/QtWebEngineCore.framework/Resources/qtwebengine_dictionaries"

# Set Qt plugin path
export QT_PLUGIN_PATH="${PYSIDE6_PATH}/Qt/plugins"

# Debug info
echo "PYSIDE6_PATH: ${PYSIDE6_PATH}"
echo "DESIGNER_PLUGINS_PATH: ${DESIGNER_PLUGINS_PATH}"
echo "QT_DESIGNER_PLUGINS: ${QT_DESIGNER_PLUGINS}"

# Launch designer with proper environment
pyside6-designer "$@"
