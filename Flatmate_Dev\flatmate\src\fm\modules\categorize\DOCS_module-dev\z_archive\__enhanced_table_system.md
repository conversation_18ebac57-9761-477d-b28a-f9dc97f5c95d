# Enhanced Table System Documentation

*Last Updated: 2025-06-18*

## 1. Overview

The Enhanced Table System provides a flexible, feature-rich way to display and interact with tabular data throughout the Flatmate application. It consists of two primary components:

1. **EnhancedTableView**: A specialized `QTableView` with advanced features for data display
2. **EnhancedTableWidget**: A container widget that wraps `EnhancedTableView` and adds UI controls

This system is designed to handle transaction data with consistent column naming, formatting, and interaction patterns.

## 2. Architecture

### 2.1 Component Hierarchy

```
EnhancedTableWidget (QWidget)
├── FilterControls (QWidget)
│   ├── ColumnComboBox (QComboBox)
│   ├── FilterInput (QLineEdit)
│   ├── ApplyFilterButton (QPushButton)
│   └── ClearFilterButton (QPushButton)
├── EnhancedTableView (QTableView)
│   ├── EnhancedTableModel (QStandardItemModel)
│   └── EnhancedFilterProxyModel (QSortFilterProxyModel)
└── ActionButtons (QWidget)
    ├── ColumnVisibilityButton (QPushButton)
    └── ExportButton (QPushButton)
```

### 2.2 File Structure

The Enhanced Table System is implemented in the following files:

```
fm/gui/widgets/
└── enhanced_table_view.py  # Contains both EnhancedTableView and EnhancedTableWidget classes
```

### 2.3 Data Flow

1. Data is loaded as a pandas DataFrame
2. DataFrame is set in the `EnhancedTableModel`
3. `EnhancedFilterProxyModel` sits between the model and view for filtering/sorting
4. User interactions with the view are emitted as signals
5. Changes to the data are tracked in the model and can be retrieved as a DataFrame

## 3. Key Features

### 3.1 EnhancedTableView

- **Column Management**:
  - Resizable columns with memory of widths
  - Sortable columns (click header)
  - Movable columns (drag header)
  - Column visibility toggle

- **Data Interaction**:
  - Row selection (single or multi)
  - Cell editing for designated columns
  - Context menu with copy/export options
  - Row highlighting

- **Filtering**:
  - Per-column filtering
  - Global search

- **Export**:
  - CSV export
  - Excel export

### 3.2 EnhancedTableWidget

- **Filter Controls**:
  - Column selector dropdown
  - Filter input field
  - Apply/Clear filter buttons

- **Action Buttons**:
  - Column visibility menu
  - Export options menu

## 4. Usage

### 4.1 Basic Setup

```python
# Create the widget
table_widget = EnhancedTableWidget()

# Set data
df = pd.DataFrame(...)
table_widget.set_dataframe(df)

# Configure display columns
display_columns = ['date', 'description', 'amount', 'account', 'tags']
column_names = {'date': 'Date', 'description': 'Description', ...}
table_widget.set_display_columns(display_columns, column_names)

# Set editable columns
table_widget.set_editable_columns(['tags'])

# Set column widths
width_map = {'Date': 12, 'Description': 40, 'Amount': 12, ...}
table_widget.set_column_widths(width_map)

# Connect signals
table_widget.table_view.row_selected.connect(handle_row_selected)
table_widget.table_view.cell_edited.connect(handle_cell_edited)
```

### 4.2 Column Configuration

The system uses `StandardColumns` enum (defined in `fm/core/standards/fm_standard_columns.py`) for consistent column naming:

```python
# Define display columns in specific order
display_columns = [
    StandardColumns.DATE.db_name,
    StandardColumns.DETAILS.db_name,
    StandardColumns.AMOUNT.db_name,
    StandardColumns.ACCOUNT.db_name,
    'tags',
    'category'
]

# Map database column names to display names
column_names = {
    StandardColumns.DATE.db_name: StandardColumns.DATE.value,
    StandardColumns.DETAILS.db_name: StandardColumns.DETAILS.value,
    StandardColumns.AMOUNT.db_name: StandardColumns.AMOUNT.value,
    StandardColumns.ACCOUNT.db_name: StandardColumns.ACCOUNT.value,
    'tags': 'Tags',
    'category': 'Category'
}
```

### 4.3 Real-World Example

Here's how the table is used in the Categorize module (`fm/modules/categorize/_view/center_panel/center_panel.py`):

```python
def set_transactions(self, df: pd.DataFrame):
    """Set the transactions dataframe to display."""
    logger.debug(f"Setting transactions: {len(df) if df is not None else 0} rows")
    if df is not None and not df.empty:
        # Make a copy to avoid modifying the original
        df_copy = df.copy()
        
        # Set the dataframe in the enhanced table widget
        self.transaction_table.set_dataframe(df_copy)
        
        # Set display columns in a specific order using StandardColumns
        display_columns = [
            StandardColumns.DATE.db_name,
            StandardColumns.DETAILS.db_name,
            StandardColumns.AMOUNT.db_name,
            StandardColumns.ACCOUNT.db_name,
            'tags',
            'category'
        ]
        valid_columns = [col for col in display_columns if col in df_copy.columns]

        # Create column display names mapping
        column_names = {
            StandardColumns.DATE.db_name: StandardColumns.DATE.value,
            StandardColumns.DETAILS.db_name: StandardColumns.DETAILS.value,
            StandardColumns.AMOUNT.db_name: StandardColumns.AMOUNT.value,
            StandardColumns.ACCOUNT.db_name: StandardColumns.ACCOUNT.value,
            'tags': 'Tags',
            'category': 'Category'
        }
        
        # Set display columns
        self.transaction_table.set_display_columns(valid_columns, column_names)
        
        # Set the tags column as editable
        self.transaction_table.set_editable_columns(['tags'])
```

## 5. Implementation Details

### 5.1 EnhancedTableModel

Defined in `fm/gui/widgets/enhanced_table_view.py`:

```python
class EnhancedTableModel(QStandardItemModel):
    """Enhanced table model with additional features for data handling."""
    
    def __init__(self, parent=None):
        """Initialize the enhanced table model."""
        super().__init__(parent)
        self._editable_columns = set()
        self._column_types = {}
        self._original_data = None
        self._display_columns = None
    
    def set_editable_columns(self, columns: List[int]):
        """Set which columns should be editable."""
        self._editable_columns = set(columns)
    
    def flags(self, index):
        """Return item flags based on column editability."""
        flags = super().flags(index)
        if index.column() in self._editable_columns:
            return flags | Qt.ItemIsEditable
        return flags & ~Qt.ItemIsEditable
    
    def set_dataframe(self, df: pd.DataFrame):
        """Set the model data from a pandas DataFrame."""
        self._original_data = df.copy()
        
        # Clear existing data
        self.clear()
        
        # Set headers
        self.setHorizontalHeaderLabels([str(col) for col in df.columns])
        
        # Add data rows
        for row_idx, row in df.iterrows():
            items = []
            for col_idx, value in enumerate(row):
                item = QStandardItem(str(value))
                # Store original value as user data for sorting
                item.setData(value, Qt.UserRole)
                items.append(item)
            self.appendRow(items)
```

### 5.2 EnhancedFilterProxyModel

Defined in `fm/gui/widgets/enhanced_table_view.py`:

```python
class EnhancedFilterProxyModel(QSortFilterProxyModel):
    """Enhanced filter proxy model with per-column filtering."""
    
    def __init__(self, parent=None):
        """Initialize the enhanced filter proxy model."""
        super().__init__(parent)
        self._column_filters = {}
    
    def set_column_filter(self, column: int, pattern: str):
        """Set filter for a specific column."""
        if not pattern:
            if column in self._column_filters:
                del self._column_filters[column]
        else:
            self._column_filters[column] = pattern
        self.invalidateFilter()
    
    def filterAcceptsRow(self, source_row, source_parent):
        """Check if row matches all column filters."""
        model = self.sourceModel()
        
        # If no filters, accept all rows
        if not self._column_filters:
            return True
        
        # Check each column filter
        for column, pattern in self._column_filters.items():
            index = model.index(source_row, column, source_parent)
            if not index.isValid():
                continue
                
            data = model.data(index, Qt.DisplayRole)
            if data is None:
                continue
                
            if pattern.lower() not in str(data).lower():
                return False
        
        return True
```

### 5.3 EnhancedTableView

Key methods from `fm/gui/widgets/enhanced_table_view.py`:

```python
def set_dataframe(self, df: pd.DataFrame):
    """Set the table data from a pandas DataFrame."""
    self._model.set_dataframe(df)
    
    # Auto-resize columns to contents initially
    self.resizeColumnsToContents()
    
    # Restore saved column widths if available
    header = self.horizontalHeader()
    for col, width in self._column_widths.items():
        if col < self._model.columnCount():
            header.resizeSection(col, width)

def set_column_widths(self, width_map: Dict[str, int]):
    """Set custom column widths.
    
    Args:
        width_map: Dictionary mapping column names to widths (in characters)
    """
    if not width_map:
        return
        
    header = self.horizontalHeader()
    char_width = self.fontMetrics().averageCharWidth()
    
    for col_idx in range(self._model.columnCount()):
        # Get column name
        col_name = self._model.headerData(col_idx, Qt.Horizontal)
        
        # Apply width if in the width map
        if col_name in width_map:
            pixel_width = width_map[col_name] * char_width
            header.resizeSection(col_idx, pixel_width)
```

### 5.4 Signal Handling

The system emits the following signals (defined in `fm/gui/widgets/enhanced_table_view.py`):
- `row_selected(int)`: When a row is selected
- `cell_edited(int, int, str)`: When a cell is edited

```python
class EnhancedTableView(QTableView):
    # Signals
    row_selected = Signal(int)  # Emits row index when selected
    cell_edited = Signal(int, int, str)  # Emits row, col, new value when edited
    
    def _on_selection_changed(self, selected, deselected):
        """Handle selection changes."""
        indices = self.selectionModel().selectedRows()
        if indices:
            # Get the source model row index (not proxy index)
            source_index = self._proxy_model.mapToSource(indices[0])
            self.row_selected.emit(source_index.row())
    
    def _on_item_changed(self, item):
        """Handle item changes."""
        row = item.row()
        col = item.column()
        value = item.text()
        self.cell_edited.emit(row, col, value)
```

## 6. Design Considerations and Future Improvements

### 6.1 Current Limitations

- New columns like 'tags' and 'category' are not part of the `StandardColumns` enum
- Column order is manually specified rather than derived from a standard source
- Column display preferences are not persisted between sessions

### 6.2 Recommended Improvements

1. **Standardize All Columns**:
   - Extend `StandardColumns` enum in `fm/core/standards/fm_standard_columns.py` to include all columns including 'tags' and 'category'
   - Consider moving to a dataclass approach for more flexibility

```python
# Current StandardColumns enum (partial)
class StandardColumns(Enum):
    DATE = "Date"
    DETAILS = "Description"
    AMOUNT = "Amount"
    ACCOUNT = "Account"
    
    # Proposed additions
    TAGS = "Tags"
    CATEGORY = "Category"
    NOTES = "Notes"
    
    # Each enum should have properties
    @property
    def db_name(self):
        """Return the database column name."""
        return self.name.lower()
```

2. **Default Column Order**:
   - The default column order should be derived from the `StandardColumns` enum
   - Database queries should return data in this standard order

```python
# Example of deriving column order from enum
def get_default_column_order():
    """Get the default column order from StandardColumns enum."""
    return [
        StandardColumns.DATE,
        StandardColumns.DETAILS,
        StandardColumns.AMOUNT,
        StandardColumns.ACCOUNT,
        StandardColumns.TAGS,
        StandardColumns.CATEGORY
    ]
```

3. **Configuration Management**:
   - Store user preferences for column visibility and order
   - Persist these settings between sessions

```python
# Example of configuration keys for column preferences
class CatKeys:
    class Display(str, Enum):
        COLUMN_ORDER = 'categorize.display.column_order'
        VISIBLE_COLUMNS = 'categorize.display.visible_columns'
        COLUMN_WIDTHS = 'categorize.display.column_widths'
```

4. **Column Selection**:
   - Implement a system to declare which columns should be displayed by default
   - Allow modules to override these defaults based on their specific needs

```python
# Example of module-specific column configuration
DEFAULT_COLUMNS = {
    'categorize': [
        StandardColumns.DATE,
        StandardColumns.DETAILS,
        StandardColumns.AMOUNT,
        StandardColumns.ACCOUNT,
        StandardColumns.TAGS,
        StandardColumns.CATEGORY
    ],
    'update_data': [
        StandardColumns.DATE,
        StandardColumns.DETAILS,
        StandardColumns.AMOUNT,
        StandardColumns.ACCOUNT
    ]
}
```

5. **Data Integration**:
   - Ensure new columns are properly integrated with the database schema
   - Maintain backward compatibility with existing data

```python
# Example migration script to add new columns
def add_tags_column():
    """Add tags column to transactions table."""
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute("ALTER TABLE transactions ADD COLUMN tags TEXT DEFAULT ''")
        conn.commit()
```

### 6.3 Implementation Plan

1. Extend `StandardColumns` enum with new columns
2. Update database schema and migration scripts
3. Modify `DataService` to handle new columns consistently
4. Update `EnhancedTableView` to derive default column order from enum
5. Implement configuration storage for user preferences

## 7. Conclusion

The Enhanced Table System provides a robust foundation for displaying and interacting with tabular data in Flatmate. With the recommended improvements, it can become even more standardized and maintainable, ensuring consistent user experience across all modules.

---

*Document created by Flatmate Development Team*
